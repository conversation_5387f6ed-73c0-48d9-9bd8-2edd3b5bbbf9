/* eslint-disable @typescript-eslint/naming-convention */
import {injectable, BindingScope, inject, service} from '@loopback/core';
import {Campaign} from '../models';
import {
  INotificationFilterFunc,
  NotifServiceBindings,
} from '@sourceloop/notification-service';
import {repository} from '@loopback/repository';
import {CampaignRepository, GroupsRepository} from '../repositories';
import {HttpErrors} from '@loopback/rest';
import {FCMBindings} from '../keys';
import {FCMConfig} from '../types';
import * as admin from 'firebase-admin';
import axios from 'axios';
import {ZohoTokenService} from './zoho-token.service';

type FCMAppsMap = Record<string, admin.messaging.Messaging>;

@injectable({scope: BindingScope.TRANSIENT})
export class CampaignService {
  private fcmClients: FCMAppsMap = {};

  constructor(
    @inject(NotifServiceBindings.NotificationFilter)
    private readonly filterNotification: INotificationFilterFunc,
    @repository(CampaignRepository)
    public campaignRepository: CampaignRepository,
    @repository(GroupsRepository)
    public groupsRepository: GroupsRepository,
    @inject(FCMBindings.Config, {optional: false})
    private readonly fcmConfigs: FCMConfig[],
    @service(ZohoTokenService)
    private readonly zohoTokenService: ZohoTokenService,
  ) {
    if (!fcmConfigs || fcmConfigs.length === 0) {
      throw new HttpErrors.PreconditionFailed('FCM configs are missing!');
    }

    for (const config of fcmConfigs) {
      const appName = config.projectId;

      try {
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        admin.app(appName).delete();
      } catch (e) {
        // App might not exist yet – ignore
      }

      const app = admin.initializeApp(
        {
          credential: admin.credential.cert({
            projectId: config.projectId,
            clientEmail: config.clientEmail,
            privateKey: config.privateKey.replace(/\\n/g, '\n'),
          }),
        },
        appName,
      );

      this.fcmClients[appName] = app.messaging();
    }
  }

  async createCampaign(campaign: Campaign): Promise<Campaign> {
    const existingCampaigns = await this.campaignRepository.find({
      where: {name: campaign.name},
      limit: 1,
    });

    if (existingCampaigns.length > 0) {
      throw new HttpErrors.BadRequest(
        `Campaign with the name "${campaign.name}" already exists.`,
      );
    }

    // Validate that the group exists and get group details
    const group = await this.groupsRepository.findById(campaign.groupId);

    if (campaign.type === 0) {
      campaign.isDraft = true; // FCM campaigns start as draft
    } else if (campaign.type === 1) {
      try {
        const accessToken = await this.zohoTokenService.getValidAccessToken();

        const listDetails = {
          [group.listKey]: [],
        };

        const payload = new URLSearchParams({
          resfmt: 'json',
          campaignname: campaign.name,
          from_email: process.env.ZOHO_FROM_EMAIL ?? '',
          subject: campaign.subject,
          list_details: JSON.stringify(listDetails),
          topicId: group.topicId,
          content_url: campaign.body ?? '',
        }).toString();

        const createZohoCampaignResponse = await axios.post(
          `${process.env.ZOHO_API_BASE_URL}/createCampaign`,
          payload,
          {
            headers: {
              Authorization: `Zoho-oauthtoken ${accessToken}`,
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        );

        campaign.campaignKey = createZohoCampaignResponse.data?.campaignKey;
        campaign.campaignStatus =
          createZohoCampaignResponse.data?.campaign_status;
        campaign.isDraft = true;
      } catch (err) {
        throw new HttpErrors.InternalServerError(
          'Zoho List Creation failed: ' + (err.response?.data || err.message),
        );
      }
    }

    const createdCampaign = await this.campaignRepository.create(campaign);
    return createdCampaign;
  }

  async sendCampaign(campaignId: string): Promise<Campaign> {
    try {
      if (!campaignId) {
        throw new HttpErrors.BadRequest('Campaign ID is missing');
      }

      // Get the campaign details
      const campaign = await this.campaignRepository.findById(campaignId);
      if (!campaign) {
        throw new HttpErrors.NotFound('Campaign not found');
      }

      // Get the group details
      const group = await this.groupsRepository.findById(campaign.groupId);

      if (campaign.type === 0) {
        // FCM Campaign Sending Logic
        await this.sendFCMMessage(campaign, group);

        // Update campaign status to sent
        await this.campaignRepository.updateById(campaign.id, {
          isDraft: false,
        });

        return campaign;
      } else if (campaign.type === 1) {
        // Zoho Campaign Sending Logic
        if (!campaign.campaignKey) {
          throw new HttpErrors.BadRequest(
            'Campaign key is missing for Zoho campaign',
          );
        }

        const response = await this.sendZohoCampaign(campaign.campaignKey);

        // Update campaign status to sent
        await this.campaignRepository.updateById(campaign.id, {
          isDraft: false,
        });

        return response;
      } else {
        throw new HttpErrors.BadRequest('Invalid campaign type');
      }
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Failed to send campaign: ' + (err.response?.data || err.message),
      );
    }
  }

  /**
   * Send campaign by campaign key (for backward compatibility with Zoho campaigns)
   */
  async sendCampaignByKey(campaignKey: string | undefined): Promise<Campaign> {
    try {
      if (!campaignKey) {
        throw new HttpErrors.BadRequest('Campaign key is missing');
      }

      // Find campaign by campaignKey
      const [campaign] = await this.campaignRepository.find({
        where: {campaignKey},
        limit: 1,
      });

      if (!campaign) {
        throw new HttpErrors.NotFound('Campaign not found');
      }

      // Use the main sendCampaign method
      return await this.sendCampaign(campaign.id!);
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Failed to send campaign by key: ' +
          (err.response?.data || err.message),
      );
    }
  }

  /**
   * Send FCM message for a specific campaign
   */
  private async sendFCMMessage(
    campaign: Campaign,
    group: any,
  ): Promise<string> {
    const message = {
      topic: group.name,
      notification: {
        title: campaign.subject,
        body: campaign.body,
      },
      data: {
        click_action: campaign.clickAction ?? '',
      },
    };

    const [config] = this.fcmConfigs;
    const fcmClient = this.fcmClients[config.projectId];

    if (!fcmClient) {
      throw new HttpErrors.InternalServerError(
        'No FCM client configured for this group',
      );
    }

    const response = await fcmClient.send(message);
    console.log('✅ Successfully sent FCM message:', response);
    return response;
  }

  /**
   * Send Zoho campaign
   */
  private async sendZohoCampaign(campaignKey: string): Promise<any> {
    const accessToken = await this.zohoTokenService.getValidAccessToken();

    const response = await axios.post(
      `${process.env.ZOHO_API_BASE_URL}/sendcampaign?resfmt=JSON&campaignkey=${campaignKey}`,
      null,
      {
        headers: {
          Authorization: `Zoho-oauthtoken ${accessToken}`,
        },
      },
    );

    return response.data;
  }

  async getCampaignDetails(campaignKey: string | undefined): Promise<Campaign> {
    try {
      if (!campaignKey) {
        throw new HttpErrors.BadRequest('Campaign key is missing');
      }

      const accessToken = await this.zohoTokenService.getValidAccessToken();

      const url = `${process.env.ZOHO_API_BASE_URL}/getcampaigndetails?resfmt=JSON&campaignkey=${campaignKey}&campaigntype=normal`;

      const response = await axios.get(url, {
        headers: {
          Authorization: `Zoho-oauthtoken ${accessToken}`,
        },
      });

      return response.data;
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Failed to fetch campaign details: ' +
          (err.response?.data || err.message),
      );
    }
  }

  async deleteCampaign(
    campaignKey: string | undefined,
  ): Promise<{status: string; message: string}> {
    try {
      if (!campaignKey) {
        throw new HttpErrors.BadRequest('Campaign key is missing');
      }

      const accessToken = await this.zohoTokenService.getValidAccessToken();

      const url = `https://campaigns.zoho.com/api/v1.1/deletecampaign?campaignkey=${encodeURIComponent(
        campaignKey,
      )}`;

      const response = await axios.get(url, {
        headers: {
          Authorization: `Zoho-oauthtoken ${accessToken}`,
        },
      });

      const data = response.data;

      if (data.code !== '200') {
        throw new Error(
          data.message || 'Zoho returned an error while deleting campaign',
        );
      }

      return {
        status: 'success',
        message: data.message,
      };
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Failed to delete campaign: ' + (err.response?.data || err.message),
      );
    }
  }
}
