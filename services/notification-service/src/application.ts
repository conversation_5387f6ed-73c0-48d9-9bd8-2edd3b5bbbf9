import {BootMixin} from '@loopback/boot';
import {ApplicationConfig} from '@loopback/core';
import {
  RestExplorerBindings,
  RestExplorerComponent,
} from '@loopback/rest-explorer';
import * as dotenv from 'dotenv';
import * as dotenvExt from 'dotenv-extended';
import {AuthenticationComponent} from 'loopback4-authentication';
import {
  AuthorizationBindings,
  AuthorizationComponent,
} from 'loopback4-authorization';
import {
  ServiceSequence,
  SFCoreBindings,
  BearerVerifierBindings,
  BearerVerifierComponent,
  BearerVerifierConfig,
  BearerVerifierType,
  SECURITY_SCHEME_SPEC,
} from '@sourceloop/core';
import {NotificationServiceComponent} from '@sourceloop/notification-service';
import {NotifServiceBindings} from '@sourceloop/notification-service';
import {RepositoryMixin} from '@loopback/repository';
import {RestApplication} from '@loopback/rest';
import {ServiceMixin} from '@loopback/service-proxy';
import path from 'path';
import * as openapi from './openapi.json';
import {FCMBindings, Msg91Bindings, ZeptoBindings} from './keys';
import {NotificationBindings} from 'loopback4-notifications';
import {Msg91Provider, ZeptpMailProvider, FcmProvider} from './providers';

export {ApplicationConfig};

export class NotificationServiceApplication extends BootMixin(
  ServiceMixin(RepositoryMixin(RestApplication)),
) {
  constructor(options: ApplicationConfig = {}) {
    const port = 3000;
    dotenv.config();
    dotenvExt.load({
      schema: '.env.example',
      errorOnMissing: process.env.NODE_ENV !== 'test',
      includeProcessEnv: true,
    });
    options.rest = options.rest ?? {};
    options.rest.basePath = process.env.BASE_PATH ?? '';
    options.rest.port = +(process.env.PORT ?? port);
    options.rest.host = process.env.HOST;
    options.rest.openApiSpec = {
      endpointMapping: {
        [`${options.rest.basePath}/openapi.json`]: {
          version: '3.0.0',
          format: 'json',
        },
      },
    };

    super(options);

    // To check if monitoring is enabled from env or not
    const enableObf = !!+(process.env.ENABLE_OBF ?? 0);
    // To check if authorization is enabled for swagger stats or not
    const authentication =
      process.env.SWAGGER_USER && process.env.SWAGGER_PASSWORD ? true : false;
    const obj = {
      enableObf,
      obfPath: process.env.OBF_PATH ?? '/obf',
      openapiSpec: openapi,
      authentication: authentication,
      swaggerUsername: process.env.SWAGGER_USER,
      swaggerPassword: process.env.SWAGGER_PASSWORD,
    };
    this.bind(SFCoreBindings.config).to(obj);

    // Set up the custom sequence
    this.sequence(ServiceSequence);

    // Add authentication component
    this.component(AuthenticationComponent);

    this.bind(NotifServiceBindings.Config).to({
      useCustomSequence: true,
      useSequelize: true,
    });
    this.component(NotificationServiceComponent);

    this.bind(ZeptoBindings.Config).to({
      url: process.env.ZEPTO_MAIL_URL as string,
      token: process.env.ZEPTO_MAIL_TOKEN as string,
    });
    this.bind(Msg91Bindings.Config).to({
      baseUrl: process.env.MSG91_BASE_URL as string,
      otpTemplateId: process.env.MSG91_OTP_TEMPLATE_ID as string,
      generalTemplateId: process.env.MSG91_GENERAL_TEMPLATE_ID as string,
      authKey: process.env.MSG91_AUTH_KEY as string,
    });

    this.bind(FCMBindings.Config).to([
      {
        projectId: process.env.FCM_PROJECT_ID!,
        clientEmail: process.env.FCM_CLIENT_EMAIL!,
        privateKey: process.env.FCM_PRIVATE_KEY!,
      },
    ]);

    this.bind(NotificationBindings.EmailProvider).toProvider(ZeptpMailProvider);
    this.bind(NotificationBindings.SMSProvider).toProvider(Msg91Provider);
    this.bind(NotificationBindings.PushProvider).toProvider(FcmProvider);

    this.bind(NotificationBindings.Config).to({
      senderEmail: process.env.ZEPTO_FROM_MAIL as string,
      sendToMultipleReceivers: true,
    });
    // Add bearer verifier component
    this.bind(BearerVerifierBindings.Config).to({
      type: BearerVerifierType.service,
      useSymmetricEncryption: true,
    } as BearerVerifierConfig);
    this.component(BearerVerifierComponent);

    // Add authorization component
    this.bind(AuthorizationBindings.CONFIG).to({
      allowAlwaysPaths: ['/explorer', '/openapi.json'],
    });
    this.component(AuthorizationComponent);

    // Set up default home page
    this.static('/', path.join(__dirname, '../public'));

    // Customize @loopback/rest-explorer configuration here
    this.configure(RestExplorerBindings.COMPONENT).to({
      path: '/explorer',
    });

    this.component(RestExplorerComponent);

    this.projectRoot = __dirname;
    // Customize @loopback/boot Booter Conventions here
    this.bootOptions = {
      controllers: {
        // Customize ControllerBooter Conventions here
        dirs: ['controllers'],
        extensions: ['.controller.js'],
        nested: true,
      },
    };

    this.api({
      openapi: '3.0.0',
      info: {
        title: 'notification-service',
        version: '1.0.0',
      },
      paths: {},
      components: {
        securitySchemes: SECURITY_SCHEME_SPEC,
      },
      servers: [{url: '/'}],
    });
  }
}
