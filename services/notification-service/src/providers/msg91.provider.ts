/* eslint-disable @typescript-eslint/naming-convention */
import {inject, Provider} from '@loopback/core';
import {HttpErrors} from '@loopback/rest';
import {
  Msg91Config,
  Msg91Message,
  Msg91Notification,
  Msg91SMSType,
} from '../types';
import {Msg91Bindings} from '../keys';
import axios, {AxiosInstance} from 'axios';
import {MessageType} from 'loopback4-notifications';
const OTP_EXPIRY = 5;
export class Msg91Provider implements Provider<Msg91Notification> {
  constructor(
    @inject(Msg91Bindings.Config, {
      optional: true,
    })
    private readonly msg91Config?: Msg91Config,
  ) {
    if (this.msg91Config) {
      this.msg91Client = axios.create({
        baseURL: msg91Config?.baseUrl,
        headers: {'Content-Type': 'application/json'},
      });
    } else {
      throw new HttpErrors.PreconditionFailed('Msg91  Config missing !');
    }
  }

  msg91Client: AxiosInstance;

  value() {
    return {
      publish: async (message: Msg91Message) => {
        if (message.receiver.to.length === 0) {
          throw new HttpErrors.BadRequest(
            'Message receiver not found in request',
          );
        }

        const publishes = message.receiver.to.map(receiver => {
          if (message.smsType === Msg91SMSType.Otp) {
            const params = {
              otp_expiry: OTP_EXPIRY, // 5 minutes
              template_id: this.msg91Config?.otpTemplateId,
              mobile: receiver.id,
              authkey: this.msg91Config?.authKey,
              otp: message.body,
            };
            return this.msg91Client.post(
              '/otp',
              {name: receiver.name},
              {params},
            );
          } else if (message.type === MessageType.SMS) {
            const params = {
              template_id: this.msg91Config?.generalTemplateId,
              mobile: receiver.id,
              authkey: this.msg91Config?.authKey,
              message: message.body,
              companyname: 'EcomDukes',
            };
            return this.msg91Client.post(
              '/otp',
              {name: receiver.name},
              {params},
            );
          } else {
            // do nothing
          }
        });

        const response = await Promise.all(publishes).catch(err => {
          console.log(err);
          throw new HttpErrors.BadRequest(err);
        });
      },
    };
  }
}
