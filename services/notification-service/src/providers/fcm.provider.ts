import {inject, Provider} from '@loopback/core';
import {HttpErrors} from '@loopback/rest';
import * as admin from 'firebase-admin';
import {FCMConfig, FCMMessage, FCMNotification} from '../types';

import {FCMBindings} from '../keys';

type FCMAppsMap = Record<string, admin.messaging.Messaging>;

export class FcmProvider implements Provider<FCMNotification> {
  private fcmClients: FCMAppsMap = {};
  constructor(
    @inject(FCMBindings.Config, {optional: false})
    private readonly fcmConfigs: FCMConfig[],
  ) {
    if (!fcmConfigs || fcmConfigs.length === 0) {
      throw new HttpErrors.PreconditionFailed('FCM configs are missing!');
    }

    for (const config of fcmConfigs) {
      const appName = config.projectId;

      if (!admin.apps.find(app => app?.name === appName)) {
        const app = admin.initializeApp(
          {
            credential: admin.credential.cert({
              projectId: config.projectId,
              clientEmail: config.clientEmail,
              privateKey: config.privateKey.replace(/\\n/g, '\n'),
            }),
          },
          appName,
        );

        this.fcmClients[appName] = app.messaging();
      } else {
        const existingApp = admin.app(appName);
        this.fcmClients[appName] = existingApp.messaging();
      }
    }
  }

  value(): FCMNotification {
    return {
      publish: async (message: FCMMessage) => {
        const [config] = this.fcmConfigs;

        const fcmClient = this.fcmClients[config.projectId];

        if (!fcmClient) {
          throw new HttpErrors.InternalServerError(
            'No FCM client configured for this group',
          );
        }

        if (message.receiver.to.length > 0) {
          try {
            await fcmClient.send({
              token: message.receiver.to[0].id,
              notification: message.notification,
              data: message.data,
            });
            console.log('push notifications sent');
          } catch (error) {
            console.log('push notifications error', error);
          }
        } else {
          throw new HttpErrors.BadRequest('No receiver or topic provided');
        }
      },
    };
  }
}
