import {BindingScope, injectable} from '@loopback/core';
import {
  SellerAdvancedShippingCharge,
  SellerBasicShippingCharge,
  SellerProductVariantShippingCharge,
  SellerShippingProfile,
} from '../models';
import {SellerShippingProfileDto} from '../models/dto/seller-shipping-profile.dto';
import {
  SellerAdvancedShippingChargeRepository,
  SellerBasicShippingChargeRepository,
  SellerProductVariantShippingChargeRepository,
  SellerShippingProfileRepository,
  ShippingMethodRepository,
} from '../repositories';
import {HttpErrors} from '@loopback/rest';
import {Transaction} from '@loopback/sequelize';
import {FilterExcludingWhere, repository} from '@loopback/repository';

@injectable({scope: BindingScope.TRANSIENT})
export class SellerShippingService {
  constructor(
    @repository(SellerShippingProfileRepository)
    private sellerShippingProfileRepository: SellerShippingProfileRepository,
    @repository(SellerBasicShippingChargeRepository)
    private sellerBasicShippingChargeRepository: SellerBasicShippingChargeRepository,
    @repository(SellerAdvancedShippingChargeRepository)
    private sellerAdvancedShippingChargeRepository: SellerAdvancedShippingChargeRepository,
    @repository(SellerProductVariantShippingChargeRepository)
    private sellerProductVariantShippingChargeRepository: SellerProductVariantShippingChargeRepository,
    @repository(ShippingMethodRepository)
    private shippingMethodRepository: ShippingMethodRepository,
  ) {}

  /**
   * Create a shipping profile with associated charges and rules
   */
  async createShippingProfile(
    profileDto: SellerShippingProfileDto,
  ): Promise<SellerShippingProfile> {
    const {
      basicShippingCharges,
      advancedShippingCharges,
      productVariantShippingCharges,
      ...rest
    } = profileDto;
    // Validate shipping method exists
    const shippingMethod = await this.shippingMethodRepository.findById(
      profileDto.shippingMethodId,
    );

    if (!shippingMethod) {
      throw new HttpErrors.NotFound('Shipping method not found');
    }

    // Check if seller already has a profile with this shipping method type
    const existingProfile = await this.sellerShippingProfileRepository.findOne({
      where: {
        sellerId: profileDto.sellerId,
        shippingMethodId: profileDto.shippingMethodId,
      },
    });

    if (existingProfile) {
      throw new HttpErrors.Conflict(
        'Seller already has a shipping profile with this shipping method',
      );
    }
    const transaction =
      await this.sellerShippingProfileRepository.dataSource.beginTransaction({
        isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,
      });
    try {
      const profile = await this.sellerShippingProfileRepository.create(
        new SellerShippingProfile(rest),
        {transaction},
      );
      // Create shipping charges if provided
      if (basicShippingCharges?.length) {
        const chargePromises = basicShippingCharges.map(charge =>
          this.sellerBasicShippingChargeRepository.create(
            new SellerBasicShippingCharge({
              ...charge,
              shippingProfileId: profile.id,
            }),
            {transaction},
          ),
        );
        await Promise.all(chargePromises);
      }

      // Create weight-based rules if provided
      if (advancedShippingCharges?.length) {
        const rulePromises = advancedShippingCharges.map(rule =>
          this.sellerAdvancedShippingChargeRepository.create(
            new SellerAdvancedShippingCharge({
              ...rule,
              shippingProfileId: profile.id,
            }),
            {transaction},
          ),
        );
        await Promise.all(rulePromises);
      }

      // Create product-specific shipping charges if provided
      if (productVariantShippingCharges?.length) {
        const productChargePromises = productVariantShippingCharges.map(
          charge =>
            this.sellerProductVariantShippingChargeRepository.create(
              new SellerProductVariantShippingCharge({
                ...charge,
                shippingProfileId: profile.id,
              }),
              {transaction},
            ),
        );
        await Promise.all(productChargePromises);
      }

      await transaction.commit();
      // Return the created profile with relations
      return profile;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Update a shipping profile with associated charges and rules
   */
  async updateShippingProfile(
    id: string,
    profileDto: Partial<SellerShippingProfileDto>,
  ): Promise<void> {
    const {
      basicShippingCharges,
      advancedShippingCharges,
      productVariantShippingCharges,
      ...profile
    } = profileDto;
    // Check if profile exists
    const existingProfile =
      await this.sellerShippingProfileRepository.findById(id);

    if (!existingProfile) {
      throw new HttpErrors.NotFound('Shipping profile not found');
    }
    const transaction =
      await this.sellerShippingProfileRepository.dataSource.beginTransaction({
        isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,
      });
    try {
      // Update the profile
      await this.sellerShippingProfileRepository.updateById(id, profile, {
        transaction,
      });

      // Update basic shipping charges if provided
      if (basicShippingCharges?.length) {
        // Delete existing basic charges
        await this.sellerBasicShippingChargeRepository.deleteAllHard(
          {shippingProfileId: id},
          {transaction},
        );
        // Create new basic charges
        const chargePromises = basicShippingCharges.map(charge =>
          this.sellerBasicShippingChargeRepository.create(
            new SellerBasicShippingCharge({
              ...charge,
              shippingProfileId: id,
            }),
            {transaction},
          ),
        );
        await Promise.all(chargePromises);
      } else {
        await this.sellerBasicShippingChargeRepository.deleteAllHard(
          {shippingProfileId: id},
          {transaction},
        );
      }

      // Update advanced shipping charges (weight-based rules) if provided
      if (advancedShippingCharges?.length) {
        // Delete existing advanced charges
        await this.sellerAdvancedShippingChargeRepository.deleteAllHard(
          {shippingProfileId: id},
          {transaction},
        );
        // Create new advanced charges
        const rulePromises = advancedShippingCharges.map(rule =>
          this.sellerAdvancedShippingChargeRepository.create(
            new SellerAdvancedShippingCharge({
              ...rule,
              shippingProfileId: id,
            }),
            {transaction},
          ),
        );
        await Promise.all(rulePromises);
      } else {
        await this.sellerAdvancedShippingChargeRepository.deleteAllHard(
          {shippingProfileId: id},
          {transaction},
        );
      }

      // Update product variant shipping charges if provided
      if (productVariantShippingCharges?.length) {
        // Delete existing product variant charges
        await this.sellerProductVariantShippingChargeRepository.deleteAllHard(
          {shippingProfileId: id},
          {transaction},
        );
        // Create new product variant charges
        const productChargePromises = productVariantShippingCharges.map(
          charge =>
            this.sellerProductVariantShippingChargeRepository.create(
              new SellerProductVariantShippingCharge({
                ...charge,
                shippingProfileId: id,
              }),
              {transaction},
            ),
        );
        await Promise.all(productChargePromises);
      } else {
        await this.sellerProductVariantShippingChargeRepository.deleteAllHard(
          {shippingProfileId: id},
          {transaction},
        );
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get shipping profile with all related entities
   */
  async getShippingProfileWithRelations(
    id: string,
  ): Promise<SellerShippingProfile> {
    return this.sellerShippingProfileRepository.findById(id, {
      include: [
        {relation: 'basicShippingCharges'},
        {relation: 'advancedShippingCharges'},
        {relation: 'productVariantShippingCharges'},
        {relation: 'shippingMethod'},
      ],
    });
  }

  /**
   * Get all shipping profiles for a seller
   */
  async getSellerShippingProfiles(
    sellerId: string,
    filter?: FilterExcludingWhere<SellerShippingProfile>,
  ): Promise<SellerShippingProfile[]> {
    const defaultFilter = {
      where: {sellerId, isActive: true},
      include: [
        {relation: 'basicShippingCharges'},
        {relation: 'advancedShippingCharges'},
        {relation: 'productVariantShippingCharges'},
        {relation: 'shippingMethod'},
      ],
    };
    filter = filter ? {...defaultFilter, ...filter} : defaultFilter;
    return this.sellerShippingProfileRepository.find(filter);
  }
}
