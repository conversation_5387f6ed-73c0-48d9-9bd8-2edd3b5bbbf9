import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Shipping} from '../models';
import {ShippingRepository} from '../repositories';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
const basePath = '/shippings';
export class ShippingController {
  constructor(
    @repository(ShippingRepository)
    public shippingRepository: ShippingRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateOrder]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Shipping model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Shipping)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Shipping, {
            title: 'NewShipping',
            exclude: ['id'],
          }),
        },
      },
    })
    shipping: Omit<Shipping, 'id'>,
  ): Promise<Shipping> {
    return this.shippingRepository.create(shipping);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Shipping model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(Shipping) where?: Where<Shipping>): Promise<Count> {
    return this.shippingRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Shipping model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Shipping, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Shipping) filter?: Filter<Shipping>,
  ): Promise<Shipping[]> {
    return this.shippingRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateOrder]})
  @patch(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Shipping PATCH success count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Shipping, {partial: true}),
        },
      },
    })
    shipping: Shipping,
    @param.where(Shipping) where?: Where<Shipping>,
  ): Promise<Count> {
    return this.shippingRepository.updateAll(shipping, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Shipping model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Shipping, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Shipping, {exclude: 'where'})
    filter?: FilterExcludingWhere<Shipping>,
  ): Promise<Shipping> {
    return this.shippingRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateOrder]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Shipping PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Shipping, {partial: true}),
        },
      },
    })
    shipping: Shipping,
  ): Promise<void> {
    await this.shippingRepository.updateById(id, shipping);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateOrder]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Shipping PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() shipping: Shipping,
  ): Promise<void> {
    await this.shippingRepository.replaceById(id, shipping);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteOrder]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Shipping DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.shippingRepository.deleteById(id);
  }
}
