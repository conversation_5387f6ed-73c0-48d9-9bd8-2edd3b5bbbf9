import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  EcomdukeserviceRequest,
  EcomdukeRequestNotes,
} from '../models';
import {EcomdukeserviceRequestRepository} from '../repositories';

export class EcomdukeserviceRequestEcomdukeRequestNotesController {
  constructor(
    @repository(EcomdukeserviceRequestRepository) protected ecomdukeserviceRequestRepository: EcomdukeserviceRequestRepository,
  ) { }

  @get('/ecomdukeservice-requests/{id}/ecomduke-request-notes', {
    responses: {
      '200': {
        description: 'Array of EcomdukeserviceRequest has many EcomdukeRequestNotes',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(EcomdukeRequestNotes)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<EcomdukeRequestNotes>,
  ): Promise<EcomdukeRequestNotes[]> {
    return this.ecomdukeserviceRequestRepository.ecomdukeRequestNotes(id).find(filter);
  }

  @post('/ecomdukeservice-requests/{id}/ecomduke-request-notes', {
    responses: {
      '200': {
        description: 'EcomdukeserviceRequest model instance',
        content: {'application/json': {schema: getModelSchemaRef(EcomdukeRequestNotes)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof EcomdukeserviceRequest.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EcomdukeRequestNotes, {
            title: 'NewEcomdukeRequestNotesInEcomdukeserviceRequest',
            exclude: ['id'],
            optional: ['ecomdukeserviceRequestId']
          }),
        },
      },
    }) ecomdukeRequestNotes: Omit<EcomdukeRequestNotes, 'id'>,
  ): Promise<EcomdukeRequestNotes> {
    return this.ecomdukeserviceRequestRepository.ecomdukeRequestNotes(id).create(ecomdukeRequestNotes);
  }

  @patch('/ecomdukeservice-requests/{id}/ecomduke-request-notes', {
    responses: {
      '200': {
        description: 'EcomdukeserviceRequest.EcomdukeRequestNotes PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EcomdukeRequestNotes, {partial: true}),
        },
      },
    })
    ecomdukeRequestNotes: Partial<EcomdukeRequestNotes>,
    @param.query.object('where', getWhereSchemaFor(EcomdukeRequestNotes)) where?: Where<EcomdukeRequestNotes>,
  ): Promise<Count> {
    return this.ecomdukeserviceRequestRepository.ecomdukeRequestNotes(id).patch(ecomdukeRequestNotes, where);
  }

  @del('/ecomdukeservice-requests/{id}/ecomduke-request-notes', {
    responses: {
      '200': {
        description: 'EcomdukeserviceRequest.EcomdukeRequestNotes DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(EcomdukeRequestNotes)) where?: Where<EcomdukeRequestNotes>,
  ): Promise<Count> {
    return this.ecomdukeserviceRequestRepository.ecomdukeRequestNotes(id).delete(where);
  }
}
