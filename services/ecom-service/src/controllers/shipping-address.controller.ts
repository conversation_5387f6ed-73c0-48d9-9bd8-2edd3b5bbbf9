import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ShippingAddress} from '../models';
import {ShippingAddressRepository} from '../repositories';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {PermissionKeys} from '@local/core';
import {authorize} from 'loopback4-authorization';

const basePath = '/shipping-addresses';

export class ShippingAddressController {
  constructor(
    @repository(ShippingAddressRepository)
    public shippingAddressRepository: ShippingAddressRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateOrder]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ShippingAddress model instance',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(ShippingAddress)},
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ShippingAddress, {
            title: 'NewShippingAddress',
            exclude: ['id'],
          }),
        },
      },
    })
    shippingAddress: Omit<ShippingAddress, 'id'>,
  ): Promise<ShippingAddress> {
    return this.shippingAddressRepository.create(shippingAddress);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'ShippingAddress model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(ShippingAddress) where?: Where<ShippingAddress>,
  ): Promise<Count> {
    return this.shippingAddressRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of ShippingAddress model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ShippingAddress, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ShippingAddress) filter?: Filter<ShippingAddress>,
  ): Promise<ShippingAddress[]> {
    return this.shippingAddressRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateOrder]})
  @patch(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ShippingAddress PATCH success count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ShippingAddress, {partial: true}),
        },
      },
    })
    shippingAddress: ShippingAddress,
    @param.where(ShippingAddress) where?: Where<ShippingAddress>,
  ): Promise<Count> {
    return this.shippingAddressRepository.updateAll(shippingAddress, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'ShippingAddress model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(ShippingAddress, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ShippingAddress, {exclude: 'where'})
    filter?: FilterExcludingWhere<ShippingAddress>,
  ): Promise<ShippingAddress> {
    return this.shippingAddressRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateOrder]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ShippingAddress PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ShippingAddress, {partial: true}),
        },
      },
    })
    shippingAddress: ShippingAddress,
  ): Promise<void> {
    await this.shippingAddressRepository.updateById(id, shippingAddress);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateOrder]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ShippingAddress PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() shippingAddress: ShippingAddress,
  ): Promise<void> {
    await this.shippingAddressRepository.replaceById(id, shippingAddress);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteOrder]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ShippingAddress DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.shippingAddressRepository.deleteById(id);
  }
}
