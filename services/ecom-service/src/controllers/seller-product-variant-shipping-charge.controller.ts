import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {STATUS_CODE, CONTENT_TYPE} from '@sourceloop/core';

import {SellerProductVariantShippingCharge} from '../models';
import {SellerProductVariantShippingChargeRepository} from '../repositories';

const basePath = '/product-variant-shipping-charges';

export class SellerProductVariantShippingChargeController {
  constructor(
    @repository(SellerProductVariantShippingChargeRepository)
    public shippingChargeRepo: SellerProductVariantShippingChargeRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'SellerProductVariantShippingCharge model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerProductVariantShippingCharge),
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerProductVariantShippingCharge, {
            title: 'NewSellerProductVariantShippingCharge',
            exclude: ['id'],
          }),
        },
      },
    })
    shippingCharge: Omit<SellerProductVariantShippingCharge, 'id'>,
  ): Promise<SellerProductVariantShippingCharge> {
    return this.shippingChargeRepo.create(shippingCharge);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'SellerProductVariantShippingCharge model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(SellerProductVariantShippingCharge)
    where?: Where<SellerProductVariantShippingCharge>,
  ): Promise<Count> {
    return this.shippingChargeRepo.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of SellerProductVariantShippingCharge instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SellerProductVariantShippingCharge, {
            includeRelations: true,
          }),
        },
      },
    },
  })
  async find(
    @param.filter(SellerProductVariantShippingCharge)
    filter?: Filter<SellerProductVariantShippingCharge>,
  ): Promise<SellerProductVariantShippingCharge[]> {
    return this.shippingChargeRepo.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'SellerProductVariantShippingCharge instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerProductVariantShippingCharge, {
          includeRelations: true,
        }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(SellerProductVariantShippingCharge, {exclude: 'where'})
    filter?: FilterExcludingWhere<SellerProductVariantShippingCharge>,
  ): Promise<SellerProductVariantShippingCharge> {
    return this.shippingChargeRepo.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerProductVariantShippingCharge PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerProductVariantShippingCharge, {
            partial: true,
          }),
        },
      },
    })
    shippingCharge: Partial<SellerProductVariantShippingCharge>,
  ): Promise<void> {
    await this.shippingChargeRepo.updateById(id, shippingCharge);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerProductVariantShippingCharge DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.shippingChargeRepo.deleteById(id);
  }
}
