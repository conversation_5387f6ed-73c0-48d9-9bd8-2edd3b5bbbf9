import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ShippingMovement} from '../models';
import {ShippingMovementRepository} from '../repositories';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';

const basePath = '/shipping-movements';

export class ShippingMovementController {
  constructor(
    @repository(ShippingMovementRepository)
    public shippingMovementRepository: ShippingMovementRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateOrder]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ShippingMovement model instance',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(ShippingMovement)},
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ShippingMovement, {
            title: 'NewShippingMovement',
            exclude: ['id'],
          }),
        },
      },
    })
    shippingMovement: Omit<ShippingMovement, 'id'>,
  ): Promise<ShippingMovement> {
    return this.shippingMovementRepository.create(shippingMovement);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'ShippingMovement model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(ShippingMovement) where?: Where<ShippingMovement>,
  ): Promise<Count> {
    return this.shippingMovementRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of ShippingMovement model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ShippingMovement, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ShippingMovement) filter?: Filter<ShippingMovement>,
  ): Promise<ShippingMovement[]> {
    return this.shippingMovementRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateOrder]})
  @patch(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ShippingMovement PATCH success count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ShippingMovement, {partial: true}),
        },
      },
    })
    shippingMovement: ShippingMovement,
    @param.where(ShippingMovement) where?: Where<ShippingMovement>,
  ): Promise<Count> {
    return this.shippingMovementRepository.updateAll(shippingMovement, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'ShippingMovement model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(ShippingMovement, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ShippingMovement, {exclude: 'where'})
    filter?: FilterExcludingWhere<ShippingMovement>,
  ): Promise<ShippingMovement> {
    return this.shippingMovementRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateOrder]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ShippingMovement PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ShippingMovement, {partial: true}),
        },
      },
    })
    shippingMovement: ShippingMovement,
  ): Promise<void> {
    await this.shippingMovementRepository.updateById(id, shippingMovement);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateOrder]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ShippingMovement PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() shippingMovement: ShippingMovement,
  ): Promise<void> {
    await this.shippingMovementRepository.replaceById(id, shippingMovement);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteOrder]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ShippingMovement DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.shippingMovementRepository.deleteById(id);
  }
}
