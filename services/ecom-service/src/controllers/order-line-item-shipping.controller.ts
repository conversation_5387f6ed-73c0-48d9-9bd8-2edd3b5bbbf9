import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  OrderLineItem,
  Shipping,
} from '../models';
import {OrderLineItemRepository} from '../repositories';

export class OrderLineItemShippingController {
  constructor(
    @repository(OrderLineItemRepository) protected orderLineItemRepository: OrderLineItemRepository,
  ) { }

  @get('/order-line-items/{id}/shipping', {
    responses: {
      '200': {
        description: 'OrderLineItem has one Shipping',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Shipping),
          },
        },
      },
    },
  })
  async get(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Shipping>,
  ): Promise<Shipping> {
    return this.orderLineItemRepository.shipping(id).get(filter);
  }

  @post('/order-line-items/{id}/shipping', {
    responses: {
      '200': {
        description: 'OrderLineItem model instance',
        content: {'application/json': {schema: getModelSchemaRef(Shipping)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof OrderLineItem.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Shipping, {
            title: 'NewShippingInOrderLineItem',
            exclude: ['id'],
            optional: ['orderLineItemId']
          }),
        },
      },
    }) shipping: Omit<Shipping, 'id'>,
  ): Promise<Shipping> {
    return this.orderLineItemRepository.shipping(id).create(shipping);
  }

  @patch('/order-line-items/{id}/shipping', {
    responses: {
      '200': {
        description: 'OrderLineItem.Shipping PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Shipping, {partial: true}),
        },
      },
    })
    shipping: Partial<Shipping>,
    @param.query.object('where', getWhereSchemaFor(Shipping)) where?: Where<Shipping>,
  ): Promise<Count> {
    return this.orderLineItemRepository.shipping(id).patch(shipping, where);
  }

  @del('/order-line-items/{id}/shipping', {
    responses: {
      '200': {
        description: 'OrderLineItem.Shipping DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Shipping)) where?: Where<Shipping>,
  ): Promise<Count> {
    return this.orderLineItemRepository.shipping(id).delete(where);
  }
}
