import {inject, Getter} from '@loopback/core';
import {repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {PgDataSource} from '../datasources';
import {
  Shipping,
  ShippingRelations,
  CartItem,
  OrderLineItem,
  ShippingMethod, ShippingAddress} from '../models';
import {CartItemRepository} from './cart-item.repository';
import {OrderLineItemRepository} from './order-line-item.repository';
import {ShippingMethodRepository} from './shipping-method.repository';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {ShippingAddressRepository} from './shipping-address.repository';

export class ShippingRepository extends SequelizeUserModifyCrudRepositoryCore<
  Shipping,
  typeof Shipping.prototype.id,
  ShippingRelations
> {
  public readonly cartItem: BelongsToAccessor<
    CartItem,
    typeof Shipping.prototype.id
  >;

  public readonly orderLineItem: BelongsToAccessor<
    OrderLineItem,
    typeof Shipping.prototype.id
  >;

  public readonly shippingMethod: BelongsToAccessor<
    ShippingMethod,
    typeof Shipping.prototype.id
  >;

  public readonly shippingAddresses: HasManyRepositoryFactory<ShippingAddress, typeof Shipping.prototype.id>;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @repository.getter('CartItemRepository')
    protected cartItemRepositoryGetter: Getter<CartItemRepository>,
    @repository.getter('OrderLineItemRepository')
    protected orderLineItemRepositoryGetter: Getter<OrderLineItemRepository>,
    @repository.getter('ShippingMethodRepository')
    protected shippingMethodRepositoryGetter: Getter<ShippingMethodRepository>,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>, @repository.getter('ShippingAddressRepository') protected shippingAddressRepositoryGetter: Getter<ShippingAddressRepository>,
  ) {
    super(Shipping, dataSource, getCurrentUser);
    this.shippingAddresses = this.createHasManyRepositoryFactoryFor('shippingAddresses', shippingAddressRepositoryGetter,);
    this.registerInclusionResolver('shippingAddresses', this.shippingAddresses.inclusionResolver);
    this.cartItem = this.createBelongsToAccessorFor(
      'cartItem',
      cartItemRepositoryGetter,
    );
    this.orderLineItem = this.createBelongsToAccessorFor(
      'orderLineItem',
      orderLineItemRepositoryGetter,
    );
    this.shippingMethod = this.createBelongsToAccessorFor(
      'shippingMethod',
      shippingMethodRepositoryGetter,
    );
    this.registerInclusionResolver('cartItem', this.cartItem.inclusionResolver);
    this.registerInclusionResolver(
      'orderLineItem',
      this.orderLineItem.inclusionResolver,
    );
    this.registerInclusionResolver(
      'shippingMethod',
      this.shippingMethod.inclusionResolver,
    );
  }
}
