import {inject} from '@loopback/core';
import {PgDataSource} from '../datasources';
import {
  EcomdukeRequestNotes,
  EcomdukeRequestNotesRelations,
  EcomdukeserviceRequest,
} from '../models';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {Getter, repository, BelongsToAccessor} from '@loopback/repository';
import {EcomdukeserviceRequestRepository} from './ecomdukeservice-request.repository';

export class EcomdukeRequestNotesRepository extends SequelizeUserModifyCrudRepositoryCore<
  EcomdukeRequestNotes,
  typeof EcomdukeRequestNotes.prototype.id,
  EcomdukeRequestNotesRelations
> {
  public readonly ecomdukeserviceRequest: BelongsToAccessor<
    EcomdukeserviceRequest,
    typeof EcomdukeRequestNotes.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('EcomdukeserviceRequestRepository')
    protected ecomdukeserviceRequestRepositoryGetter: Getter<EcomdukeserviceRequestRepository>,
  ) {
    super(EcomdukeRequestNotes, dataSource, getCurrentUser);
    this.ecomdukeserviceRequest = this.createBelongsToAccessorFor(
      'ecomdukeserviceRequest',
      ecomdukeserviceRequestRepositoryGetter,
    );
    this.registerInclusionResolver(
      'ecomdukeserviceRequest',
      this.ecomdukeserviceRequest.inclusionResolver,
    );
  }
}
