import {
  OrderLineItem,
  OrderLineItemRelations,
  Order,
  ProductVariant,
  Review, CustomizationValue, Shipping} from '../models';
import {PgDataSource} from '../datasources';
import {Getter, inject} from '@loopback/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {
  repository,
  BelongsToAccessor,
  HasOneRepositoryFactory, HasManyRepositoryFactory} from '@loopback/repository';
import {OrderRepository} from './order.repository';
import {ProductVariantRepository} from './product-variant.repository';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {ReviewRepository} from './review.repository';
import {CustomizationValueRepository} from './customization-value.repository';
import {ShippingRepository} from './shipping.repository';

export class OrderLineItemRepository extends SequelizeUserModifyCrudRepositoryCore<
  OrderLineItem,
  typeof OrderLineItem.prototype.id,
  OrderLineItemRelations
> {
  public readonly order: BelongsToAccessor<
    Order,
    typeof OrderLineItem.prototype.id
  >;

  public readonly productVariant: BelongsToAccessor<
    ProductVariant,
    typeof OrderLineItem.prototype.id
  >;

  public readonly review: HasOneRepositoryFactory<
    Review,
    typeof OrderLineItem.prototype.id
  >;

  public readonly customizationValues: HasManyRepositoryFactory<CustomizationValue, typeof OrderLineItem.prototype.id>;

  public readonly shipping: HasOneRepositoryFactory<Shipping, typeof OrderLineItem.prototype.id>;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('OrderRepository')
    protected orderRepositoryGetter: Getter<OrderRepository>,
    @repository.getter('ProductVariantRepository')
    protected productVariantRepositoryGetter: Getter<ProductVariantRepository>,
    @repository.getter('ReviewRepository')
    protected reviewRepositoryGetter: Getter<ReviewRepository>,
    @repository.getter('CustomizationValueRepository')
    protected customizationValueRepositoryGetter: Getter<CustomizationValueRepository>, @repository.getter('ShippingRepository') protected shippingRepositoryGetter: Getter<ShippingRepository>,
  ) {
    super(OrderLineItem, dataSource, getCurrentUser);
    this.shipping = this.createHasOneRepositoryFactoryFor('shipping', shippingRepositoryGetter);
    this.registerInclusionResolver('shipping', this.shipping.inclusionResolver);
    this.customizationValues = this.createHasManyRepositoryFactoryFor('customizationValues', customizationValueRepositoryGetter,);
    this.registerInclusionResolver('customizationValues', this.customizationValues.inclusionResolver);
    this.review = this.createHasOneRepositoryFactoryFor(
      'review',
      reviewRepositoryGetter,
    );
    this.registerInclusionResolver('review', this.review.inclusionResolver);
    this.productVariant = this.createBelongsToAccessorFor(
      'productVariant',
      productVariantRepositoryGetter,
    );
    this.registerInclusionResolver(
      'productVariant',
      this.productVariant.inclusionResolver,
    );
    this.order = this.createBelongsToAccessorFor(
      'order',
      orderRepositoryGetter,
    );
    this.registerInclusionResolver('order', this.order.inclusionResolver);
  }
}
