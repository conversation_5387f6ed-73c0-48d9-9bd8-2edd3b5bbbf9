import {Getter, inject} from '@loopback/core';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';

import {PgDataSource} from '../datasources';
import {
  EcomdukeserviceRequest,
  EcomDukeserviceRequestRelations,
  Ecomdukeservice, EcomdukeRequestNotes} from '../models';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {EcomdukeserviceRepository} from './ecomdukeservice.repository';
import {EcomdukeRequestNotesRepository} from './ecomduke-request-notes.repository';

export class EcomdukeserviceRequestRepository extends SequelizeUserModifyCrudRepositoryCore<
  EcomdukeserviceRequest,
  typeof EcomdukeserviceRequest.prototype.id,
  EcomDukeserviceRequestRelations
> {
  public readonly ecomdukeservice: BelongsToAccessor<
    Ecomdukeservice,
    typeof EcomdukeserviceRequest.prototype.id
  >;

  public readonly ecomdukeRequestNotes: HasManyRepositoryFactory<EcomdukeRequestNotes, typeof EcomdukeserviceRequest.prototype.id>;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('EcomdukeserviceRepository')
    protected ecomdukeserviceRepositoryGetter: Getter<EcomdukeserviceRepository>, @repository.getter('EcomdukeRequestNotesRepository') protected ecomdukeRequestNotesRepositoryGetter: Getter<EcomdukeRequestNotesRepository>,
  ) {
    super(EcomdukeserviceRequest, dataSource, getCurrentUser);
    this.ecomdukeRequestNotes = this.createHasManyRepositoryFactoryFor('ecomdukeRequestNotes', ecomdukeRequestNotesRepositoryGetter,);
    this.registerInclusionResolver('ecomdukeRequestNotes', this.ecomdukeRequestNotes.inclusionResolver);
    this.ecomdukeservice = this.createBelongsToAccessorFor(
      'ecomdukeservice',
      ecomdukeserviceRepositoryGetter,
    );
    this.registerInclusionResolver(
      'ecomdukeservice',
      this.ecomdukeservice.inclusionResolver,
    );
  }
}
