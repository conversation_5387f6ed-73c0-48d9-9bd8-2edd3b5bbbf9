import {Getter, inject} from '@loopback/core';
import {PgDataSource} from '../datasources';
import {ShippingMovement, ShippingMovementRelations, Shipping} from '../models';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {repository, BelongsToAccessor} from '@loopback/repository';
import {ShippingRepository} from './shipping.repository';

export class ShippingMovementRepository extends SequelizeUserModifyCrudRepositoryCore<
  ShippingMovement,
  typeof ShippingMovement.prototype.id,
  ShippingMovementRelations
> {
  public readonly shipping: BelongsToAccessor<
    Shipping,
    typeof ShippingMovement.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('ShippingRepository')
    protected shippingRepositoryGetter: Getter<ShippingRepository>,
  ) {
    super(ShippingMovement, dataSource, getCurrentUser);
    this.shipping = this.createBelongsToAccessorFor(
      'shipping',
      shippingRepositoryGetter,
    );
    this.registerInclusionResolver('shipping', this.shipping.inclusionResolver);
  }
}
