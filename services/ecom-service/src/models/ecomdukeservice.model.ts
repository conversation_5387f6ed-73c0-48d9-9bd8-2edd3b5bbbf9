import {belongsTo, model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

@model({name: 'ecomdukeservices'})
export class Ecomdukeservice extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
    required: true,
    default: 0.0,
  })
  price: number;

  @property({
    type: 'string',
    required: true,
    default: 'INR',
  })
  currency: string;

  @belongsTo(() => Ecomdukeservice, {keyTo: 'id'}, {name: 'tax_category_id'})
  taxCategoryId: string;

  @property({
    type: 'boolean',
    required: true,
    default: true,
    name: 'is_active',
  })
  isActive: boolean;

  @property({
    type: 'string',
    name: 'service_type',
  })
  serviceType?: string;

  @property({
    type: 'string',
    name: 'payment_type',
  })
  paymentType?: string;

  @property({
    type: 'string',
    name: 'recurring_interval',
  })
  recurringInterval?: string;

  @property({
    type: 'number',
    name: 'first_part_amount',
  })
  firstPartAmount?: number;

  @property({
    type: 'number',
    name: 'second_part_amount',
  })
  secondPartAmount?: number;

  @property({
    type: 'date',
    name: 'last_date_to_pay',
  })
  lastDateToPay?: string;

  @property({
    type: 'boolean',
    name: 'file_upload_required',
  })
  fileUploadRequired?: boolean;

  constructor(data?: Partial<Ecomdukeservice>) {
    super(data);
  }
}

export interface EcomDukeserviceRelations {
  // define navigational properties here
}

export type EcomDukeserviceWithRelations = Ecomdukeservice &
  EcomDukeserviceRelations;
