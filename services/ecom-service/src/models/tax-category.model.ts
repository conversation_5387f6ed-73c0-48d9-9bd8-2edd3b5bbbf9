import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

@model({settings: {strict: false}, name: 'tax_categories'})
export class TaxCategory extends UserModifiableEntity {
  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'boolean',
    default: true,
    name: 'is_default',
  })
  isDefault?: boolean;

  @property({
    type: 'string',
    required: false,
    name: 'hsn_code',
  })
  hsnCode?: string;

  @property({
    type: 'number',
    jsonSchema: {
      maximum: 100,
    },
    required: false,
    name: 'tax_rate',
  })
  taxRate?: number;

  @property({
    type: 'string',
  })
  description?: string;

  constructor(data?: Partial<TaxCategory>) {
    super(data);
  }
}

export interface TaxCategoryRelations {
  // describe navigational properties here
}

export type TaxCategoryWithRelations = TaxCategory & TaxCategoryRelations;
