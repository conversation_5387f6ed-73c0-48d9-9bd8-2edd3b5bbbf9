import {injectable, BindingScope, inject} from '@loopback/core';
import * as fs from 'fs';
import * as path from 'path';
import * as handlebars from 'handlebars';
import {MessageType} from 'loopback4-notifications';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {Notification} from '../models';
import * as jwt from 'jsonwebtoken';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {PermissionKey} from '../enums';
import {systemUser} from '../constants';
import {
  AuthCodeBindings,
  CodeWriterFn,
} from '@sourceloop/authentication-service';

interface EmailData {
  [key: string]: string;
}

@injectable({scope: BindingScope.TRANSIENT})
export class NotificationHelperService {
  constructor(
    @inject(AuthCodeBindings.CODEWRITER_PROVIDER)
    private readonly codeWriter: CodeWriterFn,
    @restService(Notification)
    private readonly notification: ModifiedRestService<Notification>,
  ) {}

  async sendEmail(
    templateFileName: string,
    subject: string,
    emailData: EmailData,
    recipientEmail: string,
    recipientName: string,
  ): Promise<void> {
    try {
      const templatePath = path.resolve(
        __dirname,
        `../templates/${templateFileName}`,
      );
      const templateSource = fs.readFileSync(templatePath, 'utf8');
      const template = handlebars.compile(templateSource);

      const emailBody = template(emailData);

      const notificationPayload = {
        body: emailBody,
        type: MessageType.Email,
        subject,
        receiver: {
          to: [
            {
              id: recipientEmail,
              name: recipientName,
            },
          ],
        },
      };

      const codePayload: IAuthUserWithPermissions = {
        ...systemUser,
        permissions: [
          PermissionKey.CreateNotification,
          PermissionKey.CreateNotificationNum,
        ],
      };
      const token = await this.codeWriter(
        jwt.sign(codePayload, process.env.JWT_SECRET as string, {
          expiresIn: '1h',
          audience: '',
          issuer: process.env.JWT_ISSUER,
          algorithm: 'HS256',
        }),
      );
      await this.notification.create(notificationPayload, `Bearer ${token}`);
    } catch (error) {
      console.error('Error sending email:', error);
      throw error;
    }
  }

  async sendSMS(
    message: string,
    recipientPhone: string,
    recipientName: string,
  ): Promise<void> {
    try {
      const notificationPayload = {
        body: message,
        type: MessageType.SMS,
        subject: 'Ecomdukes Notification',
        receiver: {
          to: [
            {
              id: recipientPhone,
              name: recipientName,
            },
          ],
        },
      };

      const codePayload: IAuthUserWithPermissions = {
        ...systemUser,
        permissions: [
          PermissionKey.CreateNotification,
          PermissionKey.CreateNotificationNum,
        ],
      };
      const token = await this.codeWriter(
        jwt.sign(codePayload, process.env.JWT_SECRET as string, {
          expiresIn: '1h',
          audience: '',
          issuer: process.env.JWT_ISSUER,
          algorithm: 'HS256',
        }),
      );
      await this.notification.create(notificationPayload, `Bearer ${token}`);
    } catch (error) {
      console.error('Error sending SMS:', error);
      throw error;
    }
  }
}
