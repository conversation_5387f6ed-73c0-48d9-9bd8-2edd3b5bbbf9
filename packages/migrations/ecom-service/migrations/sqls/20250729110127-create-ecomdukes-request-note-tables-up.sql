/* Replace with your SQL commands */
CREATE TABLE IF NOT EXISTS main.ecomduke_request_notes (
    id                          uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    ecomdukeservice_requests_id uuid NOT NULL,
    notes                       text NOT NULL,
    previous_status             varchar(50),
    changed_status              varchar(50),
    created_on                  timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by                  uuid,
    deleted                     boolean DEFAULT false NOT NULL,
    deleted_on                  timestamptz,
    deleted_by                  uuid,
    modified_on                 timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_by                 uuid,
    CONSTRAINT pk_ecomduke_request_notes PRIMARY KEY (id),
    CONSTRAINT fk_request_notes_service_request FOREIGN KEY (ecomdukeservice_requests_id)
        REFERENCES main.ecomdukeservice_requests (id) ON DELETE CASCADE
);

ALTER TABLE main.ecomdukeservice_requests
ADD COLUMN IF NOT EXISTS invoice_url text;
