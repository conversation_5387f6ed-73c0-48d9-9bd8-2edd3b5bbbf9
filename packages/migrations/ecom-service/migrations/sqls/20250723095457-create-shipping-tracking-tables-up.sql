/* Replace with your SQL commands */
ALTER TABLE main.shipping
ADD COLUMN IF NOT EXISTS shipping_partner_id  INTEGER,
ADD COLUMN IF NOT EXISTS partner_account_code VARCHAR(100);

-- Create shipping_movements table
CREATE TABLE IF NOT EXISTS main.shipping_movements (
    id                              UUID DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    waybill                         VARCHAR(100) NOT NULL,
    order_id                        VARCHAR(100),
    status                          VARCHAR(100),
    remark                          TEXT,
    location                        VARCHAR(100),
    clickpost_status_description    VARCHAR(100),
    clickpost_status_code           INTEGER,
    timestamp                       TIMESTAMPTZ,
    courier_partner_id              INTEGER,
    status_bucket_description       TEXT,
    latest_status_description       TEXT,
    status_bucket                   INTEGER,
    reference_number                VARCHAR(100),
    is_rvp                          BOOLEAN,
    courier_partner_edd             DATE,
    shipping_id                     UUID NOT NULL,

    created_on                      TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on                     TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                         BOOLEAN DEFAULT FALSE,
    deleted_on                      TIMESTAMPTZ,
    deleted_by                      UUID,
    created_by                      UUID,
    modified_by                     UUID,

    CONSTRAINT pk_shipping_movements PRIMARY KEY (id),
    CONSTRAINT fk_shipping_tracking_shipping_id FOREIGN KEY (shipping_id)
        REFERENCES main.shipping(id) ON DELETE CASCADE
);

-- Create shipping_addresses table
CREATE TABLE IF NOT EXISTS main.shipping_addresses (
    id                              UUID DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    address_line1                   TEXT NOT NULL,
    address_line2                   TEXT,
    city                            VARCHAR(100) NOT NULL,
    state                           VARCHAR(100) NOT NULL,
    zip_code                        VARCHAR(20) NOT NULL,
    country                         VARCHAR(100) NOT NULL,
    customer_id                     UUID,
    locality                        VARCHAR(150) NOT NULL,
    name                            VARCHAR(150) NOT NULL,
    phone_number                    VARCHAR(20) NOT NULL,
    landmark                        TEXT,
    alternative_phone_number        VARCHAR(20),
    address_type                    VARCHAR(10) NOT NULL,
    shipping_id                     UUID NOT NULL,

    created_on                      TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on                     TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                         BOOLEAN DEFAULT FALSE,
    deleted_on                      TIMESTAMPTZ,
    deleted_by                      UUID,
    created_by                      UUID,
    modified_by                     UUID,

    CONSTRAINT pk_shipping_addresses PRIMARY KEY (id),
    CONSTRAINT fk_shipping_address_shipping_id FOREIGN KEY (shipping_id)
        REFERENCES main.shipping(id) ON DELETE CASCADE
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_shipping_tracking_waybill ON main.shipping_movements(waybill);
CREATE INDEX IF NOT EXISTS idx_shipping_address_shipping_id ON main.shipping_addresses(shipping_id);

-- Triggers for modified_on timestamp update
CREATE TRIGGER mdt_shipping_movements
BEFORE UPDATE ON main.shipping_movements
FOR EACH ROW EXECUTE PROCEDURE main.moddatetime();

CREATE TRIGGER mdt_shipping_addresses
BEFORE UPDATE ON main.shipping_addresses
FOR EACH ROW EXECUTE PROCEDURE main.moddatetime();
