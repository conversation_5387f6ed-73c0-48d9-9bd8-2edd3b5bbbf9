-- Update carts table to store shipping details
ALTER TABLE main.carts
ADD COLUMN IF NOT EXISTS shipping_cost NUMERIC(10, 2) DEFAULT 0;

-- Update orders table to store shipping details
ALTER TABLE main.orders
ADD COLUMN IF NOT EXISTS shipping_cost NUMERIC(10, 2) DEFAULT 0;


-- <PERSON>reate shipping table
CREATE TABLE IF NOT EXISTS main.shipping (
    id                      UUID DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    seller_id               UUID NOT NULL,
    shipping_cost           NUMERIC(10, 2) NOT NULL,
    status                  VARCHAR(50) DEFAULT 'PENDING',
    min_delivery_date       TIMESTAMPTZ,
    max_delivery_date       TIMESTAMPTZ,
    expected_delivery_date  TIMESTAMPTZ,
    actual_delivery_date    TIMESTAMPTZ,
    tracking_number         VARCHAR(100),
    tracking_url            TEXT,
    shipping_carrier        VARCHAR(100),
    shipping_method_type    VARCHAR(50),
    shipping_notes          TEXT,
    cart_item_id            UUID,
    order_line_item_id      UUID,
    shipping_method_id      UUID,
    created_on              TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on             TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                 BOOLEAN DEFAULT FALSE,
    deleted_on              TIMESTAMPTZ,
    deleted_by              UUID,
    created_by              UUID,
    modified_by             UUID,
    CONSTRAINT pk_shipping PRIMARY KEY (id),
    CONSTRAINT fk_shipping_cart_item FOREIGN KEY (cart_item_id) 
        REFERENCES main.cart_items(id) ON DELETE SET NULL,
    CONSTRAINT fk_shipping_order_line_item FOREIGN KEY (order_line_item_id) 
        REFERENCES main.order_line_items(id) ON DELETE SET NULL,
    CONSTRAINT fk_shipping_shipping_method FOREIGN KEY (shipping_method_id) 
        REFERENCES main.shipping_methods(id) ON DELETE SET NULL
);

-- Create index for faster lookups
CREATE INDEX idx_shipping_cart_item_id ON main.shipping(cart_item_id);
CREATE INDEX idx_shipping_order_line_item_id ON main.shipping(order_line_item_id);
CREATE INDEX idx_shipping_seller_id ON main.shipping(seller_id);

-- Create trigger for modified_on
CREATE TRIGGER mdt_shipping
BEFORE UPDATE ON main.shipping
FOR EACH ROW EXECUTE PROCEDURE main.moddatetime();