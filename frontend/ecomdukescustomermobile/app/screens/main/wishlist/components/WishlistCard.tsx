import React from 'react';
import {View, Text, Image, TouchableOpacity, StyleSheet} from 'react-native';
import {IconButton} from 'react-native-paper';
import {colors} from '../../../../theme/colors';
import {Icon} from 'react-native-paper';
import customColors from '../../../../theme/customColors';

interface CardProps {
  imageUrl: string;
  title: string;
  description: string;
  deal: string;
  rating: number;
  ratingCount: number;
  price: number;
  mrp: number;
  onAddToCart: () => void;
  onRemove: () => void;
  onPress?: () => void;
  isAddedToCart: boolean;
  onGoToCart?: () => void;
}

const WishlistCard: React.FC<CardProps> = ({
  imageUrl,
  title,
  description,
  deal,
  rating,
  ratingCount,
  price,
  mrp,
  onAddToCart,
  onRemove,
  onPress,
  isAddedToCart,
  onGoToCart,
}) => {
  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
      <View style={styles.card}>
        <View>
          <Image source={{uri: imageUrl}} style={styles.image} />
          <View style={styles.priceRow}>
            <Text style={styles.price}>₹ {price.toFixed(2)}</Text>
            <Text style={styles.mrp}>₹ {mrp.toFixed(2)}</Text>
          </View>
        </View>

        <View style={styles.details}>
          <Text style={styles.title} numberOfLines={2}>
            {title}
          </Text>
          <Text style={styles.description} numberOfLines={2}>
            {description}
          </Text>
          <Text style={styles.deal}>Deal - {deal}</Text>

          <View style={styles.ratingRow}>
            {rating !== undefined && (
              <View style={styles.ratingContainer}>
                {Array.from({length: 5}).map((_, i) => (
                  <Icon
                    key={i}
                    source={
                      i < Math.floor(rating)
                        ? 'star'
                        : i < rating
                        ? 'star-half-full'
                        : 'star-outline'
                    }
                    size={13}
                    color={colors.tertiary}
                  />
                ))}
                <Text style={styles.ratingCount}> ({ratingCount ?? 0})</Text>
              </View>
            )}
          </View>

          <View style={styles.actionRow}>
            <TouchableOpacity
              style={[
                styles.cartButton,
                isAddedToCart && styles.cartButtonAdded,
              ]}
              onPress={isAddedToCart ? onGoToCart : onAddToCart}>
              <Text
                style={[
                  styles.cartText,
                  isAddedToCart && styles.cartTextAdded,
                ]}>
                {isAddedToCart ? 'Go to Cart' : 'Add to Cart'}
              </Text>
            </TouchableOpacity>

            <IconButton
              icon="delete"
              size={30}
              onPress={onRemove}
              iconColor={colors.tertiary}
            />
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    backgroundColor: colors.gray.card,
    borderRadius: 12,
    padding: 12,
    marginVertical: 8,
    marginHorizontal: 12,
    elevation: 2,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 12,
    marginRight: 12,
  },
  details: {
    flex: 1,
    justifyContent: 'space-between',
    gap: 6,
  },
  title: {fontWeight: 'bold', fontSize: 16},
  description: {color: colors.gray.dark, fontSize: 13},
  deal: {color: customColors.primaryContainer, fontSize: 13, fontWeight: '500'},
  ratingRow: {flexDirection: 'row', alignItems: 'center', marginVertical: 4},
  ratingCount: {
    color: colors.gray.dark,
    fontSize: 12,
  },
  priceRow: {marginTop: 30, gap: 6},
  price: {
    fontSize: 15,
    fontWeight: '900',
    color: colors.tertiary,
    marginLeft: 8,
  },

  mrp: {
    marginLeft: 9,
    fontSize: 13,
    color: colors.gray.dark,
    textDecorationLine: 'line-through',
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  cartButton: {
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 6,
    backgroundColor: colors.gray.card,
    borderWidth: 1,
    borderColor: colors.gray.dark,
  },
  cartText: {
    color: customColors.textBlack,
    fontWeight: '500',
    padding: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    marginBottom: 2,
    marginTop: 5,
  },
  cartButtonAdded: {
    backgroundColor: customColors.primaryContainer,
  },

  cartTextAdded: {
    color: customColors.white,
  },
});

export default WishlistCard;
