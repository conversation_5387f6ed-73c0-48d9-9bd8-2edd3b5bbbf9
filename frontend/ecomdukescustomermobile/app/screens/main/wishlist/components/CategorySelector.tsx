import React from 'react';
import {View, FlatList, TouchableOpacity, Text, StyleSheet} from 'react-native';
import customColors from '../../../../theme/customColors';
import {colors} from '../../../../theme/colors';

interface CategorySelectorProps {
  categories: [string, string][]; // e.g., [['cat1', 'Electronics'], ...]
  selectedCollectionId: string | null;
  isCollectionsLoading: boolean;
  onSelect: (id: string | null) => void;
}

const CategorySelector: React.FC<CategorySelectorProps> = ({
  categories,
  selectedCollectionId,
  isCollectionsLoading,
  onSelect,
}) => {
  const data = !isCollectionsLoading
    ? [['all', 'All Products'], ...categories]
    : [];

  return (
    <View style={styles.container}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          gap: 10,
        }}>
        <Text
          style={{
            fontSize: 16,
            fontWeight: 'bold',
            marginBottom: 8,
            color: colors.tertiary,
          }}>
          Categories
        </Text>
        <View>
          <FlatList
            data={data}
            keyExtractor={([id]) => id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.flatListContent}
            renderItem={({item: [id, name]}) => {
              const isSelected =
                selectedCollectionId === id ||
                (id === 'all' && !selectedCollectionId);

              return (
                <>
                  <TouchableOpacity
                    style={[
                      styles.button,
                      isSelected
                        ? styles.selectedButton
                        : styles.unselectedButton,
                    ]}
                    onPress={() => onSelect(id === 'all' ? null : id)}>
                    <Text
                      style={[
                        styles.buttonText,
                        isSelected
                          ? styles.selectedText
                          : styles.unselectedText,
                      ]}>
                      {name}
                    </Text>
                  </TouchableOpacity>
                </>
              );
            }}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
  },
  flatListContent: {
    gap: 8,
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 18,
    borderRadius: 20,
    borderWidth: 1,
  },
  selectedButton: {
    backgroundColor: customColors.primaryContainer,
    borderColor: customColors.primaryContainer,
  },
  unselectedButton: {
    backgroundColor: customColors.white,
    borderColor: colors.gray.medium,
  },
  buttonText: {
    fontSize: 14,
  },
  selectedText: {
    color: customColors.white,
  },
  unselectedText: {
    color: customColors.textBlack,
  },
});

export default CategorySelector;
