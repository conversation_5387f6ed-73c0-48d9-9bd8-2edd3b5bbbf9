import React, {useState} from 'react';
import {View, Text, StyleSheet, Image, Share} from 'react-native';
import {IconButton} from 'react-native-paper';
import {Images} from '../../../assets/images';
import customColors from '../../../theme/customColors';
import {colors} from '../../../theme/colors';
import CustomButton from '../../../components/CustomButton/CustomButton';
import {
  useCreateReferralCodeMutation,
  useGetUserQuery,
} from '../../../redux/auth/authApiSlice';
import {ReferralScreenNavigationProp} from '../../../navigations/types';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useGetDukeCoinBalanceQuery} from '../../../redux/ecom/ecomDukesCoinApiSlice';
interface ScreenProps {
  navigation: ReferralScreenNavigationProp;
}
const ReferralScreen: React.FC<ScreenProps> = ({navigation}) => {
  const {data: user} = useGetUserQuery();
  const {data: coinBalances, isLoading, error} = useGetDukeCoinBalanceQuery();

  const sellerId = user?.profileId;

  const [referralCode, setReferralCode] = useState('');
  const [isCodeGenerated, setIsCodeGenerated] = useState(false);

  const [createReferralCode] = useCreateReferralCodeMutation();

  const handleShareReferral = async () => {
    if (!sellerId) return;

    let code = referralCode;

    // Step 1: Generate code if not already generated
    if (!isCodeGenerated) {
      const result = await createReferralCode({
        referrerId: sellerId,
      }).unwrap();
      code = result.referralCode;
      setReferralCode(code);
      setIsCodeGenerated(true);
    }

    // Step 2: Share using Share API
    const referralLink = `https://demo.ecomdukes.in/register?code=${code}`;
    const message = `Hey! 👋 I'm inviting you to try Ecomdukes – an awesome shopping app! 🛍️\nUse my referral code : ${code} to sign up and get a discount: ${referralLink}`;
    const shareOptions = {
      title: 'Invite with Referral Code',
      message: message,
      failOnCancel: false,
    };

    await Share.share(shareOptions);
  };
  const onBackPress = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <IconButton
            icon="chevron-left"
            size={36}
            iconColor={colors.tertiary}
            onPress={onBackPress}
            style={styles.backButton}
          />
          <Text style={styles.headerText}>Referral Page</Text>
        </View>
        <Image
          source={Images.referral}
          style={styles.banner}
          resizeMode="contain"
        />

        <View style={styles.card}>
          <Text style={styles.label}>
            Referral Code : <Text style={styles.code}>{referralCode}</Text>
          </Text>

          <View style={styles.descriptionBox}>
            <Text style={styles.descriptionText}>
              Refer your friends and earn EcomDukes Coins! You’ll receive coins
              when your referred friends install the app and sign up using your
              code. Use your earned coins as discounts on future purchases.
            </Text>
          </View>

          {isLoading ? (
            <Text>Loading your coins...</Text>
          ) : error ? (
            <Text>Failed to fetch coins.</Text>
          ) : (
            <Text style={{color: colors.tertiary}}>
              You have {coinBalances?.balance ?? 0} EcomDukes Coins
            </Text>
          )}
          <View style={styles.shareRow}>
            <Text style={styles.label}>Share :</Text>
            <IconButton
              icon="share-variant"
              size={20}
              onPress={handleShareReferral}
            />
            <Text style={{color: colors.gray.medium}}>
              (Generate/Share Referral Code)
            </Text>
          </View>

          <CustomButton
            style={styles.dukesCoinBox}
            title={`Total DukesCoin: ${coinBalances?.balance ?? 0}`}
          />
          <Text style={styles.noteText}>
            50% discount is available on Purchase using DukesCoin
          </Text>

          <View style={styles.coinStats}>
            <View style={styles.statBox}>
              <Text style={styles.statText}>
                Used : {coinBalances?.maxApplicable ?? 0}
              </Text>
            </View>
            <View style={styles.statBox}>
              <Text style={styles.statText}>
                Balance : {coinBalances?.balance ?? 0}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};
export default ReferralScreen;

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: colors.gray.backGround, padding: 10},
  header: {
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {fontSize: 18, fontWeight: 'bold'},
  banner: {
    width: '100%',
    height: '45%',
    borderRadius: 16,
    padding: 15,
  },
  card: {
    backgroundColor: customColors.white,
    borderRadius: 16,
    marginLeft: 12,
    marginRight: 12,
    padding: 16,
  },
  label: {
    fontWeight: '600',
    fontSize: 14,
    marginBottom: 6,
  },
  code: {
    fontWeight: 'bold',
  },
  descriptionBox: {
    backgroundColor: colors.gray.card,
    borderRadius: 8,
    padding: 12,
    marginVertical: 8,
  },
  descriptionText: {
    fontSize: 13,
    color: colors.gray.dark,
  },
  shareRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  dukesCoinBox: {
    backgroundColor: colors.tertiary,
  },
  dukesCoinText: {
    color: customColors.white,
    fontWeight: 'bold',
  },
  coinAmount: {
    color: customColors.white,
  },
  noteText: {
    textAlign: 'center',
    fontSize: 12,
    marginVertical: 8,
    color: colors.gray.dark,
  },
  coinStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  statBox: {
    backgroundColor: colors.gray.medium,
    padding: 10,
    borderRadius: 10,
    width: '48%',
    alignItems: 'center',
  },
  statText: {
    color: customColors.white,
    fontWeight: 'bold',
  },
  backButton: {
    margin: 0,
    padding: 0,
  },
  safeArea: {
    flex: 1,
    backgroundColor: customColors.white,
  },
  headerText: {fontWeight: 'bold', fontSize: 18},
});
