import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
// import {useTypedSelector} from '../../../redux/store';
import customColors from '../../../theme/customColors';
import {colors} from '../../../theme/colors';
import {useGetDukeCoinBalanceQuery} from '../../../redux/ecom/ecomDukesCoinApiSlice';

const EcomDukesCoinScreen = () => {
  const {data: coinBalances} = useGetDukeCoinBalanceQuery();

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>My EcomDukes Coins</Text>

      {coinBalances && coinBalances.balance > 0 ? (
        <View style={styles.coinBox}>
          <Text style={styles.coinText}>Available Coins:</Text>
          <Text style={styles.coinAmount}>{coinBalances.balance}</Text>

          <TouchableOpacity style={styles.redeemButton}>
            <Text style={styles.redeemText}>Use Coins</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.noCoinBox}>
          <Text style={styles.noCoinText}>
            You don’t have any EcomDukes Coins yet.
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: customColors.white, padding: 16},
  heading: {fontSize: 20, fontWeight: 'bold', marginBottom: 20},
  coinBox: {
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    alignItems: 'center',
  },
  coinText: {fontSize: 16, marginBottom: 8},
  coinAmount: {fontSize: 32, fontWeight: 'bold', color: colors.primary},
  redeemButton: {
    marginTop: 20,
    backgroundColor: colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 30,
    borderRadius: 8,
  },
  redeemText: {color: customColors.white, fontWeight: '600'},
  noCoinBox: {
    padding: 20,
    backgroundColor: '#fefefe',
    borderRadius: 12,
    alignItems: 'center',
  },
  noCoinText: {fontSize: 16, color: colors.gray.text},
});

export default EcomDukesCoinScreen;
