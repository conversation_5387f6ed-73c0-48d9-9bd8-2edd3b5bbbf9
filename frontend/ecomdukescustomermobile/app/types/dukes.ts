export interface DukeCoin {
  id?: string;
  userTenantId: string;
  coins: number;
  referralId: string;
  transactionType?: TransactionType;
  coinsChanged: number;
  description?: string;
  maxApplicable?: number;
}
export declare enum TransactionType {
  Earn = 'Earn',
  Redeem = 'Redeem',
  Adjustment = 'Adjustment',
}
export interface Referral {
  deleted: boolean;
  deletedOn: string;
  deletedBy: string;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
  id: string;
  referralCode: string;
  type: string;
  status: string;
  referrerId: string;
  referredId: string;
  additionalProp1: Record<string, any>;
}

export interface DukeCoinsResponse {
  deleted: boolean;
  deletedOn: string;
  deletedBy: string;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
  id: string;
  userTenantId: string;
  coins: number;
  referralId: string;
  transactionType: string;
  coinsChanged: number;
  description: string;
  referral: Referral;
  foreignKey: string;
}
