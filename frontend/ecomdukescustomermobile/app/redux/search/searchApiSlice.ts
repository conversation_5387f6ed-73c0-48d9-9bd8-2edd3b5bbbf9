import {ApiSliceIdentifier} from '../../constants/enums';
import {apiSlice} from '../../redux/apiSlice';
import {IFilterWithKeyword} from '../../Types/filter';
import {ProductVariant} from '../product/product';
export interface FilterValue {
  label: string;
  value: string;
  productVariantIds: string[];
  metadata?: {
    previewUrl?: string;
    parentId?: string | null;
    position?: number;
  };
}
export interface FilterGroup {
  label: string;
  isFacet: boolean;
  values: FilterValue[];
  metadata?: {
    type?: 'slider';
    min: number;
    max: number;
  };
}

export const searchApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getSearchSuggestions: builder.query<ProductVariant[], string>({
      query: keyword => ({
        url: '/search/suggestions',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          keyword,
        },
      }),
    }),
    search: builder.query<ProductVariant[], IFilterWithKeyword>({
      query: ({keyword, facetValueIds, collectionIds, ...filter}) => ({
        url: '/search',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          keyword: keyword,
          facetValueIds,
          collectionIds,
          filter: JSON.stringify(filter),
        },
      }),
    }),
    filters: builder.query<
      FilterGroup[],
      {keyword?: string; facetValueIds?: string[]; collectionIds?: string[]}
    >({
      query: ({keyword, facetValueIds, collectionIds}) => ({
        url: '/search/filters',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          keyword,
          facetValueIds,
          collectionIds,
        },
      }),
    }),
  }),
});
export const {
  useGetSearchSuggestionsQuery,
  useLazyGetSearchSuggestionsQuery,
  useSearchQuery,
  useFiltersQuery,
  useLazySearchQuery,
} = searchApiSlice;
