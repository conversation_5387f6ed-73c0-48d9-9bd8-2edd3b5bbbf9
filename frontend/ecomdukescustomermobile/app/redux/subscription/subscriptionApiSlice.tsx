import {ApiSliceIdentifier} from '../../constants/enums';
import {apiSlice} from '../apiSlice';

export interface SubscriptionRequest {
  topic: string;
  fcmToken?: string;
  listKey: string;
  groupId: string;
  userTenantId: string;
}

export interface SubscriptionResponse {
  fcm?: string;
  zoho?: string;
}

export const subscriptionApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    subscribeToTopic: builder.mutation<
      SubscriptionResponse,
      SubscriptionRequest
    >({
      query: subscriptionData => ({
        url: '/subscribe',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: subscriptionData,
      }),
    }),
  }),
});

export const {useSubscribeToTopicMutation} = subscriptionApiSlice;
