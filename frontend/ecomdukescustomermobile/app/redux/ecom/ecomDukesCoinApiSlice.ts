import {ApiSliceIdentifier} from '../../constants/enums';
import {buildFilterParams, Count, IFilter} from '../../types/api';
import {Duke<PERSON>oin, DukeCoinsResponse} from '../../types/dukes';
import {apiSlice} from '../apiSlice';

export const DukeCoinApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getDukeCoins: builder.query<DukeCoin[], {filter?: IFilter}>({
      query: ({filter}) => ({
        url: '/duke-coins',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    getDukeCoinsCount: builder.query<Count, {filter?: IFilter}>({
      query: ({filter}) => ({
        url: '/duke-coins/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
    getDukeCoinBalance: builder.query<
      {balance: number; maxApplicable: number},
      void
    >({
      query: () => ({
        url: '/duke-coins/users/coins/balance',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    getDukeCoinsById: builder.query<DukeCoinsResponse, string>({
      query: id => ({
        url: `/duke-coins/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
  }),
});

export const {
  useGetDukeCoinsQuery,
  useLazyGetDukeCoinsQuery,
  useGetDukeCoinsCountQuery,
  useGetDukeCoinBalanceQuery,
  useGetDukeCoinsByIdQuery,
} = DukeCoinApiSlice;
