'use client';

import {Avatar, Box, Typography} from '@mui/material';
import {CardStyle} from 'enums/pageSection.enum';
import {useRouter} from 'next/navigation';
import {SectionItem} from 'types/page-section';

type Props = {
  item: SectionItem;
  cardStyle?: CardStyle;
};

const CollectionCard = ({item, cardStyle}: Props) => {
  const router = useRouter();

  const handleClick = () => {
    router.push(`listing?collectionIds=${item.entityId}`);
  };

  const title = item.collection?.name || item.title || '';
  const subtitle = item.subtitle || '';
  const thumbnail = item.previewUrl || '';

  if (cardStyle === CardStyle.AVATAR_WITH_TITLE) {
    return (
      <Box
        onClick={handleClick}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          textAlign: 'center',
          width: 100,
        }}
      >
        <Avatar
          src={thumbnail}
          alt={title}
          sx={{width: 64, height: 64, mb: 1, bgcolor: 'white'}}
        />
        <Typography
          variant="subtitle2"
          fontWeight={400}
          sx={{fontSize: '0.85rem'}}
        >
          {title}
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      onClick={handleClick}
      sx={{
        width: 200,
        flex: '0 0 auto',
        borderRadius: '16px',
        overflow: 'hidden',
        backgroundColor: '#fff',
        cursor: 'pointer',
        position: 'relative',
        boxShadow: 1,
        '&:hover': {
          transform: 'scale(1.02)',
        },
      }}
    >
      {cardStyle === CardStyle.IMAGE_TITLE_SUBTITLE ? (
        <Box sx={{height: 230, display: 'flex', flexDirection: 'column'}}>
          <Box
            sx={{
              height: '40%',
              backgroundColor: '#C8D3D5',
              p: 1.5,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
            }}
          >
            <Typography variant="h6" fontWeight={700} sx={{color: '#0E1C1F'}}>
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" sx={{color: '#0E1C1F', mt: 0.5}}>
                {subtitle}
              </Typography>
            )}
          </Box>
          <Box
            sx={{
              height: '100%',
              backgroundImage: `url(${thumbnail})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              flexGrow: 1,
            }}
          />
        </Box>
      ) : cardStyle === CardStyle.IMAGE_TITLE ? (
        <Box
          sx={{
            height: 230,
            backgroundImage: `url(${thumbnail})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            position: 'relative',
            display: 'flex',
            alignItems: 'flex-start',
            justifyContent: 'center',
            p: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            fontWeight={600}
            sx={{color: '#FFF', textShadow: '0 0 5px rgba(0,0,0,0.6)'}}
          >
            {title}
          </Typography>
        </Box>
      ) : (
        <Box
          sx={{
            height: 230,
            backgroundImage: `url(${thumbnail})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      )}
    </Box>
  );
};

export default CollectionCard;
