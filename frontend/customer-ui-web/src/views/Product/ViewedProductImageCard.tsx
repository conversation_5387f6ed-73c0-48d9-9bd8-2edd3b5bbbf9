'use client';
import Image from 'next/image';
import {CURRENCY_MAP} from 'constants/product';
import {Tooltip} from '@mui/material';
import {useAddItemToCartMutation} from 'redux/ecom/cartApiSlice';
import {CartItem} from 'types/cart';
import {openSnackbar} from 'api/snackbar';
import {SnackbarProps} from 'types/snackbar';
import {useAppSelector} from 'redux/hooks';
import {
  useAddItemToWishlistMutation,
  useRemoveItemFromWishlistMutation,
} from 'redux/ecom/wishlistApiSlice';
import React, {useState} from 'react';
import {
  CardContent,
  CardMedia,
  Typography,
  IconButton,
  Box,
  Rating,
  Grid,
  Button,
  Stack,
} from '@mui/material';
import {Heart} from 'iconsax-react';
import {ProductVariant} from 'types/product';
import {useRouter} from 'next/navigation';

const ViewedProductCard: React.FC<{productVariant: ProductVariant}> = ({
  productVariant,
}) => {
  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);
  const [addItemToCart, {isLoading: isAddingToCart}] =
    useAddItemToCartMutation();
  const [isAddedToCart, setIsAddedToCart] = useState(false);

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.stopPropagation();

    const cartItem: Partial<CartItem> = {
      productVariantId: productVariant.id,
      quantity: 1,
    };

    await addItemToCart(cartItem).unwrap();
    setIsAddedToCart(true);
    openSnackbar({
      open: true,
      message: 'Product added to Cart',
      variant: 'alert',
      alert: {color: 'success'},
    } as SnackbarProps);
  };
  const handleGoToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push('/cart');
  };
  const router = useRouter();
  const [isWishlisted, setIsWishlisted] = useState(
    productVariant.wishlist?.id ? true : false,
  );
  const [addItemToWishlist, {isLoading: isAddingToWishlist}] =
    useAddItemToWishlistMutation();

  const [removeItemFromWishlist, {isLoading: isRemovingFromWishlist}] =
    useRemoveItemFromWishlistMutation();

  const [wishlistId, setWishlistId] = useState<string | undefined>(
    productVariant.wishlist?.id,
  );

  const handleToggleWishlist = async (e: React.MouseEvent) => {
    e.stopPropagation();

    if (!isLoggedIn) {
      router.push('/login');
      return;
    }

    if (isWishlisted && wishlistId) {
      await removeItemFromWishlist(wishlistId).unwrap();
      openSnackbar({
        open: true,
        message: 'Removed from wishlist',
        variant: 'alert',
        alert: {color: 'info'},
      } as SnackbarProps);
      setIsWishlisted(false);
      setWishlistId(undefined);
    } else {
      const result = await addItemToWishlist(productVariant.id).unwrap();
      openSnackbar({
        open: true,
        message: 'Added to wishlist',
        variant: 'alert',
        alert: {color: 'success'},
      } as SnackbarProps);
      setIsWishlisted(true);
      setWishlistId(result.id);
    }
  };

  return (
    <Grid spacing={2} justifyContent="flex-start">
      <Box sx={{position: 'relative'}}>
        <CardMedia
          onClick={() => router.push(`/product-details/${productVariant.id}`)}
          component="img"
          height="200"
          image={productVariant.featuredAsset?.previewUrl}
          alt={productVariant.name}
          sx={{
            width: '100%',
            height: '300px',
            borderRadius: '20px',
            padding: '10px',
            backgroundSize: 'contain',
            objectFit: 'fill',
          }}
        />
        <IconButton
          aria-label="add to favorites"
          onClick={handleToggleWishlist}
          disabled={isAddingToWishlist || isRemovingFromWishlist}
          sx={{
            position: 'absolute',
            top: '13px',
            right: '13px',
            backgroundColor: '#fff',
            borderRadius: '50%',
            boxShadow: 1,
            zIndex: 2,
            color: isWishlisted ? '#9A2D8E' : 'inherit',
          }}
        >
          {isWishlisted ? (
            <Heart size="32" color="#FF8A65" variant="Bold" />
          ) : (
            <Heart size="32" color="#FF8A65" variant="Outline" />
          )}
        </IconButton>
        <IconButton
          aria-label="similar"
          sx={{
            position: 'absolute',
            bottom: '13px',
            right: '13px',
            backgroundColor: 'rgba(255, 255, 255, 1)',
            borderRadius: '50%',
            width: 28,
            height: 28,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 1)',
            },
            zIndex: 2,
          }}
        >
          <Image
            src="/assets/images/icons/similarIcon.svg"
            alt="Similar Icon"
            width={20}
            height={20}
            style={{fill: 'red'}}
          />
        </IconButton>
      </Box>

      <CardContent
        sx={{
          height: 'calc(100% - 300px)',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          padding: '12px',
        }}
      >
        <Grid container spacing={4} sx={{flexGrow: 1}}>
          <Grid item xs={8}>
            <Grid container spacing={0.5}>
              <Grid item xs={12}>
                <Tooltip title={productVariant.name}>
                  <Typography
                    component="div"
                    gutterBottom
                    sx={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      fontWeight: '600',
                      textAlign: 'left',
                      lineHeight: '1.2',
                      cursor: 'pointer',
                    }}
                  >
                    {productVariant.name}
                  </Typography>
                </Tooltip>
              </Grid>

              <Grid item xs={12}>
                <Tooltip title={productVariant.product?.description || ''}>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      fontSize: {xs: '0.7rem', sm: '0.8rem'},
                    }}
                  >
                    {productVariant.product?.description ||
                      'No description available.'}
                  </Typography>
                </Tooltip>
              </Grid>

              <Grid item xs={12} minHeight="32px">
                {productVariant.reviews && productVariant.reviews.length > 0 ? (
                  (() => {
                    const totalRatings = productVariant.reviews.length;
                    const avgRating =
                      productVariant.reviews.reduce(
                        (sum, r) => sum + r.rating,
                        0,
                      ) / totalRatings;

                    return (
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Rating
                          name="read-only"
                          value={parseFloat(avgRating.toFixed(1))}
                          precision={0.1}
                          readOnly
                          sx={{fontSize: {xs: '0.8rem', sm: '1rem'}}}
                        />
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{fontSize: {xs: '0.7rem', sm: '0.8rem'}}}
                        >
                          ({totalRatings})
                        </Typography>
                      </Stack>
                    );
                  })()
                ) : (
                  <Box height="28px" />
                )}
              </Grid>

              <Grid xs={12} sx={{mt: 1}}>
                {isAddedToCart ? (
                  <Button
                    onClick={handleGoToCart}
                    sx={{
                      minWidth: '6.5rem',
                      borderRadius: '1rem',
                      color: 'white',
                      backgroundColor: 'primary.main',
                      '&:hover': {
                        color: 'white',
                      },
                      fontSize: {xs: '0.7rem', sm: '0.8rem'},
                      left: '13px',
                    }}
                  >
                    Go to Cart
                  </Button>
                ) : (
                  <Button
                    onClick={handleAddToCart}
                    disabled={isAddingToCart}
                    sx={{
                      minWidth: '6.5rem',
                      borderRadius: '1rem',
                      color: 'black',
                      backgroundColor: 'white',
                      border: '0.5px solid black',
                      '&:hover': {backgroundColor: '#f0f0f0'},
                      fontSize: {xs: '0.7rem', sm: '0.8rem'},
                      left: '13px',
                    }}
                  >
                    {isAddingToCart ? 'Adding...' : 'Add to Cart'}
                  </Button>
                )}
              </Grid>
            </Grid>
          </Grid>

          <Grid item xs={4}>
            <Grid container spacing={1}>
              {(() => {
                const price = productVariant.productVariantPrice?.price;
                const mrp = productVariant.productVariantPrice?.mrp;
                const currency =
                  CURRENCY_MAP[
                    productVariant.productVariantPrice?.currencyCode
                  ] ?? '₹';

                const parsedPrice = parseFloat(price ?? '');
                const parsedMrp = parseFloat(mrp ?? '');

                const isDiscountValid =
                  !isNaN(parsedPrice) &&
                  !isNaN(parsedMrp) &&
                  parsedPrice < parsedMrp;

                return (
                  <>
                    {/* Deal Percentage */}
                    {isDiscountValid && (
                      <Grid item xs={12}>
                        <Typography
                          variant="body2"
                          color="primary"
                          fontWeight="bold"
                        >
                          {`Deal: ${Math.round(
                            (1 - parsedPrice / parsedMrp) * 100,
                          )}% off`}
                        </Typography>
                      </Grid>
                    )}
                    {/* MRP with line-through */}
                    {isDiscountValid && (
                      <Grid item xs={12}>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{textDecoration: 'line-through'}}
                        >
                          M.R.P.: {currency} {mrp}
                        </Typography>
                      </Grid>
                    )}

                    {/* Final Price */}
                    <Grid item xs={12}>
                      <Typography variant="body1" fontWeight="bold">
                        {currency} {price ?? 'N/A'}
                      </Typography>
                    </Grid>
                  </>
                );
              })()}
            </Grid>
          </Grid>
        </Grid>
      </CardContent>
    </Grid>
  );
};

export default ViewedProductCard;
