'use client';

import {Box} from '@mui/material';
import Image from 'next/image';
import {useEffect, useState, useRef} from 'react';

type CarouselSlide = {
  imageUrl: string;
  redirectUrl: string;
  title?: string;
};

export default function Carousel({slides}: {slides: CarouselSlide[]}) {
  const validSlides = slides.filter(slide => !!slide.imageUrl); // skip empty image slides
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(true);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isTabVisibleRef = useRef(true);

  const slideCount = validSlides.length;
  const isSingleImage = slideCount === 1;

  const resetTimeout = () => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
  };

  useEffect(() => {
    if (isSingleImage) return;

    const handleVisibilityChange = () => {
      isTabVisibleRef.current = document.visibilityState === 'visible';

      if (isTabVisibleRef.current) {
        setCurrentIndex(prev => (prev >= slideCount ? 0 : prev));
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isSingleImage, slideCount]);

  useEffect(() => {
    if (isSingleImage || !isTabVisibleRef.current) return;

    resetTimeout();
    timeoutRef.current = setTimeout(() => {
      setCurrentIndex(prev => prev + 1);
    }, 3000);

    return () => resetTimeout();
  }, [currentIndex, isSingleImage]);

  const handleTransitionEnd = () => {
    if (isSingleImage) return;

    if (currentIndex === slideCount) {
      setIsTransitioning(false);
      setCurrentIndex(0);
      setTimeout(() => {
        setIsTransitioning(true);
      }, 20);
    }
  };

  const displayedSlides = isSingleImage
    ? validSlides
    : [...validSlides, validSlides[0]];

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        height: {xs: '200px', sm: '300px', md: '400px'},
        overflow: 'hidden',
        borderRadius: 3,
        mb: 10,
      }}
    >
      {/* Slides */}
      <Box
        onTransitionEnd={handleTransitionEnd}
        sx={{
          display: 'flex',
          width: `${displayedSlides.length * 100}%`,
          transform: `translateX(-${currentIndex * (100 / displayedSlides.length)}%)`,
          transition:
            isTransitioning && !isSingleImage
              ? 'transform 1s ease-in-out'
              : 'none',
        }}
      >
        {displayedSlides.map((slide, index) => {
          const SlideWrapper = slide.redirectUrl ? 'a' : 'div';

          return (
            <Box
              key={index}
              component={SlideWrapper}
              href={slide.redirectUrl || undefined}
              target={slide.redirectUrl ? '_blank' : undefined}
              rel={slide.redirectUrl ? 'noopener noreferrer' : undefined}
              sx={{
                position: 'relative',
                width: `${100 / displayedSlides.length}%`,
                height: {xs: '200px', sm: '300px', md: '400px'},
                flexShrink: 0,
                cursor: slide.redirectUrl ? 'pointer' : 'default',
              }}
            >
              <Image
                src={slide.imageUrl!}
                alt={slide.title || `Slide ${index + 1}`}
                layout="fill"
                objectFit="cover"
              />
            </Box>
          );
        })}
      </Box>

      {/* Dot Indicators */}
      {!isSingleImage && (
        <Box
          sx={{
            position: 'absolute',
            bottom: 16,
            left: '50%',
            transform: 'translateX(-50%)',
            display: 'flex',
            gap: 1.5,
          }}
        >
          {validSlides.map((_, index) => {
            const isActive =
              currentIndex === index ||
              (currentIndex === slideCount && index === 0);
            return (
              <Box
                key={index}
                sx={{
                  width: isActive ? 12 : 8,
                  height: isActive ? 12 : 8,
                  borderRadius: '50%',
                  backgroundColor: isActive
                    ? 'white'
                    : 'rgba(255, 255, 255, 0.5)',
                  transition: 'all 0.3s ease',
                }}
              />
            );
          })}
        </Box>
      )}
    </Box>
  );
}
