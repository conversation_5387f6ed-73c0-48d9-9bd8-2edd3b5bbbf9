import {
  <PERSON>,
  <PERSON>rid,
  <PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Button,
  CircularProgress,
  Chip,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import {useEffect, useMemo, useState} from 'react';
import {ProductVariant} from 'types/product';
import {IFilter} from 'types/api';
import {useGetCollectionsQuery} from 'redux/ecom/collectionApiSlice';
import {useGetProductVariantsQuery} from 'redux/ecom/productVariantApiSlice';
import {useRouter} from 'next/navigation';
import ViewedProductCard from './ViewedProductImageCard';

const AllCategoriesSection = () => {
  const [selectedCollectionId, setSelectedCollectionId] = useState<
    string | null
  >(null);
  const {data: collections, isLoading: isCollectionsLoading} =
    useGetCollectionsQuery({
      fields: {id: true, name: true},
    });

  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only('xs'));
  const isSm = useMediaQuery(theme.breakpoints.only('sm'));
  const isMd = useMediaQuery(theme.breakpoints.only('md'));
  const isLg = useMediaQuery(theme.breakpoints.only('lg'));
  const isXl = useMediaQuery(theme.breakpoints.only('xl'));

  const visibleCount = useMemo(() => {
    if (isXs) return 2;
    if (isSm) return 3;
    if (isMd) return 4;
    if (isLg) return 5;
    return 6; // xl or up
  }, [isXs, isSm, isMd, isLg, isXl]);

  useEffect(() => {
    if (collections && collections.length > 0 && !selectedCollectionId) {
      setSelectedCollectionId(collections[0].id);
    }
  }, [collections, selectedCollectionId]);

  const productVariantFilter: IFilter | void = selectedCollectionId
    ? {
        include: [
          {
            relation: 'product',
            required: true,
            scope: {
              where: {
                collectionId: selectedCollectionId,
              },
            },
          },
          {relation: 'productVariantPrice'},
          {relation: 'featuredAsset'},
        ],
        limit: 8,
      }
    : undefined;

  const {data: productVariants, isLoading: isVariantsLoading} =
    useGetProductVariantsQuery(productVariantFilter);

  const router = useRouter();

  const {visibleCollections, remainingCollectionsCount} = useMemo(() => {
    if (!collections)
      return {visibleCollections: [], remainingCollectionsCount: 0};
    const visible = collections.slice(0, visibleCount);
    const remaining = collections.length - visible.length;
    return {
      visibleCollections: visible,
      remainingCollectionsCount: remaining,
    };
  }, [collections, visibleCount]);

  return (
    <Box mt={4}>
      <Stack flexDirection="row" justifyContent="space-between" mb={2}>
        <Typography variant="h4">All Categories</Typography>
        <Button
          variant="outlined"
          onClick={() => router.push('view-more?type=all-categories')}
          sx={{
            color: '#707070',
            borderColor: '#707070',
            '&:hover': {
              borderColor: '#9E3393',
              color: '#00004F',
            },
          }}
        >
          View More
        </Button>
      </Stack>

      {/* Categories */}
      {isCollectionsLoading ? (
        <CircularProgress size={24} />
      ) : (
        <Grid container spacing={2} mb={3}>
          {visibleCollections.map(collection => (
            <Grid item key={collection.id}>
              <Button
                variant={
                  selectedCollectionId === collection.id
                    ? 'contained'
                    : 'outlined'
                }
                onClick={() => setSelectedCollectionId(collection.id)}
                sx={{
                  borderRadius: 20,
                  textTransform: 'none',
                  whiteSpace: 'nowrap',
                  minWidth: 120,
                  backgroundColor:
                    selectedCollectionId === collection.id
                      ? '#9A2D8E'
                      : 'white',
                  color:
                    selectedCollectionId === collection.id ? 'white' : 'black',
                }}
              >
                {collection.name}
              </Button>
            </Grid>
          ))}

          {remainingCollectionsCount > 0 && (
            <Grid item>
              <Chip
                label={`+${remainingCollectionsCount} more`}
                color="primary"
                clickable
                onClick={() => router.push('view-more?type=all-categories')}
                sx={{borderRadius: 2, height: 36}}
              />
            </Grid>
          )}
        </Grid>
      )}

      {/* Product Grid */}
      {isVariantsLoading ? (
        <CircularProgress />
      ) : (
        <Grid container spacing={3}>
          {productVariants?.map((product: ProductVariant) => (
            <Grid item xs={12} sm={6} md={3} key={product.id}>
              <ViewedProductCard productVariant={product} />
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
};

export default AllCategoriesSection;
