'use client';

import {Grid, Typography, Box, Icon<PERSON>utton, Card} from '@mui/material';
import Image from 'next/image';
import {Printer, Timer1} from 'iconsax-react';
import {CardContent} from '@mui/material';

type Props = {
  orderId: string;
  email: string;
  date: string;
  time: string;
};

export default function OrderConfirmation({orderId, email, date, time}: Props) {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        px: 2,
        py: 4,
      }}
    >
      <Card
        sx={{
          backgroundColor: 'white',
          borderRadius: 3,
          boxShadow: 'none',
          width: '100%',
          maxWidth: 1000,
          mx: 'auto',
          px: {xs: 2, sm: 4, md: 15},
          py: {xs: 3, sm: 4},
        }}
      >
        <Grid container justifyContent="center" spacing={2}>
          <Grid item xs={12} textAlign="center">
            <Typography
              variant="h1"
              fontWeight="bold"
              color="#00004F"
              mt={2}
              fontSize={{xs: '1.8rem', sm: '2.2rem', md: '2.5rem'}}
            >
              🎉 Thank You! 🎉
            </Typography>
          </Grid>

          <Grid item xs={12} textAlign="center" color="#00004F">
            <Typography
              variant="h6"
              sx={{
                fontSize: {xs: '1.1rem', sm: '1.25rem', md: '1.45rem'},
                fontWeight: 400,
              }}
            >
              Your order{' '}
              <Box
                component="span"
                fontWeight="bold"
                fontSize={{xs: '1.25rem', md: '1.6rem'}}
              >
                #{orderId}
              </Box>{' '}
              has been placed!
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <Typography
              variant="h6"
              //   textAlign="center"
              sx={{
                color: '#000000',
                fontSize: {xs: '1rem', sm: '1.1rem', md: '1.15rem'},
              }}
            >
              We sent an email to <strong>{email}</strong> with your order
              confirmation and receipt. If the email hasn't arrived within two
              minutes, please check your spam folder.
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <Grid
              container
              justifyContent="center"
              spacing={4}
              alignItems="center"
              mt={3}
              textAlign="center"
            >
              <Grid item>
                <Box display="flex" alignItems="center" gap={1}>
                  <IconButton>
                    <Timer1 size="28" color="#9E3393" variant="Bold" />
                  </IconButton>
                  <Typography variant="h6" fontSize="1rem">
                    <Box component="span" fontWeight="bold">
                      Time Placed:
                    </Box>{' '}
                    {date} {time}
                  </Typography>
                </Box>
              </Grid>

              <Grid item>
                <Box display="flex" alignItems="center" gap={1}>
                  <IconButton>
                    <Printer size="28" color="#9E3393" variant="Bold" />
                  </IconButton>
                  <Typography variant="body1" fontSize="1rem">
                    Print
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12}>
            <Card
              variant="outlined"
              sx={{
                borderRadius: 2,
                p: {xs: 1.5, sm: 2.5},
                boxShadow: '0px 4px 10px rgba(0,0,0,0.1)',
              }}
            >
              <CardContent sx={{p: 0}}>
                <Typography
                  variant="body1"
                  fontWeight="bold"
                  fontSize={{xs: '0.95rem', sm: '1rem', md: '1.05rem'}}
                  color="#00004F"
                  textAlign="center"
                >
                  Thank you for being a part of our community! Don’t forget to
                  check out our <strong>Referral Program</strong> – invite your
                  friends and earn reward coins. These coins can be used to get
                  discounts on your future purchases!
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} textAlign="center" pb={3}>
            <Box display="flex" justifyContent="center">
              <Image
                src="/assets/images/Logo landscape.svg"
                alt="EcomDukes Logo"
                width={250}
                height={100}
                style={{maxWidth: '100%', height: 'auto'}}
              />
            </Box>
          </Grid>
        </Grid>
      </Card>
    </Box>
  );
}
