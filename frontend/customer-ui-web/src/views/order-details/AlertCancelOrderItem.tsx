'use client';

import {useTheme} from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Avatar from 'components/@extended/Avatar';
import {PopupTransition} from 'components/@extended/Transitions';
import {ThemeMode} from 'config';
import {openSnackbar} from 'api/snackbar';
import {Trash} from 'iconsax-react';
import {SnackbarProps} from 'types/snackbar';
import {useUpdateOrderItemStatusMutation} from 'redux/order/orderApiSlice';
import {OrderItemStatus} from 'enums/orderStatus';

interface AlertCancelOrderItemProps {
  orderItemId: string;
  title: string;
  open: boolean;
  handleClose: () => void;
  refetch: () => void;
}

export default function AlertCancelOrderItem({
  orderItemId,
  title,
  open,
  handleClose,
  refetch,
}: AlertCancelOrderItemProps) {
  const theme = useTheme();
  const [updateOrderItemStatus] = useUpdateOrderItemStatusMutation();

  const cancelHandler = async () => {
    await updateOrderItemStatus({
      orderItemId,
      newStatus: OrderItemStatus.Cancelled,
    }).unwrap();

    openSnackbar({
      open: true,
      message: 'Order item cancelled successfully.',
      variant: 'alert',
      alert: {color: 'success'},
    } as SnackbarProps);

    handleClose();
    refetch();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      keepMounted
      TransitionComponent={PopupTransition}
      maxWidth="xs"
      aria-labelledby="order-cancel-title"
      aria-describedby="order-cancel-description"
    >
      <DialogContent sx={{mt: 2, my: 1}}>
        <Stack alignItems="center" spacing={3.5}>
          <Avatar
            color="error"
            sx={{
              width: 72,
              height: 72,
              fontSize: '1.75rem',
              color:
                theme.palette.mode === ThemeMode.DARK
                  ? theme.palette.common.white
                  : theme.palette.error[100],
            }}
          >
            <Trash />
          </Avatar>
          <Stack spacing={2}>
            <Typography variant="h4" align="center">
              Cancel this order item?
            </Typography>
            <Typography align="center">
              Cancelling
              <Typography variant="subtitle1" component="span">
                {title}
              </Typography>
              will permanently remove it from the order and cannot be reversed.
            </Typography>
          </Stack>

          <Stack direction="row" spacing={2} sx={{width: 1}}>
            <Button
              fullWidth
              onClick={handleClose}
              color="secondary"
              variant="outlined"
            >
              Keep
            </Button>
            <Button
              fullWidth
              color="error"
              variant="contained"
              onClick={cancelHandler}
              autoFocus
            >
              Cancel Order
            </Button>
          </Stack>
        </Stack>
      </DialogContent>
    </Dialog>
  );
}
