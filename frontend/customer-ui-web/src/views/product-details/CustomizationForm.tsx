import {useFormik} from 'formik';
import * as Yup from 'yup';
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormLabel,
  IconButton,
  MenuItem,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from '@mui/material';
import {ProductCustomField} from 'types/product';
import {
  useAddItemToCartMutation,
  useUpdateProductCustomizationsMutation,
} from 'redux/ecom/cartApiSlice';
import {SnackbarProps} from 'types/snackbar';
import {openSnackbar} from 'api/snackbar';
import {useEffect, useState} from 'react';
import {useRouter} from 'next/navigation';
import {productCustomizationsAtom} from 'utils/atoms/productCustomizationAtom';
import {useSetAtom} from 'jotai';
import {useAtomValue} from 'jotai';
import {Camera, Trash} from 'iconsax-react';

type Props = {
  fields: ProductCustomField[];
  productVariantId: string;
  isInCart?: boolean;
  cartId?: string;
};

const ProductCustomizationForm: React.FC<Props> = ({
  fields,
  productVariantId,
  isInCart,
  cartId,
}) => {
  const setProductCustomizations = useSetAtom(productCustomizationsAtom);
  const existingCustomizations = useAtomValue(productCustomizationsAtom);
  const router = useRouter();
  const [addItemToCart, {isLoading}] = useAddItemToCartMutation();
  const [updateCustomizations, {isLoading: isSaving}] =
    useUpdateProductCustomizationsMutation();

  const [isAddedToCart, setIsAddedToCart] = useState(false);

  const initialValues = fields.reduce(
    (acc, field) => {
      const storedValue =
        existingCustomizations?.[productVariantId]?.[field.id];
      acc[field.name] =
        storedValue ??
        (field.fieldType === 'checkbox'
          ? false
          : field.fieldType === 'file'
            ? ''
            : '');
      return acc;
    },
    {} as Record<string, any>,
  );

  const handleGoToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push('/cart');
  };

  const validationSchema = Yup.object(
    fields.reduce(
      (schema, field) => {
        if (field.fieldType === 'file') return schema;

        let validator: Yup.AnySchema;
        if (field.fieldType === 'number') {
          validator = Yup.number()
            .typeError(`${field.label} must be a number`)
            .transform((value, originalValue) =>
              String(originalValue).trim() === '' ? undefined : value,
            );
        } else if (field.fieldType === 'checkbox') {
          validator = field.isRequired
            ? Yup.boolean().oneOf([true], `${field.label} is required`)
            : Yup.boolean();
        } else {
          let stringValidator = Yup.string();
          if (field.validationRegex) {
            stringValidator = stringValidator.matches(
              new RegExp(field.validationRegex),
              'Invalid format',
            );
          }
          if (field.isRequired) {
            stringValidator = stringValidator.required(
              `${field.label} is required`,
            );
          }
          validator = stringValidator;
        }

        if (field.isRequired && field.fieldType !== 'checkbox') {
          validator = validator.required(`${field.label} is required`);
        }
        schema[field.name] = validator;
        return schema;
      },
      {} as Record<string, Yup.AnySchema>,
    ),
  );

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => {
      const customizationValues = await Promise.all(
        fields.map(async field => {
          let value: string;

          if (field.fieldType === 'file') {
            const files: File[] | string = values[field.name];

            if (Array.isArray(files)) {
              const base64Strings = await Promise.all(
                files.map(file => {
                  return new Promise<string>((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onloadend = () => resolve(reader.result as string);
                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                  });
                }),
              );
              value = JSON.stringify(base64Strings);
            } else {
              value = files; // already stringified
            }
          } else {
            value = String(values[field.name]);
          }

          return {
            customizationFieldId: field.id,
            value,
          };
        }),
      );

      const customizationValuesById = fields.reduce(
        (acc, field) => {
          acc[field.id] = values[field.name];
          return acc;
        },
        {} as Record<string, any>,
      );

      setProductCustomizations(prev => ({
        ...prev,
        [productVariantId]: customizationValuesById,
      }));

      if (isInCart && cartId && productVariantId) {
        await updateCustomizations({
          cartId,
          data: customizationValues,
        }).unwrap();
        openSnackbar({
          open: true,
          message: 'Customizations saved successfully!',
          variant: 'alert',
          alert: {color: 'success'},
        } as SnackbarProps);
      } else {
        const cartItem = {
          productVariantId,
          quantity: 1,
          customizationValues,
        };
        await addItemToCart(cartItem).unwrap();
        setIsAddedToCart(true);
        openSnackbar({
          open: true,
          message: 'Product added to Cart',
          variant: 'alert',
          alert: {color: 'success'},
        } as SnackbarProps);
      }
    },
  });

  useEffect(() => {
    const customizationValuesById = fields.reduce(
      (acc, field) => {
        acc[field.id] = formik.values[field.name];
        return acc;
      },
      {} as Record<string, any>,
    );

    setProductCustomizations(prev => ({
      ...prev,
      [productVariantId]: customizationValuesById,
    }));
  }, [formik.values, productVariantId, setProductCustomizations]);

  return (
    <form onSubmit={formik.handleSubmit}>
      <Box display="flex" flexDirection="column" gap={3}>
        {fields.map(field => {
          const error =
            formik.touched[field.name] &&
            typeof formik.errors[field.name] === 'string'
              ? (formik.errors[field.name] as string)
              : undefined;

          switch (field.fieldType) {
            case 'text':
            case 'number':
              return (
                <>
                  <Box key={field.id} sx={{mb: 0}}>
                    <Typography
                      sx={{
                        mb: 0.5,
                        fontWeight: 420,
                        color: '#5d6d7e  ',
                        ml: 2,
                      }}
                      component="label"
                      htmlFor={field.name}
                    >
                      {field.placeholder}
                    </Typography>
                    <TextField
                      key={field.id}
                      type={field.fieldType}
                      name={field.name}
                      fullWidth
                      value={formik.values[field.name]}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={Boolean(error)}
                      helperText={error}
                      sx={{
                        borderRadius: '25px',
                        background: 'white',
                      }}
                    />
                  </Box>
                </>
              );

            case 'textarea':
              return (
                <>
                  <Box key={field.id} sx={{mb: 0}}>
                    <Typography
                      sx={{mb: 0.5, fontWeight: 420, color: '#5d6d7e  ', ml: 2}}
                      component="label"
                      htmlFor={field.name}
                    >
                      {field.placeholder}
                    </Typography>
                    <TextField
                      key={field.id}
                      name={field.name}
                      fullWidth
                      multiline={field.fieldType === 'textarea'}
                      rows={field.fieldType === 'textarea' ? 4 : undefined}
                      value={formik.values[field.name]}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={Boolean(error)}
                      helperText={error}
                      sx={{
                        borderRadius: '25px',
                        background: 'white',
                      }}
                    />
                  </Box>
                </>
              );

            case 'select':
            case 'dropdown':
              return (
                <>
                  <Box key={field.id} sx={{mb: 0}}>
                    <Typography
                      sx={{mb: 0.5, fontWeight: 420, color: '#5d6d7e  ', ml: 2}}
                      component="label"
                      htmlFor={field.name}
                    >
                      {field.placeholder}
                    </Typography>
                    <TextField
                      select
                      key={field.id}
                      name={field.name}
                      fullWidth
                      value={formik.values[field.name]}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={Boolean(error)}
                      helperText={error}
                      sx={{
                        borderRadius: '25px',
                        background: 'white',
                      }}
                    >
                      {(field.productCustomizationOptions || []).map(option => (
                        <MenuItem key={option.id} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </TextField>
                  </Box>
                </>
              );

            case 'checkbox':
              return (
                <FormControl key={field.id} error={Boolean(error)}>
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Checkbox
                          name={field.name}
                          checked={formik.values[field.name]}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                        />
                      }
                      label={field.label}
                    />
                  </FormGroup>
                  {error && <Typography color="error">{error}</Typography>}
                </FormControl>
              );

            case 'file': {
              let urls: string[] = [];

              const raw = formik.values[field.name];
              if (typeof raw === 'string') {
                try {
                  const parsed = JSON.parse(raw);
                  urls = Array.isArray(parsed) ? parsed : [];
                } catch {
                  urls = [];
                }
              } else if (Array.isArray(raw)) {
                urls = raw.map((f: File | string) =>
                  f instanceof File ? URL.createObjectURL(f) : f,
                );
              }

              return (
                <Box key={field.id}>
                  <Typography
                    variant="subtitle1"
                    fontWeight="bold"
                    sx={{color: '#00004F', mb: 1}}
                  >
                    {field.label}
                  </Typography>

                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                      px: 2,
                      py: 1.5,
                      bgcolor: '#f2f3f5',
                      borderRadius: 3,
                      flexWrap: 'wrap',
                    }}
                  >
                    <Button
                      component="label"
                      variant="outlined"
                      startIcon={<Camera />}
                      sx={{textTransform: 'none'}}
                    >
                      Upload Files
                      <input
                        hidden
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={e => {
                          const selected = e.target.files
                            ? Array.from(e.target.files)
                            : [];

                          const existingFiles = formik.values[field.name] || [];
                          const allFiles = [...existingFiles, ...selected];

                          const uniqueFiles = Array.from(
                            new Map(
                              allFiles.map(file => [file.name, file]),
                            ).values(),
                          );

                          formik.setFieldValue(field.name, uniqueFiles);
                        }}
                      />
                    </Button>

                    <Typography sx={{fontSize: 14, color: 'text.secondary'}}>
                      {(Array.isArray(formik.values[field.name])
                        ? formik.values[field.name].map((f: File) => f.name)
                        : []
                      ).join(', ') || 'No file chosen'}
                    </Typography>
                  </Box>

                  {urls.length > 0 && (
                    <Box
                      sx={{display: 'flex', gap: 1, mt: 2, flexWrap: 'wrap'}}
                    >
                      {urls.map((url, idx) => (
                        <Box
                          key={`${field.name}-preview-${idx}`}
                          position="relative"
                          width={64}
                          height={64}
                        >
                          <img
                            src={url}
                            alt={`preview-${idx}`}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                              borderRadius: 4,
                            }}
                          />
                          {!isInCart && (
                            <IconButton
                              size="small"
                              sx={{
                                position: 'absolute',
                                top: 0,
                                right: 0,
                                bgcolor: 'white',
                              }}
                              onClick={e => {
                                e.stopPropagation();

                                const currentValue = formik.values[field.name];

                                let newFiles: (File | string)[] = [];

                                if (Array.isArray(currentValue)) {
                                  newFiles = currentValue.filter(
                                    (_: File | string, i: number) => i !== idx,
                                  );
                                } else if (typeof currentValue === 'string') {
                                  try {
                                    const parsed = JSON.parse(currentValue);
                                    if (Array.isArray(parsed)) {
                                      newFiles = parsed.filter(
                                        (_: string, i: number) => i !== idx,
                                      );
                                    }
                                  } catch {
                                    newFiles = [];
                                  }
                                }

                                formik.setFieldValue(field.name, newFiles);
                              }}
                            >
                              <Trash fontSize="small" />
                            </IconButton>
                          )}
                        </Box>
                      ))}
                    </Box>
                  )}

                  {error && <Typography color="error">{error}</Typography>}
                </Box>
              );
            }

            case 'radio':
              return (
                <FormControl
                  key={field.id}
                  component="fieldset"
                  error={Boolean(error)}
                >
                  <FormLabel component="legend">{field.label}</FormLabel>
                  <RadioGroup
                    name={field.name}
                    value={formik.values[field.name]}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  >
                    {(field.productCustomizationOptions || []).map(option => (
                      <FormControlLabel
                        key={option.id}
                        value={option.value}
                        control={<Radio />}
                        label={option.label}
                      />
                    ))}
                  </RadioGroup>
                  {error && <Typography color="error">{error}</Typography>}
                </FormControl>
              );

            default:
              return (
                <TextField
                  key={field.id}
                  name={field.name}
                  label={field.label}
                  placeholder={field.placeholder}
                  fullWidth
                  value={formik.values[field.name]}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={Boolean(error)}
                  helperText={error}
                />
              );
          }
        })}
        {isAddedToCart ? (
          <Button
            onClick={handleGoToCart}
            variant="contained"
            sx={{
              backgroundColor: '#07074f',
              '&:hover': {backgroundColor: '#07074f'},
              borderRadius: 6,
              px: 4,
              fontWeight: 'bold',
              width: {xs: '100%', sm: 'auto'},
            }}
          >
            GO TO CART
          </Button>
        ) : (
          <Button
            type="submit"
            variant="contained"
            disabled={isLoading || isSaving}
            sx={{
              backgroundColor: '#00004F',
              '&:hover': {backgroundColor: '#07074f'},
              borderRadius: 6,
              px: 4,
              fontWeight: 'bold',
              width: {xs: '100%', sm: 'auto'},
            }}
          >
            {isInCart
              ? isSaving
                ? 'Saving...'
                : 'Save'
              : isLoading
                ? 'Adding...'
                : 'Add to Cart'}
          </Button>
        )}
      </Box>
    </form>
  );
};

export default ProductCustomizationForm;
