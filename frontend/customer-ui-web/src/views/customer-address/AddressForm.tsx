import {
  Box,
  Button,
  Grid,
  Typography,
  OutlinedInput,
  Stack,
  FormControl,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormHelperText,
  InputAdornment,
  TextField,
  Autocomplete,
} from '@mui/material';
import AnimateButton from 'components/@extended/AnimateButton';
import {useFormik} from 'formik';
import {addressValidationSchema} from 'validation/address.validation';
import {AddressDto} from 'types/address';
import {useMemo} from 'react';
import {Country, State} from 'country-state-city';

type AddressFormProps = {
  initialData?: AddressDto;
  loading?: boolean;
  onCancel: () => void;
  onSave: (data: AddressDto) => void;
};

export default function AddressForm({
  initialData,
  loading,
  onCancel,
  onSave,
}: AddressFormProps) {
  const countries = Country.getAllCountries();

  const formik = useFormik({
    initialValues: {
      name: initialData?.name || '',
      zipCode: initialData?.zipCode || '',
      phoneNumber: initialData?.phoneNumber?.startsWith('+91')
        ? initialData.phoneNumber.replace('+91', '').trim()
        : initialData?.phoneNumber || '',
      locality: initialData?.locality || '',
      addressLine1: initialData?.addressLine1 || '',
      addressLine2: initialData?.addressLine2 || '',

      city: initialData?.city || '',
      country: 'India',
      state: 'Kerala',
      addressType: initialData?.addressType || '',
      alternativePhoneNumber: initialData?.alternativePhoneNumber || '',
      landmark: initialData?.landmark || '',
    },
    validationSchema: addressValidationSchema,
    onSubmit: values => {
      onSave(values);
    },
  });
  const states = useMemo(() => {
    if (!formik.values.country) return [];
    const country = countries.find(c => c.name === formik.values.country);
    return State.getStatesOfCountry(country?.isoCode);
  }, [countries, formik.values.country]);

  const countryValue = useMemo(() => {
    return countries.find(item => item.name === formik.values.country);
  }, [countries, formik.values.country]);
  if (loading) return <Typography>Loading...</Typography>;

  return (
    <Box
      component="form"
      onSubmit={formik.handleSubmit}
      sx={{
        width: '100%',
        maxWidth: 939,
        p: {xs: 2, sm: 4},
        borderRadius: 2,
        bgcolor: '#EFF1F2',
        mt: 3,
      }}
    >
      <Grid container spacing={4} sx={{mt: 1}}>
        {/* Left Column */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <OutlinedInput
                  fullWidth
                  name="name"
                  placeholder="Name"
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.name && Boolean(formik.errors.name)}
                />
                {formik.touched.name && formik.errors.name && (
                  <FormHelperText error>{formik.errors.name}</FormHelperText>
                )}
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <OutlinedInput
                  fullWidth
                  name="zipCode"
                  placeholder="ZipCode"
                  value={formik.values.zipCode}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.zipCode && Boolean(formik.errors.zipCode)
                  }
                />
                {formik.touched.zipCode && formik.errors.zipCode && (
                  <FormHelperText error>{formik.errors.zipCode}</FormHelperText>
                )}
              </Stack>
            </Grid>
          </Grid>
        </Grid>

        {/* Right Column */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <FormControl
                  fullWidth
                  error={
                    formik.touched.phoneNumber &&
                    Boolean(formik.errors.phoneNumber)
                  }
                >
                  <OutlinedInput
                    name="phoneNumber"
                    placeholder="10-digit mobile number"
                    value={formik.values.phoneNumber}
                    onChange={e => {
                      const {value} = e.target;
                      if (/^\d{0,10}$/.test(value)) {
                        formik.setFieldValue('phoneNumber', value);
                      }
                    }}
                    onBlur={formik.handleBlur}
                    startAdornment={
                      <InputAdornment position="start">+91</InputAdornment>
                    }
                    inputProps={{
                      maxLength: 10,
                      inputMode: 'numeric',
                      pattern: '[0-9]*',
                    }}
                  />
                  {formik.touched.phoneNumber && formik.errors.phoneNumber && (
                    <FormHelperText>{formik.errors.phoneNumber}</FormHelperText>
                  )}
                </FormControl>
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <OutlinedInput
                  fullWidth
                  name="locality"
                  placeholder="Locality"
                  value={formik.values.locality}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.locality && Boolean(formik.errors.locality)
                  }
                />
                {formik.touched.locality && formik.errors.locality && (
                  <FormHelperText error>
                    {formik.errors.locality}
                  </FormHelperText>
                )}
              </Stack>
            </Grid>
          </Grid>
        </Grid>

        {/* Address */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <OutlinedInput
                fullWidth
                multiline
                name="addressLine1"
                placeholder="Address Line1"
                value={formik.values.addressLine1}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={
                  formik.touched.addressLine1 &&
                  Boolean(formik.errors.addressLine1)
                }
              />
              {formik.touched.addressLine1 && formik.errors.addressLine1 && (
                <FormHelperText error>
                  {formik.errors.addressLine1}
                </FormHelperText>
              )}
            </Grid>
            <Grid item xs={12}>
              <Autocomplete
                id="country"
                options={countries}
                autoHighlight
                fullWidth
                value={countryValue}
                onBlur={() => {
                  formik.setFieldTouched('country', true);
                }}
                getOptionLabel={option => option.name}
                onChange={(e, newValue) => {
                  formik.setFieldValue('country', newValue?.name);
                }}
                renderInput={params => (
                  <TextField
                    {...params}
                    placeholder="Select a country"
                    name="country"
                    error={
                      formik.touched.country && Boolean(formik.errors.country)
                    }
                    helperText={formik.touched.country && formik.errors.country}
                  />
                )}
              />
            </Grid>
          </Grid>
        </Grid>
        {/* State & Landmark */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <OutlinedInput
                fullWidth
                multiline
                name="addressLine2"
                placeholder="Address Line2"
                value={formik.values.addressLine2}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={
                  formik.touched.addressLine2 &&
                  Boolean(formik.errors.addressLine2)
                }
              />
              {formik.touched.addressLine2 && formik.errors.addressLine2 && (
                <FormHelperText error>
                  {formik.errors.addressLine2}
                </FormHelperText>
              )}
            </Grid>
            <Grid item xs={12}>
              <Autocomplete
                id="state"
                options={states}
                autoHighlight
                fullWidth
                value={states.find(item => item.name === formik.values.state)}
                onBlur={() => {
                  formik.setFieldTouched('country', true);
                }}
                getOptionLabel={option => option.name}
                onChange={(e, newValue) => {
                  formik.setFieldValue('state', newValue?.name);
                }}
                renderInput={params => (
                  <TextField
                    {...params}
                    placeholder="Select a state"
                    name="state"
                    error={formik.touched.state && Boolean(formik.errors.state)}
                    helperText={formik.touched.state && formik.errors.state}
                  />
                )}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Stack spacing={1}>
                <OutlinedInput
                  fullWidth
                  name="city"
                  placeholder="City/District/Town"
                  value={formik.values.city}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.city && Boolean(formik.errors.city)}
                />
                {formik.touched.city && formik.errors.city && (
                  <FormHelperText error>{formik.errors.city}</FormHelperText>
                )}
              </Stack>
            </Grid>
            <Grid item xs={12} md={6}>
              <Stack spacing={1}>
                <OutlinedInput
                  fullWidth
                  name="landmark"
                  placeholder="Landmark (Optional)"
                  value={formik.values.landmark}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
              </Stack>
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12} md={6}>
          <OutlinedInput
            fullWidth
            name="alternativePhoneNumber"
            placeholder="Alternate Phone (Optional)"
            value={formik.values.alternativePhoneNumber}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={
              formik.touched.alternativePhoneNumber &&
              Boolean(formik.errors.alternativePhoneNumber)
            }
          />
          {formik.touched.alternativePhoneNumber &&
            formik.errors.alternativePhoneNumber && (
              <FormHelperText error>
                {formik.errors.alternativePhoneNumber}
              </FormHelperText>
            )}
        </Grid>
        {/* Address Type */}
        <Grid item xs={12}>
          <FormControl
            error={
              formik.touched.addressType && Boolean(formik.errors.addressType)
            }
          >
            <Typography variant="body2">Address Type</Typography>
            <RadioGroup
              row
              name="addressType"
              value={formik.values.addressType}
              onChange={formik.handleChange}
            >
              <FormControlLabel value="Home" control={<Radio />} label="Home" />
              <FormControlLabel value="Work" control={<Radio />} label="Work" />
              <FormControlLabel
                value="Other"
                control={<Radio />}
                label="Other"
              />
            </RadioGroup>
            {formik.touched.addressType && formik.errors.addressType && (
              <FormHelperText>{formik.errors.addressType}</FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <Stack
            direction="row"
            spacing={1}
            justifyContent="flex-start"
            alignItems="center"
            flexWrap="wrap"
          >
            <AnimateButton>
              <Button
                variant="contained"
                type="submit"
                sx={{
                  bgcolor: '#00004F',
                  px: {xs: 4, sm: 6, md: 10},
                  py: {xs: 1, sm: 1.5},
                  minWidth: {xs: 'auto', sm: '150px'},
                  fontSize: '1rem',
                }}
              >
                Save
              </Button>
            </AnimateButton>
            <Typography
              variant="h5"
              sx={{
                cursor: 'pointer',
                lineHeight: 1,
                fontSize: {xs: '14px', sm: '16px', md: '18px'},
              }}
              onClick={onCancel}
            >
              Cancel
            </Typography>
          </Stack>
        </Grid>
      </Grid>
    </Box>
  );
}
