// ==============================|| TYPES - USER PROFILE  ||============================== //

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  middleName: string | null;
  username: string;
  email: string;
  phone: string | null;
  lastLogin: string | null;
  photoUrl: string | null;
  designation: string | null;
  dob: string | Date | null;
  gender: string | null;
  defaultTenantId: string;
  permissions: string[];
  role: string;
  userPreferences: {
    locale: string;
  };
  tenantId: string;
  userTenantId: string;
  status: number;
  iat: number;
  exp: number;
  iss: string;
  approved: boolean;
  verificationCode: string;
  profileId: string;
  profileVerificationCode: string;
  onBoardComplete: boolean;
  rejectionReason: string;
  profileStatus: string;
  profileDisplayId: string;
  phoneVerified: boolean;
  emailVerified: boolean;
}

export interface UserDto {
  firstName: string;
  lastName: string;
  email: string;
  phone: string | null;
  designation: string | null;
  gender: string | null;
  photoUrl: File | null;
  dob: string | Date | null;
}
