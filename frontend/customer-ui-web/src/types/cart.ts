import {CartStatus} from 'enums/ecom.enum';
import {ProductVariant} from './product';
import {PromoCode} from './promoCode';

export interface Cart {
  id: string;
  customerId: string;
  status: CartStatus | string;
  cartItems?: CartItem[];
  promoCode?: PromoCode;
  promoCodeId?: string | null;
  ecomDukeCoinsApplied?: number;
}

export interface CartItem {
  id: string;
  quantity: number;
  cartId: string;
  productVariantId: string;
  cart?: Cart;
  productVariant?: ProductVariant;
}

export interface CustomizationValue {
  id?: string;
  value: string;
  cartId: string;
  orderId: string;
  customizationFieldId: string;
}

export interface CheckoutCartResponse {
  id: string;
  cart_details: null | any;
  cf_order_id: string;
  created_at: string;
  customer_details: {
    customer_id: string;
    customer_name: string;
    customer_email: string;
    customer_phone: string;
    customer_uid: string | null;
  };
  entity: string;
  order_amount: number;
  order_currency: string;
  order_expiry_time: string;
  order_id: string;
  order_meta: {
    notify_url: string | null;
    payment_methods: string | null;
    payment_methods_filters: string | null;
    return_url: string | null;
  };
  order_note: string;
  order_splits: any[];
  order_status: string;
  order_tags: string | null;
  payment_session_id: string;
  products: {
    one_click_checkout: {
      enabled: boolean;
      conditions: any[];
    };
    verify_pay: {
      enabled: boolean;
      conditions: any[];
    };
  };
  terminal_data: any | null;
}
