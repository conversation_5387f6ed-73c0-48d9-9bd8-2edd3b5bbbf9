import {AxiosResponse} from 'axios';
import {SignupDto, TokenResponse} from 'types/auth';
import {User} from 'types/user-profile';
import authApiService from 'utils/authApi';

export const fetchToken = async (code: string): Promise<TokenResponse> => {
  const data = {
    code: code,
    clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID as string,
  };

  try {
    const response: AxiosResponse<TokenResponse> = await authApiService.post(
      '/auth/token',
      data,
    );

    return response.data;
  } catch (error) {
    throw error;
  }
};

export const fetchUserDetails = async (token: string): Promise<User> => {
  const user = await authApiService.get<User>('/auth/me', {
    headers: {
      Authorization: `Bearer ${token}`,
      device_id: '',
      ['x-origin']: 'ecomdukes-customer',
    },
  });

  return user.data;
};

export const forgotPassword = async (email: string) => {
  const payload = {
    username: email,
    client_id: process.env.NEXT_PUBLIC_LOCAL_CLIENT_ID,
    client_secret: process.env.NEXT_PUBLIC_LOCAL_CLIENT_SECRET,
  };
  const response = await authApiService.post('/auth/forget-password', payload, {
    headers: {
      'x-origin': 'ecomdukes-customer',
    },
  });
  return response.data;
};

export const resetPassword = async (token: string, newPassword: string) => {
  const payload = {
    token: token,
    password: newPassword,
    client_id: process.env.NEXT_PUBLIC_LOCAL_CLIENT_ID,
    client_secret: process.env.NEXT_PUBLIC_LOCAL_CLIENT_SECRET,
  };
  const response = await authApiService.patch('/auth/reset-password', payload);
  return response.data;
};

export const signUp = async (token: string, signupDto: SignupDto) => {
  const user = await authApiService
    .post('/auth/sign-up/create-user', signupDto, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    .catch(error => {
      throw error;
    });

  return user;
};

export const sendOtp = async (key: string) => {
  return authApiService.post('/auth/send-otp', {
    client_id: process.env.NEXT_PUBLIC_LOCAL_CLIENT_ID,
    client_secret: process.env.NEXT_PUBLIC_LOCAL_CLIENT_SECRET,
    key,
  });
};

export const verifyOtp = async (key: string, otp: string) => {
  return authApiService.post('/auth/verify-otp', {
    client_id: process.env.NEXT_PUBLIC_LOCAL_CLIENT_ID,
    key,
    otp,
  });
};
