'use client';

// next
import Image from 'next/image';

// material-ui
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// project-imports
import AuthSocButton from 'sections/auth/AuthSocButton';

// assets
const imgFacebook = '/assets/images/auth/facebook.svg';
const imgGoogle = '/assets/images/auth/google.svg';

// ================================|| LOGIN ||================================ //

export default function SocialMedia() {
  return (
    <Grid
      container
      spacing={3}
      sx={{minHeight: '100%', alignContent: 'space-between'}}
    >
      <Grid item xs={12} sx={{'& > div': {margin: '24px auto'}}}>
        <Grid container spacing={1}>
          <Grid item xs={12}>
            <AuthSocButton
              sx={{
                backgroundColor: '#000',
                color: '#fff',
                '&:hover': {
                  backgroundColor: '#222',
                },
              }}
            >
              <Stack direction="row" alignItems="center" spacing={1}>
                <Image src={imgGoogle} alt="Google" width={16} height={16} />
                <Typography sx={{color: '#fff'}}>
                  Sign In with Google
                </Typography>
              </Stack>
            </AuthSocButton>
          </Grid>
          <Grid item xs={12}>
            <AuthSocButton>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Image
                  src={imgFacebook}
                  alt="Facebook"
                  width={16}
                  height={16}
                />
                <Typography>Sign In with Facebook</Typography>
              </Stack>
            </AuthSocButton>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
}
