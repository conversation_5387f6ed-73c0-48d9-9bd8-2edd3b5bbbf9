import {ApiSliceIdentifier} from 'enums/api.enum';
import {OrderItemStatus} from 'enums/orderStatus';
import {apiSlice} from 'redux/apiSlice';
import {buildFilterParams, Count} from 'types/api';
import {IFilter} from 'types/filter';
import {Order, OrderLineItem} from 'types/order';

export const orderApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getOrder: builder.query<OrderLineItem[], {filter?: IFilter}>({
      query: ({filter}) => ({
        url: '/order-line-items',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    getOrderById: builder.query<Order, {id: string; filter?: IFilter}>({
      query: ({id, filter}) => ({
        url: `/orders/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
    getOrderCount: builder.query<Count, {filter?: IFilter}>({
      query: ({filter}) => ({
        url: '/order-line-items/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
    updateOrderItemStatus: builder.mutation<
      void,
      {orderItemId: string; newStatus: OrderItemStatus}
    >({
      query: ({orderItemId, newStatus}) => ({
        url: `/order-line-items/${orderItemId}/status/${newStatus}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),

    downloadInvoicePdf: builder.query<Blob, string>({
      query: invoiceId => ({
        url: `/invoices/${invoiceId}/download`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        responseHandler: (response: Response) => response.blob(),
      }),
    }),
  }),
});

export const {
  useGetOrderQuery,
  useGetOrderByIdQuery,
  useGetOrderCountQuery,
  useDownloadInvoicePdfQuery,
  useLazyDownloadInvoicePdfQuery,
  useUpdateOrderItemStatusMutation,
} = orderApiSlice;
