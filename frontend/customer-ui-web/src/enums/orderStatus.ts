export enum OrderStatus {
  Pending = 'pending',
  Paid = 'paid',
  PaymentFailed = 'payment_failed',
  Failed = 'failed',
  Picked = 'picked',
  InTransit = 'in_transit',
  Delivered = 'delivered',
  DeliveryFailed = 'delivery_failed',
  Cancelled = 'cancelled',
  Returned = 'returned',
  Refunded = 'refunded',
  RefundInitiated = 'refund_initiated',
  RefundFailed = 'refund_failed',
}
export enum OrderItemStatus {
  New = 'new',
  Pending = 'pending',
  Accepted = 'accepted',
  Processing = 'processing',
  Dispatched = 'dispatched',
  Rejected = 'rejected',
  ReturnRefund = 'return_refund',
  Delivered = 'delivered',
  RefundCompleted = 'refund_completed',
  Cancelled = 'cancelled',
  Paid = 'paid',
  ReadyToDispatch = 'ready_to_dispatch',
}

export const orderStatusOptions: {
  label: string;
  value: OrderItemStatus | '';
}[] = [
  {label: 'All', value: ''},
  {label: 'New', value: OrderItemStatus.New},
  {label: 'Pending', value: OrderItemStatus.Pending},
  {label: 'Accepted', value: OrderItemStatus.Accepted},
  {label: 'Processing', value: OrderItemStatus.Processing},
  {label: 'Dispatched', value: OrderItemStatus.Dispatched},
  {label: 'Rejected', value: OrderItemStatus.Rejected},
  {label: 'Return/Refund', value: OrderItemStatus.ReturnRefund},
  {label: 'Delivered', value: OrderItemStatus.Delivered},
  {label: 'Refund Completed', value: OrderItemStatus.RefundCompleted},
  {label: 'Cancelled', value: OrderItemStatus.Cancelled},
];
