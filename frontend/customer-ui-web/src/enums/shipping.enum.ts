export declare enum ShippingStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  SHIPPED = 'SHIPPED',
  IN_TRANSIT = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  FAILED = 'FAILED',
  RETURNED = 'RETURNED',
  CANCELLED = 'CANCELLED',
}
export declare enum ShippingMethodType {
  ECOMDUKES = 'ECOMDUKES',
  SELF_SHIPPING = 'SELF_SHIPPING',
}
export declare enum BasicShippingStateType {
  IN_STATE = 'IN_STATE',
  OUT_STATE = 'OUT_STATE',
}
