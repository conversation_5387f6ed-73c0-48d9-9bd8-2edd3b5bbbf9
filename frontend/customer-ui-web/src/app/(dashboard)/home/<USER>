'use client';

import {<PERSON>, Button, Grid, Stack, Typography} from '@mui/material';
import {
  useGetMostViewedProductsQuery,
  useGetPageSectionsQuery,
  useGetRecentlyViewedProductsQuery,
  useGetTopSellingProductsQuery,
} from 'redux/ecom/pageSectionApiSlice';
import {
  CardStyle,
  PageType,
  SectionType,
  PRODUCT_SECTION_TYPES,
} from '../../../enums/pageSection.enum';
import Carousel from 'views/Product/Carousel';
import {PageSection} from 'types/page-section';
import {fieldsExcludeMetaFields} from 'types/api';
import BannerSection from 'views/Product/Banner';
import TextBlockSection from 'views/Product/TextBlock';
import CollectionCard from 'views/Product/CollectionCard';
import {ReviewStatus} from 'types/review';
import {useAppSelector} from 'redux/hooks';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import React from 'react';
import {IFilter} from 'types/filter';
import ProductFilterSection from 'views/Product/ProductFilterSection';
import AllCategoriesSection from 'views/Product/AllCategories';
import FacetCard from 'views/Product/FacetCard';
import {GiftProductsSection} from 'views/Product/GiftProductsSection';
import {useRouter} from 'next/navigation';
import {SimilarProducts} from 'views/Product/SimilarProducts';
import ProductImages from 'views/Product/ProductImages';
import ViewedProductCard from 'views/Product/ViewedProductImageCard';

export default function HomeScreen() {
  const {data: sections = [], isLoading} = useGetPageSectionsQuery({
    where: {isActive: true, pageType: PageType.HOME},
    order: ['displayOrder ASC'],
    include: [
      {relation: 'sectionItems', scope: {fields: fieldsExcludeMetaFields}},
    ],
    fields: fieldsExcludeMetaFields,
  });
  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);
  const {data: user} = useGetUserQuery(undefined, {skip: !isLoggedIn});

  const productFilter: IFilter = {
    include: [
      {
        relation: 'featuredAsset',
        scope: {
          fields: {preview: true, id: true},
        },
      },
      {
        relation: 'product',
        scope: {
          fields: {description: true, id: true},
        },
      },
      {
        relation: 'productVariantPrice',
        scope: {
          fields: {
            price: true,
            mrp: true,
            currencyCode: true,
          },
        },
      },
      ...(isLoggedIn
        ? [
            {
              relation: 'wishlist',
              scope: {
                where: {
                  deleted: false,
                  customerId: user?.profileId,
                },
                fields: {id: true},
              },
            },
          ]
        : []),
      {
        relation: 'reviews',
        scope: {
          fields: {
            rating: true,
          },
          where: {
            status: ReviewStatus.APPROVED,
          },
        },
      },
    ],
    fields: {
      name: true,
      id: true,
      featuredAssetId: true,
      productId: true,
    },
  };

  const {data: mostViewed = []} = useGetMostViewedProductsQuery(productFilter, {
    skip: !isLoggedIn,
  });

  const {data: recentlyViewed = []} = useGetRecentlyViewedProductsQuery(
    productFilter,
    {skip: !isLoggedIn},
  );

  const {data: topSelling = []} = useGetTopSellingProductsQuery(productFilter);

  const scrollGradientSx = {
    overflowX: 'auto',
    overflowY: 'hidden',
    pb: 2,
    '&::-webkit-scrollbar': {
      height: '7px',
    },
    '&::-webkit-scrollbar-track': {
      background: '#f0f0f0',
      borderTop: '1px solid #e0e0e0',
      borderBottom: '1px solid #e0e0e0',
    },
    '&::-webkit-scrollbar-thumb': {
      background: 'linear-gradient(to right, #00C9FF , #5847F9)',
      borderRadius: '10px',
      minWidth: '30px',
    },
  };

  const router = useRouter();

  const renderSection = (section: PageSection) => {
    const items = section.sectionItems || [];

    if (section.type === SectionType.CAROUSEL) {
      const carouselSlides = items
        .filter(item => !!item.previewUrl)
        .map(item => ({
          imageUrl: item.previewUrl as string,
          redirectUrl: item.metadata?.redirectUrl || '#',
          title: item.title || '',
        }));

      return <Carousel slides={carouselSlides} />;
    }

    if (section.type === SectionType.TEXT_BLOCK) {
      return <TextBlockSection title={section.title} items={items} />;
    }

    if (section.type === SectionType.BANNER) {
      const bannerItem = items.find(item => item.previewUrl);
      if (!bannerItem) return null;

      return (
        <BannerSection
          imageUrl={bannerItem.previewUrl as string}
          altText={bannerItem.title || 'Banner'}
          link={bannerItem.metadata?.redirectUrl}
        />
      );
    }

    if (section.type === SectionType.FEATURED_COLLECTION) {
      const shouldShowTitle = section.metadata?.showTitle !== false;

      return (
        <Stack flexDirection="column" gap={3} sx={{mt: '-7%'}}>
          <Stack
            flexDirection="row"
            justifyContent="space-between"
            sx={scrollGradientSx}
          >
            {shouldShowTitle && (
              <Typography variant="h4" sx={{color: '#0A0A55'}}>
                {section.title}
              </Typography>
            )}
          </Stack>
          <Stack
            flexDirection="row"
            flexWrap="nowrap"
            gap={5}
            overflow="scroll"
            sx={{pb: 2}}
          >
            {items.map(item => (
              <CollectionCard
                key={item.id}
                item={item}
                cardStyle={section.cardStyle}
              />
            ))}
          </Stack>
        </Stack>
      );
    }

    // Handle sections that display product cards
    if (PRODUCT_SECTION_TYPES.includes(section.type)) {
      const allProductVariants = items.flatMap(
        item => item.productVariants || [],
      );
      return (
        <Stack flexDirection="column" gap={3} mt={3}>
          <Stack flexDirection="row" justifyContent="space-between">
            <Typography variant="h4" sx={{color: '#0A0A55'}}>
              {section.title}
            </Typography>
            <Button
              variant="outlined"
              sx={{
                color: '#707070',
                borderColor: '#707070',
                '&:hover': {
                  borderColor: '#9E3393',
                  color: '#00004F',
                },
              }}
            >
              View More
            </Button>
          </Stack>
          <Stack
            flexDirection="row"
            flexWrap="nowrap"
            gap={5}
            overflow="scroll"
          >
            {allProductVariants.map(variant => (
              <Box
                key={variant.id}
                sx={{
                  minWidth: 240,
                  maxWidth: 300,
                  flex: '0 0 auto',
                }}
              >
                <ViewedProductCard productVariant={variant} />
              </Box>
            ))}
          </Stack>
        </Stack>
      );
    }

    if (section.type === SectionType.RECENTLY_VIEWED && isLoggedIn) {
      return recentlyViewed.length > 0 ? (
        <Stack flexDirection="column" gap={3} mt={3}>
          <Typography variant="h4" sx={{color: '#0A0A55'}}>
            {section.title}
          </Typography>
          <Stack
            flexDirection="row"
            flexWrap="nowrap"
            gap={0}
            overflow="scroll"
            sx={{'& > *': {marginRight: '-50px'}}}
          >
            {recentlyViewed.map(variant => (
              <Box
                key={variant.id}
                sx={{minWidth: 240, maxWidth: 300, flex: '0 0 auto'}}
              >
                <ProductImages productVariant={variant} />
              </Box>
            ))}
          </Stack>
        </Stack>
      ) : null;
    }

    if (section.type === SectionType.MOST_VIEWED && isLoggedIn) {
      return mostViewed.length > 0 ? (
        <Stack flexDirection="column" gap={3} mt={3}>
          <Stack flexDirection="row" justifyContent="space-between">
            <Typography variant="h4" sx={{color: '#0A0A55'}}>
              {section.title}
            </Typography>
            <Button
              variant="outlined"
              sx={{
                color: '#707070',
                borderColor: '#707070',
                '&:hover': {
                  borderColor: '#9E3393',
                  color: '#00004F',
                },
              }}
              onClick={() => router.push('/view-more?type=most-viewed')}
            >
              View More
            </Button>
          </Stack>
          <Stack
            flexDirection="row"
            flexWrap="nowrap"
            gap={5}
            overflow="scroll"
          >
            {mostViewed.map(variant => (
              <Box
                key={variant.id}
                sx={{minWidth: 240, maxWidth: 300, flex: '0 0 auto'}}
              >
                <ViewedProductCard productVariant={variant} />
              </Box>
            ))}
          </Stack>
        </Stack>
      ) : null;
    }

    if (section.type === SectionType.TOP_SELLING) {
      return topSelling.length > 0 ? (
        <Stack flexDirection="column" gap={3} mt={3}>
          <Stack flexDirection="row" justifyContent="space-between">
            <Typography variant="h4" sx={{color: '#0A0A55'}}>
              {section.title}
            </Typography>
            <Button
              variant="outlined"
              sx={{
                color: '#707070',
                borderColor: '#707070',
                '&:hover': {
                  borderColor: '#9E3393',
                  color: '#00004F',
                },
              }}
              onClick={() => router.push('/view-more?type=top-selling')}
            >
              View More
            </Button>
          </Stack>
          <Stack
            flexDirection="row"
            flexWrap="nowrap"
            gap={5}
            overflow="scroll"
            sx={{...scrollGradientSx, pb: 2}}
          >
            {topSelling.map(variant => (
              <Box
                key={variant.id}
                sx={{minWidth: 240, maxWidth: 300, flex: '0 0 auto'}}
              >
                <ViewedProductCard productVariant={variant} />
              </Box>
            ))}
          </Stack>
        </Stack>
      ) : null;
    }

    if (section.type === SectionType.PRODUCT_FILTER) {
      return <ProductFilterSection section={section} />;
    }

    if (section.type === SectionType.ALL_CATEGORIES) {
      return <AllCategoriesSection />;
    }

    if (section.type === SectionType.FACETS) {
      const shouldShowTitle = section.metadata?.showTitle !== false;

      return (
        <Stack flexDirection="column" gap={3} mt={3}>
          {shouldShowTitle && (
            <Typography variant="h4" sx={{color: '#0A0A55'}}>
              {section.title}
            </Typography>
          )}
          <Stack
            flexDirection="row"
            flexWrap="nowrap"
            gap={3}
            overflow="scroll"
            sx={{...scrollGradientSx, pb: 2}}
          >
            {items.map(item => (
              <FacetCard
                key={item.id}
                title={item.title}
                subtitle={item.subtitle}
                thumbnail={item.previewUrl as string}
                facetValueIds={item.metadata?.facetValueIds || []}
                cardStyle={section.cardStyle as CardStyle}
              />
            ))}
          </Stack>
        </Stack>
      );
    }

    if (section.type === SectionType.GIFT_PRODUCTS) {
      return (
        <GiftProductsSection
          title={section.title}
          isLoggedIn={isLoggedIn}
          user={user}
        />
      );
    }

    if (section.type === SectionType.SIMILAR_PRODUCTS && isLoggedIn) {
      return (
        <SimilarProducts
          title={section.title}
          isLoggedIn={isLoggedIn}
          user={user}
        />
      );
    }

    // Default section rendering
    return <></>;
  };

  return (
    <Box>
      {isLoading ? (
        <Typography>Loading...</Typography>
      ) : (
        <Grid container>
          <Grid item xs={12}>
            <Grid container px={3}>
              {(() => {
                return sections.map((section, index) => {
                  return (
                    <Grid item xs={12} mt={4}>
                      {renderSection(section)}
                    </Grid>
                  );
                });
              })()}
            </Grid>
          </Grid>
        </Grid>
      )}
    </Box>
  );
}
