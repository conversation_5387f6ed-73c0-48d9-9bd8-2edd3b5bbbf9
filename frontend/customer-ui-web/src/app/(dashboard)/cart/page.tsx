'use client';
import {Box, CircularProgress, Typography} from '@mui/material';
import {Stack} from '@mui/system';
import MainCard from 'components/MainCard';
import {useEffect, useState} from 'react';
import {Cart} from 'types/cart';
import {fieldsExcludeMetaFields} from 'types/api';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import {useGetActiveCartQuery} from 'redux/ecom/cartApiSlice';
import {useAppSelector} from 'redux/hooks';
import {ProductVariant} from 'types/product';
import PaymentSummary from 'views/cart/PaymentSummary';
import ProductCartCard from 'views/cart/ProductCartCard';
import {useCalculateBulkShippingMutation} from 'redux/ecom/shippingCalculationApiSlice';
import {Shipping} from 'types/shipping';

export default function CartPage() {
  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);
  const {data: user} = useGetUserQuery(undefined, {skip: !isLoggedIn});
  const [selectedShippingAddressId, setSelectedShippingAddressId] = useState<
    string | null
  >(null);
  const [shippingEstimates, setShippingEstimates] = useState<
    Record<string, Shipping>
  >({});
  const [deliveryCharge, setDeliveryCharge] = useState(0);

  const {
    data: activeCart,
    isLoading: isCartsLoading,
    refetch,
  } = useGetActiveCartQuery({
    filter: {
      include: [
        {
          relation: 'cartItems',
          fields: fieldsExcludeMetaFields,
          scope: {
            where: {deleted: false},
            fields: fieldsExcludeMetaFields,
            include: [
              {
                relation: 'productVariant',
                scope: {
                  fields: fieldsExcludeMetaFields,
                  include: [
                    {
                      relation: 'productVariantPrice',
                      scope: {
                        fields: fieldsExcludeMetaFields,
                      },
                    },
                    {
                      relation: 'product',
                      scope: {
                        fields: fieldsExcludeMetaFields,
                      },
                    },
                    {
                      relation: 'featuredAsset',
                      scope: {
                        fields: fieldsExcludeMetaFields,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          relation: 'promoCode',
        },
      ],
    },
  });
  const cartItems = activeCart?.cartItems ?? [];

  const [calculateBulkShipping] = useCalculateBulkShippingMutation();

  useEffect(() => {
    const fetchShippingRates = async () => {
      if (!selectedShippingAddressId) return;

      if (activeCart?.cartItems?.length) {
        const cartItemIds = activeCart.cartItems.map(item => item.id);
        const response = await calculateBulkShipping({
          cartItemIds,
          shippingAddressId: selectedShippingAddressId,
        }).unwrap();

        const totalShippingCost = response.reduce(
          (sum, item) => sum + (item.shippingCost || 0),
          0,
        );

        setDeliveryCharge(totalShippingCost);

        const estimateMap: Record<string, Shipping> = {};
        for (const estimate of response) {
          estimateMap[estimate?.cartItemId ?? ''] = estimate;
        }

        setShippingEstimates(estimateMap);
      }
    };

    fetchShippingRates();
  }, [activeCart, selectedShippingAddressId, calculateBulkShipping]);

  useEffect(() => {
    refetch();
  }, [refetch]);

  useEffect(() => {
    if (user?.profileId) {
      refetch();
    }
  }, [user?.profileId, refetch]);

  const isEmpty = !isCartsLoading && cartItems.length === 0;

  if (isCartsLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="60vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (isEmpty) {
    return (
      <MainCard sx={{p: 5, borderRadius: 2, textAlign: 'center'}}>
        <Typography variant="h3" fontWeight="bold">
          No items in cart.
        </Typography>
        <Typography variant="body1" mt={1}>
          Looks like your cart is empty. Start shopping to add items!
        </Typography>
      </MainCard>
    );
  }

  return (
    <Stack direction={{xs: 'column', md: 'row'}} spacing={3} sx={{p: 3}}>
      {/* Cart Items Section */}
      <Stack flex={2} spacing={2}>
        <MainCard
          title={`My Cart (${cartItems.length})`}
          sx={{
            p: 2,
            mb: 2,
            borderRadius: 2,
            boxShadow: 1,
            position: 'relative',
          }}
        >
          {cartItems.map((cartItem, index) => {
            const shippingInfo = shippingEstimates[cartItem.id];
            return (
              <ProductCartCard
                key={cartItem.id}
                product={cartItem.productVariant as ProductVariant}
                index={index}
                qty={cartItem.quantity}
                refetch={refetch}
                cartId={activeCart?.id}
                minDeliveryDate={shippingInfo?.minDeliveryDate ?? null}
                maxDeliveryDate={shippingInfo?.maxDeliveryDate ?? null}
              />
            );
          })}
        </MainCard>
      </Stack>

      {/* Payment Summary */}
      <Stack flex={1}>
        <PaymentSummary
          cart={activeCart as Cart}
          refetchCart={refetch}
          onSelectShippingAddressId={setSelectedShippingAddressId}
          deliveryCharge={deliveryCharge}
        />
      </Stack>
    </Stack>
  );
}
