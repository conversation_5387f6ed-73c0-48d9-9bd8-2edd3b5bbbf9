import * as yup from 'yup';

// Shared reusable schema for text fields inside objects
const requiredTextField = (message: string) => yup.string().required(message);

// Shared schema for optional parent object with required inner field
const optionalObjectWithRequiredField = (fieldKey: string, message: string) =>
  yup.object({ [fieldKey]: requiredTextField(message) }).optional();

export const detailsSchema = optionalObjectWithRequiredField('details', 'Please provide product details.');

export const uniquenessSchema = optionalObjectWithRequiredField('uniqueness', 'Please provide product unique features.');

export const suitabilitySchema = optionalObjectWithRequiredField(
  'suitableFor',
  'Please provide information about the product suitability.'
);

export const personalWorkSchema = optionalObjectWithRequiredField(
  'workLevel',
  'Please describe how you personalize or craft this product.'
);

export const returnPolicySchema = optionalObjectWithRequiredField('returnPolicy', 'Please fill in the return policy.');

export const termsSchema = optionalObjectWithRequiredField('terms', 'Please provide terms and conditions.');

export const disclaimerSchema = optionalObjectWithRequiredField('disclaimer', 'Please provide Disclaimer.');

export const moreInfoSchema = optionalObjectWithRequiredField('info', 'Please provide More Info.');

export const specificationSchema = yup
  .array()
  .of(
    yup.object({
      name: yup.string().required('Please provide a name for this specification.'),
      value: yup.string().required('Please provide a value for this specification.')
    })
  )
  .optional();

export const optionValueSchema = yup.object({
  id: yup.number().required('Value ID is required'),
  name: yup.string().required('Value name is required')
});

export const optionSchema = yup.object({
  id: yup.number().required('Option ID is required'),
  name: yup.string().required('Option name is required'),
  unit: yup.string().required('Option Unit is required'),
  values: yup.array().of(optionValueSchema).min(1, 'At least one option value is required').required('Option values are required')
});

export const optionsSchema = yup.array().of(optionSchema).optional();

export const variantSchema = yup.object({
  id: yup.number().required('Variant ID is required'),
  name: yup.string().required('Variant name is required'),
  price: yup.number().required('Variant Price is required').min(0, 'Price must be positive'),
  mrp: yup.number().required('Variant MRP is required').min(0, 'MRP must be positive'),
  outOfStockThreshold: yup.number().min(0, 'Out of stock threshold cannot be negative'),
  optionIds: yup.array().of(yup.number().required('Option ID is required'))
});

export const variantsSchema = yup.array().of(variantSchema).optional();

export const facetsSchema = yup.array().of(yup.string().uuid('Invalid facet ID')).optional();

export const boxContentSchema = yup.object({
  itemName: yup.string().required('Box content item is required'),
  quantity: yup.number().required('Box content quantity is required').min(1, 'Quantity must be at least 1')
});

export const boxContentsSchema = yup.array().of(boxContentSchema).optional();
