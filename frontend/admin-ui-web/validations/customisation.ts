import { FieldType } from 'types/product-dto';
import * as yup from 'yup';

export const customizationOptionSchema = yup.object({
  label: yup.string().required('Option label is required.'),
  value: yup.string().required('Option value is required.')
});

export const customizationSchema = yup.object({
  name: yup.string().required('Name is required.'),
  label: yup.string().required('Label is required.'),
  placeholder: yup.string().optional(),
  fieldType: yup
    .mixed<FieldType>() // update this based on your enum/union
    .oneOf(Object.values(FieldType))
    .required('Field type is required.'),
  isRequired: yup.boolean().required(),
  options: yup
    .array()
    .of(customizationOptionSchema)
    .when('fieldType', {
      is: (val: string) => ['select', 'radio', 'checkbox'].includes(val),
      then: (schema) => schema.min(1, 'At least one option is required.'),
      otherwise: (schema) => schema.strip() // remove if not needed
    })
});
