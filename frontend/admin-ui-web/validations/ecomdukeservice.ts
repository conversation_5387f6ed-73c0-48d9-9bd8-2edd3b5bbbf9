// import * as Yup from 'yup';
// export const serviceValidationSchema = Yup.object().shape({
//   name: Yup.string().required('Service name is required'),
//   price: Yup.number().required('Price is required').min(0, 'Price must be non-negative'),
//   currency: Yup.string().required('Currency is required'),
//   taxCategoryId: Yup.string().required('Tax category is required'),
//   isActive: Yup.boolean().required()
// });
import * as Yup from 'yup';
import { PaymentType, ServiceType } from 'enums/ecomDukeService.enum';

export const serviceValidationSchema = Yup.object().shape({
  name: Yup.string().required('Service name is required'),

  price: Yup.number().required('Price is required').min(0, 'Price must be non-negative'),

  currency: Yup.string().required('Currency is required'),

  taxCategoryId: Yup.string().required('Tax category is required'),

  isActive: Yup.boolean().required(),

  fileUploadRequired: Yup.boolean().required(),

  serviceType: Yup.string().oneOf(Object.values(ServiceType), 'Invalid service type').required('Service type is required'),

  paymentType: Yup.string().oneOf(Object.values(PaymentType), 'Invalid payment type').required('Payment type is required'),

  recurringInterval: Yup.string().when('serviceType', {
    is: ServiceType.Recurring,
    then: (schema) => schema.required('Recurring interval is required'),
    otherwise: (schema) => schema.notRequired()
  }),

  lastDateToPay: Yup.string()
    .required('Last date to pay is required')
    .test('is-date-time', 'Invalid date format', (value) => {
      return !value || !isNaN(Date.parse(value));
    }),

  firstPartAmount: Yup.number().when('paymentType', {
    is: PaymentType.Partial,
    then: (schema) => schema.required('First part amount is required').min(0, 'Must be non-negative'),
    otherwise: (schema) => schema.notRequired()
  }),

  secondPartAmount: Yup.number().when('paymentType', {
    is: PaymentType.Partial,
    then: (schema) =>
      schema
        .required('Second part amount is required')
        .min(0, 'Must be non-negative')
        .test('sum-equals-price', 'Sum of first and second part must equal total price', function (secondPartAmount) {
          const { firstPartAmount, price } = this.parent;
          const total = Number(firstPartAmount) + Number(secondPartAmount);
          return total === Number(price);
        }),
    otherwise: (schema) => schema.notRequired()
  })
});
