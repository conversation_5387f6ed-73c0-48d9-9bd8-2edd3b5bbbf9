import * as Yup from 'yup';

export const taxValidationSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  hsnCode: Yup.string().required('Tax is required'),
  taxRate: Yup.number()
    .min(0, 'Tax rate must be between 0 and 100')
    .max(100, 'Tax rate must be between 0 and 100')
    .required('TaxRate is required'),
  isDefault: Yup.boolean(),
  description: Yup.string()
});
