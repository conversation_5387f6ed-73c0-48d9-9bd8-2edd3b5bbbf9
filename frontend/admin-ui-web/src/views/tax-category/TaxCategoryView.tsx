'use client';

import React, { useEffect } from 'react';
import { Card, CardContent, Typography, Box, Grid } from '@mui/material';
import { useGetTaxCategoryByIdQuery } from 'redux/app/tax/taxCategoryApiSlice';
import Loader from 'components/Loader';

function ViewTaxCategory({ taxCategoryId }: { taxCategoryId: string }) {
  const { data, isLoading, error, refetch } = useGetTaxCategoryByIdQuery({ id: taxCategoryId });

  useEffect(() => {
    refetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isLoading) return <Loader />;
  if (error || !data) return <Typography color="error">Error fetching Tax Category</Typography>;

  return (
    <Card sx={{ p: 2, boxShadow: 3, mt: 1 }}>
      <CardContent>
        <Typography variant="h4" gutterBottom>
          Tax Category Details
        </Typography>

        <Grid container spacing={2}>
          {/* Left Column */}
          <Grid item xs={12} md={6}>
            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>Name:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                {data.name}
              </Box>
            </Typography>

            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>ID:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                {data.id}
              </Box>
            </Typography>

            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>HSN Code:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                {data.hsnCode || 'N/A'}
              </Box>
            </Typography>
          </Grid>

          {/* Right Column */}
          <Grid item xs={12} md={6}>
            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>Tax Rate (%):</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                {data.taxRate != null ? `${data.taxRate}%` : 'N/A'}
              </Box>
            </Typography>

            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>Default:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                {data.isDefault ? 'Yes' : 'No'}
              </Box>
            </Typography>
            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>Description:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                {data.description}
              </Box>
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>Created On:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                {data.createdOn ? new Date(data.createdOn).toLocaleString() : '-'}
              </Box>
            </Typography>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
}

export default ViewTaxCategory;
