import { Grid } from '@mui/material';
import MainCard from 'components/MainCard';
import { FormikProps } from 'formik';
import { FC } from 'react';
import { ProductDto } from 'types/product-dto';
import { BoxContentsFormTable } from './BoxContents';
import { SpecificationFormTable } from './Speicifications';
import { ProductVariantUpdateDto } from 'types/product-variant';

interface Props {
  formik: FormikProps<ProductDto> | FormikProps<ProductVariantUpdateDto>;
}

export const ProductMetaForm: FC<Props> = ({ formik }) => {
  return (
    <MainCard title="Product Metadata" content={false} sx={{ padding: 0, margin: 0 }}>
      <Grid container spacing={2} sx={{ p: 2 }} rowGap={2}>
        <Grid item xs={12}>
          <BoxContentsFormTable formik={formik} />
        </Grid>
        <Grid item xs={12}>
          <SpecificationFormTable formik={formik} />
        </Grid>
      </Grid>
    </MainCard>
  );
};
