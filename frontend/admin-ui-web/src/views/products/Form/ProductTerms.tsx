import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Box, Typography } from '@mui/material';
import { FormikErrors, FormikProps } from 'formik';
import { Add, Trash } from 'iconsax-react';
import { FC, useEffect, useState } from 'react';
import { ProductDto } from 'types/product-dto';
import { EditorProps } from 'react-draft-wysiwyg';
import dynamic from 'next/dynamic';
import { convertFromRaw, convertToRaw, EditorState } from 'draft-js';
import { ProductTermsAndCondition } from 'types/product';
import { ProductVariantUpdateDto } from 'types/product-variant';

const Editor = dynamic<EditorProps>(
  () => import('react-draft-wysiwyg').then((mod) => mod.Editor) as Promise<React.ComponentType<EditorProps>>,
  { ssr: false }
);
interface Props {
  formik: FormikProps<ProductDto> | FormikProps<ProductVariantUpdateDto>;
}

export const TermsFormTable: FC<Props> = ({ formik }) => {
  const [editorState, setEditorState] = useState<EditorState>(EditorState.createEmpty());
  const [showAddButton, setShowAddButton] = useState<boolean>(!Boolean(formik.values.terms));

  useEffect(() => {
    const raw = formik.values.terms?.terms;

    if (!raw) return;

    try {
      const contentState = convertFromRaw(JSON.parse(raw));
      if (!editorState.getCurrentContent().hasText()) {
        setEditorState(EditorState.createWithContent(contentState));
      }
    } catch {
      if (!editorState.getCurrentContent().hasText()) {
        setEditorState(EditorState.createEmpty());
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.values.terms]);

  const handleEditorChange = (state: EditorState) => {
    setEditorState(state);

    const content = editorState.getCurrentContent();
    if (content.hasText()) {
      formik.setFieldValue(`terms.terms`, JSON.stringify(convertToRaw(content)));
    } else {
      formik.setFieldValue('terms', { terms: '' });
    }
  };

  const handleAdd = () => {
    formik.setFieldValue('terms', { terms: '' });
    setEditorState(EditorState.createEmpty());
    setShowAddButton(false);
  };

  const handleDelete = () => {
    formik.setFieldValue('terms', undefined);
    formik.setFieldError('terms', undefined);
    formik.setFieldTouched('terms', false);
    setEditorState(EditorState.createEmpty());
    setShowAddButton(true);
  };

  return (
    <TableContainer component={Paper}>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>Terms & Conditions</TableCell>
            <TableCell width="60px" align="right">
              <IconButton size="small" color="primary" onClick={handleAdd}>
                <Add fontSize="small" />
              </IconButton>
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {!showAddButton && (
            <TableRow>
              <TableCell>
                <Box
                  sx={{
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 1,
                    p: 1,
                    minHeight: 200
                  }}
                >
                  <Editor
                    editorState={editorState}
                    onEditorStateChange={(state) => handleEditorChange(state)}
                    toolbarClassName="toolbar-class"
                    wrapperClassName="wrapper-class"
                    editorClassName="editor-class"
                    onBlur={() => {
                      formik.setFieldTouched('terms.terms', true);
                    }}
                  />
                </Box>
                {typeof formik.touched.terms === 'object' && formik.touched.terms && (
                  <Typography color="error" variant="body2" sx={{ mt: 1 }}>
                    {typeof formik.errors.terms === 'object' && (formik.errors.terms as FormikErrors<ProductTermsAndCondition>)?.terms}
                  </Typography>
                )}
              </TableCell>
              <TableCell align="right">
                <IconButton size="small" color="error" onClick={() => handleDelete()}>
                  <Trash fontSize="small" />
                </IconButton>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
