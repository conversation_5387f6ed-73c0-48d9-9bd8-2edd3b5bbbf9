import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Paper, IconButton } from '@mui/material';
import { FormikProps } from 'formik';
import { Add, Trash } from 'iconsax-react';
import { FC } from 'react';
import { ProductDto } from 'types/product-dto';
import { ProductVariantUpdateDto } from 'types/product-variant';

interface Props {
  formik: FormikProps<ProductDto> | FormikProps<ProductVariantUpdateDto>;
}

export const BoxContentsFormTable: FC<Props> = ({ formik }) => {
  const handleDelete = (index: number) => {
    const updatedBoxContents = [...formik.values.boxContents];
    updatedBoxContents.splice(index, 1);
    formik.setFieldValue('boxContents', updatedBoxContents);
  };

  const handleAdd = () => {
    const updatedBoxContents = [...formik.values.boxContents, { boxContents: '' }];
    formik.setFieldValue('boxContents', updatedBoxContents);
  };

  return (
    <TableContainer component={Paper}>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>Box Content</TableCell>
            <TableCell></TableCell>
            <TableCell width="60px" align="right">
              <IconButton size="small" color="primary" onClick={handleAdd}>
                <Add fontSize="small" />
              </IconButton>
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {formik.values.boxContents.map((boxContents, idx) => (
            <TableRow key={idx}>
              <TableCell>
                <TextField
                  fullWidth
                  name={`boxContents[${idx}].itemName`}
                  value={boxContents.itemName}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  size="small"
                  error={
                    !!formik.touched.boxContents?.[idx]?.itemName &&
                    typeof formik.errors.boxContents?.[idx] === 'object' &&
                    !!formik.errors.boxContents?.[idx]?.itemName
                  }
                  helperText={
                    formik.touched.boxContents?.[idx]?.itemName &&
                    typeof formik.errors.boxContents?.[idx] === 'object' &&
                    formik.errors.boxContents?.[idx]?.itemName
                  }
                  placeholder="Enter Item name"
                />
              </TableCell>
              <TableCell>
                <TextField
                  fullWidth
                  name={`boxContents[${idx}].quantity`}
                  value={boxContents.quantity}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  size="small"
                  error={
                    !!formik.touched.boxContents?.[idx]?.quantity &&
                    typeof formik.errors.boxContents?.[idx] === 'object' &&
                    !!formik.errors.boxContents?.[idx]?.quantity
                  }
                  helperText={
                    formik.touched.boxContents?.[idx]?.quantity &&
                    typeof formik.errors.boxContents?.[idx] === 'object' &&
                    formik.errors.boxContents?.[idx]?.quantity
                  }
                  placeholder="Enter Quantity"
                />
              </TableCell>
              <TableCell align="right">
                <IconButton size="small" color="error" onClick={() => handleDelete(idx)}>
                  <Trash fontSize="small" />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
