'use client';

import { Box, IconButton, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from '@mui/material';
import { FormikErrors, FormikProps } from 'formik';
import dynamic from 'next/dynamic';
import { FC, useEffect, useState } from 'react';
import { convertFromRaw, convertToRaw, EditorState } from 'draft-js';
import { EditorProps } from 'react-draft-wysiwyg';
import { Add, Trash } from 'iconsax-react';
import { ProductDto } from 'types/product-dto';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import { ProductDetail } from 'types/product';
import { ProductVariantUpdateDto } from 'types/product-variant';

const Editor = dynamic<EditorProps>(
  () => import('react-draft-wysiwyg').then((mod) => mod.Editor) as Promise<React.ComponentType<EditorProps>>,
  { ssr: false }
);

interface Props {
  formik: FormikProps<ProductDto> | FormikProps<ProductVariantUpdateDto>;
}

export const ProductDetailFormTable: FC<Props> = ({ formik }) => {
  const [editorState, setEditorState] = useState<EditorState>(EditorState.createEmpty());
  const [showAddButton, setShowAddButton] = useState<boolean>(!Boolean(formik.values.details));

  useEffect(() => {
    const raw = formik.values.details?.details;

    if (!raw) return;

    try {
      const contentState = convertFromRaw(JSON.parse(raw));
      if (!editorState.getCurrentContent().hasText()) {
        setEditorState(EditorState.createWithContent(contentState));
      }
    } catch {
      if (!editorState.getCurrentContent().hasText()) {
        setEditorState(EditorState.createEmpty());
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.values.details]);

  const handleEditorChange = (state: EditorState) => {
    setEditorState(state);

    const content = editorState.getCurrentContent();
    if (content.hasText()) {
      formik.setFieldValue(`details.details`, JSON.stringify(convertToRaw(content)));
    } else {
      formik.setFieldValue('details', { details: '' });
    }
  };

  const handleAdd = () => {
    formik.setFieldValue('details', { details: '' });
    setEditorState(EditorState.createEmpty());
    setShowAddButton(false);
  };

  const handleDelete = () => {
    formik.setFieldValue('details', undefined);
    formik.setFieldError('details', undefined);
    formik.setFieldTouched('details', false);
    setEditorState(EditorState.createEmpty());
    setShowAddButton(true);
  };

  return (
    <TableContainer component={Paper}>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>Product Details</TableCell>
            <TableCell width="60px" align="right">
              {showAddButton && (
                <IconButton size="small" color="primary" onClick={handleAdd} aria-label="Add product detail">
                  <Add fontSize="small" />
                </IconButton>
              )}
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {!showAddButton && (
            <TableRow>
              <TableCell>
                <Box
                  sx={{
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 1,
                    p: 1,
                    minHeight: 200
                  }}
                >
                  <Editor
                    editorState={editorState}
                    onEditorStateChange={(state) => handleEditorChange(state)}
                    toolbarClassName="toolbar-class"
                    wrapperClassName="wrapper-class"
                    editorClassName="editor-class"
                    onBlur={() => {
                      formik.setFieldTouched('details.details', true);
                    }}
                  />
                </Box>
                {typeof formik.touched.details === 'object' && formik.touched.details && (
                  <Typography color="error" variant="body2" sx={{ mt: 1 }}>
                    {typeof formik.errors.details === 'object' && (formik.errors.details as FormikErrors<ProductDetail>)?.details}
                  </Typography>
                )}
              </TableCell>
              <TableCell align="right">
                <IconButton size="small" color="error" onClick={() => handleDelete()} aria-label="Delete product detail">
                  <Trash fontSize="small" />
                </IconButton>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
