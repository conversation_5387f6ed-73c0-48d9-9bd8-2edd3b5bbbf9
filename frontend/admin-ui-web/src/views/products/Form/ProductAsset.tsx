import { Box, But<PERSON>, CardActions, CardContent, Checkbox, ImageList, ImageListItem, Modal, Stack } from '@mui/material';
import MainCard from 'components/MainCard';
import Image from 'next/image';
import { Dispatch, FC, SetStateAction, useState } from 'react';
import { Asset } from 'types/product';
import { AddAsset } from './AddAsset';
import { LoadingButton } from '@mui/lab';
import CropImageModal from 'components/third-party/CropModal';
import InfiniteScroll from 'react-infinite-scroll-component';
import Loader from 'components/Loader';
import LazyVideo from './LazyVideo';

interface Props {
  imageUrl?: string;
  altText?: string;
  openGallery: boolean;
  assets: Asset[];
  toggleGallery: () => void;
  handleFileChange: (e: File) => void;
  isAssetUploading: boolean;
  setSelectedAssets: Dispatch<SetStateAction<{ id: string; preview: string; mimeType: string }[]>>;
  selectedAssets: { id: string; preview: string; mimeType: string }[];
  handleFeatureAssetChange: (id: string) => void;
  featuredAssetId: string;
  assetsCount: number;
  hasMore: boolean;
  loadMoreAssets: () => void;
}

export const ProductAsset: FC<Props> = ({
  imageUrl,
  altText,
  openGallery,
  assets,
  toggleGallery,
  handleFileChange,
  isAssetUploading,
  setSelectedAssets,
  selectedAssets,
  handleFeatureAssetChange,
  featuredAssetId,
  assetsCount,
  hasMore,
  loadMoreAssets
}) => {
  const [cropModalOpen, setCropModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [, setFileToCrop] = useState<File | null>(null);

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const isImage = file.type.startsWith('image');
      if (isImage) {
        const reader = new FileReader();
        reader.onload = () => {
          setSelectedImage(reader.result as string);
          setFileToCrop(file);
          setCropModalOpen(true);
        };
        reader.readAsDataURL(file);
      } else {
        handleFileChange(file);
      }
    }
  };

  const handleToggle = (id: string, preview: string, mimeType: string) => {
    setSelectedAssets((prev) => {
      const exists = prev.some((item) => item.id === id);
      if (exists) return prev.filter((item) => item.id !== id);
      return [...prev, { id, preview, mimeType }];
    });
  };
  const featuredAsset = selectedAssets.find((a) => a.id === featuredAssetId);

  return (
    <MainCard title="Medias">
      <Stack direction="row" spacing={2} alignItems="flex-start">
        <Box
          sx={{
            width: 210,
            height: 210,
            position: 'relative',
            borderRadius: 1,
            overflow: 'hidden',
            border: '1px solid #ccc'
          }}
        >
          {featuredAsset ? (
            featuredAsset.mimeType.startsWith('image') ? (
              <Image src={featuredAsset.preview} alt={altText ?? 'Featured Media'} fill style={{ objectFit: 'cover' }} />
            ) : (
              <LazyVideo
                key={featuredAsset.id}
                src={featuredAsset.preview}
                type={featuredAsset.mimeType}
                width="100%"
                height="100%"
                autoPlay={true}
                muted={true}
                loop={true}
                controls={true}
                style={{
                  objectFit: 'contain'
                }}
              />
            )
          ) : (
            <Image
              src={imageUrl ?? '/assets/images/icons/image_placeholder.png'}
              alt={altText ?? 'Featured Media'}
              fill
              style={{ objectFit: 'cover' }}
            />
          )}
        </Box>

        <Stack spacing={1}>
          <Stack direction="row" spacing={1}>
            {selectedAssets.map((item) => {
              const isFeatured = featuredAssetId === item.id;
              const isImage = item.mimeType?.startsWith('image');

              return (
                <Box
                  key={item.id}
                  onClick={() => handleFeatureAssetChange(item.id)}
                  sx={{
                    width: 50,
                    height: 50,
                    position: 'relative',
                    borderRadius: 1,
                    overflow: 'hidden',
                    border: isFeatured ? '2px solid #1976d2' : '1px solid #ccc',
                    cursor: 'pointer'
                  }}
                >
                  {isImage ? (
                    <Image src={item.preview} alt={item.id} fill style={{ objectFit: 'cover' }} />
                  ) : (
                    <LazyVideo src={item.preview} type={item.mimeType} width={50} height={50} controls={false} muted />
                  )}
                </Box>
              );
            })}
          </Stack>
          <Stack direction="row" justifyContent="flex-end">
            <LoadingButton variant="outlined" onClick={toggleGallery} loading={isAssetUploading}>
              Add Media
            </LoadingButton>
          </Stack>
        </Stack>
      </Stack>

      <Modal open={openGallery} onClose={toggleGallery}>
        <MainCard
          title="Choose Media"
          modal
          darkTitle
          content={false}
          secondary={<AddAsset handleChange={handleImageSelect} />}
          sx={{ width: 600 }}
        >
          <CardContent
            id="scrollableAssets"
            sx={{
              height: 300,
              overflowY: 'auto'
            }}
          >
            <InfiniteScroll
              dataLength={assets.length}
              loader={<Loader />}
              next={loadMoreAssets}
              hasMore={hasMore}
              scrollableTarget="scrollableAssets"
            >
              <ImageList sx={{ width: 500 }} cols={4} rowHeight={164}>
                {assets.map((item) => {
                  const isSelected = selectedAssets.some((asset) => asset.id === item.id);
                  const isImage = item.mimeType.startsWith('image');
                  const imageSelected = selectedAssets.some((a) => a.mimeType.startsWith('image'));
                  const isDisabled = !isImage && !imageSelected;

                  return (
                    <ImageListItem
                      key={item.id}
                      onClick={() => {
                        if (!isDisabled) handleToggle(item.id, item.previewUrl, item.mimeType);
                      }}
                      sx={{
                        position: 'relative',
                        border: isSelected && !isDisabled ? '2px solid #1976d2' : '2px solid transparent',
                        borderRadius: 1,
                        cursor: isDisabled ? 'not-allowed' : 'pointer',
                        overflow: 'hidden',
                        opacity: isDisabled ? 0.5 : 1,
                        pointerEvents: isDisabled ? 'none' : 'auto' // disables click
                      }}
                    >
                      {isImage ? (
                        <Image
                          width={100}
                          height={100}
                          src={item.previewUrl}
                          alt={item.name}
                          loading="lazy"
                          style={{ objectFit: 'cover' }}
                        />
                      ) : (
                        <LazyVideo src={item.previewUrl} type={item.mimeType} width={100} height={100} controls={false} muted />
                      )}
                      <Box
                        sx={{
                          position: 'absolute',
                          backgroundColor: 'rgba(255,255,255,0.7)',
                          borderRadius: '50%'
                        }}
                      >
                        <Checkbox checked={isSelected} sx={{ padding: 0.5 }} color="primary" />
                      </Box>
                    </ImageListItem>
                  );
                })}
              </ImageList>
            </InfiniteScroll>
          </CardContent>
          <CardActions sx={{ justifyContent: 'flex-end' }}>
            <Button onClick={toggleGallery} color="secondary" variant="outlined">
              Cancel
            </Button>
            <LoadingButton variant="outlined" onClick={toggleGallery} loading={isAssetUploading} disabled={selectedAssets.length === 0}>
              {selectedAssets.length ? `Add ${selectedAssets.length} Media` : 'Add Media'}
            </LoadingButton>
          </CardActions>
        </MainCard>
      </Modal>

      {selectedImage && (
        <CropImageModal
          open={cropModalOpen}
          onClose={() => setCropModalOpen(false)}
          onCropComplete={async (croppedFile) => {
            if (croppedFile) {
              await handleFileChange(croppedFile);
            }
          }}
          imageSrc={selectedImage}
        />
      )}
    </MainCard>
  );
};
