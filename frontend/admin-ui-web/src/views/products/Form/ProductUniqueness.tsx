'use client';

import { Box, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography } from '@mui/material';
import { FormikErrors, FormikProps } from 'formik';
import dynamic from 'next/dynamic';
import { FC, useEffect, useState } from 'react';
import { convertFromRaw, convertToRaw, EditorState } from 'draft-js';
import { EditorProps } from 'react-draft-wysiwyg';
import { Add, Trash } from 'iconsax-react';
import { ProductDto, ProductUniqueness } from 'types/product-dto';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import { ProductVariantUpdateDto } from 'types/product-variant';

const Editor = dynamic<EditorProps>(
  () => import('react-draft-wysiwyg').then((mod) => mod.Editor) as Promise<React.ComponentType<EditorProps>>,
  { ssr: false }
);
interface Props {
  formik: FormikProps<ProductDto> | FormikProps<ProductVariantUpdateDto>;
}

export const UniquenessForm: FC<Props> = ({ formik }) => {
  const [editorState, setEditorState] = useState<EditorState>(EditorState.createEmpty());
  const [showAddButton, setShowAddButton] = useState<boolean>(!Boolean(formik.values.uniqueness));

  useEffect(() => {
    const raw = formik.values.uniqueness?.uniqueness;

    if (!raw) return;

    try {
      const contentState = convertFromRaw(JSON.parse(raw));
      if (!editorState.getCurrentContent().hasText()) {
        setEditorState(EditorState.createWithContent(contentState));
      }
    } catch {
      if (!editorState.getCurrentContent().hasText()) {
        setEditorState(EditorState.createEmpty());
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.values.uniqueness]);

  const handleEditorChange = (state: EditorState) => {
    setEditorState(state);

    const content = editorState.getCurrentContent();
    if (content.hasText()) {
      formik.setFieldValue(`uniqueness.uniqueness`, JSON.stringify(convertToRaw(content)));
    } else {
      formik.setFieldValue('uniqueness', { uniqueness: '' });
    }
  };

  const handleAdd = () => {
    formik.setFieldValue('uniqueness', { uniqueness: '' });
    setEditorState(EditorState.createEmpty());
    setShowAddButton(false);
  };

  const handleDelete = () => {
    formik.setFieldValue('uniqueness', undefined);
    formik.setFieldError('uniqueness', undefined);
    formik.setFieldTouched('uniqueness', false);
    setEditorState(EditorState.createEmpty());
    setShowAddButton(true);
  };

  return (
    <TableContainer component={Paper}>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>What make this product unique?</TableCell>
            <TableCell align="right">
              {showAddButton && (
                <IconButton size="small" color="primary" onClick={handleAdd}>
                  <Add size="16" />
                </IconButton>
              )}
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {!showAddButton && (
            <TableRow>
              <TableCell>
                <Box
                  sx={{
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 1,
                    p: 1,
                    minHeight: 200
                  }}
                >
                  <Editor
                    editorState={editorState}
                    onEditorStateChange={(state) => handleEditorChange(state)}
                    toolbarClassName="toolbar-class"
                    wrapperClassName="wrapper-class"
                    editorClassName="editor-class"
                    placeholder="What makes your product stand out? Share the special materials, techniques, stories, or design elements that make it one-of-a-kind."
                    onBlur={() => {
                      formik.setFieldTouched('uniqueness.uniqueness', true);
                    }}
                  />
                </Box>
                {typeof formik.touched.uniqueness === 'object' && formik.touched.uniqueness && (
                  <Typography color="error" variant="body2" sx={{ mt: 1 }}>
                    {typeof formik.errors.uniqueness === 'object' &&
                      (formik.errors.uniqueness as FormikErrors<ProductUniqueness>)?.uniqueness}
                  </Typography>
                )}
              </TableCell>
              <TableCell align="right">
                <IconButton size="small" color="error" onClick={() => handleDelete()}>
                  <Trash fontSize="small" />
                </IconButton>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
