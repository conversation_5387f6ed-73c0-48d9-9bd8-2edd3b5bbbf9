import { Button, Grid } from '@mui/material';
import MainCard from 'components/MainCard';
import { FormikProps } from 'formik';
import { FC } from 'react';
import { FieldType, ProductDto } from 'types/product-dto';
import { CustomizationForm } from './CustomizationForm';
import { ProductVariantUpdateDto } from 'types/product-variant';
interface ProductVariantFormProps {
  formik: FormikProps<ProductDto> | FormikProps<ProductVariantUpdateDto>;
}
export const ProductCustomizationForm: FC<ProductVariantFormProps> = ({ formik }) => {
  const handleAddCustomization = () => {
    formik.setFieldValue('customizations', [
      ...formik.values.customizations,
      {
        name: '',
        label: '',
        placeholder: '',
        fieldType: FieldType.TEXT,
        isRequired: false,
        options: [],
        values: []
      }
    ]);
  };

  return (
    <MainCard title="Product Customization" content={false} sx={{ padding: 0, margin: 0 }}>
      <Grid container spacing={2} sx={{ p: 2 }} rowGap={2}>
        <Grid item xs={12}>
          <Button variant="outlined" onClick={handleAddCustomization}>
            Add Customization
          </Button>
        </Grid>
        {!!formik.values.customizations?.length && (
          <Grid item xs={12}>
            <CustomizationForm formik={formik} />
          </Grid>
        )}
      </Grid>
    </MainCard>
  );
};
