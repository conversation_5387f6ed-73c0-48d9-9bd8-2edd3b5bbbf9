'use client';

import { Box, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography } from '@mui/material';
import { FormikErrors, FormikProps } from 'formik';
import dynamic from 'next/dynamic';
import { FC, useEffect, useState } from 'react';
import { convertFromRaw, convertToRaw, EditorState } from 'draft-js';
import { EditorProps } from 'react-draft-wysiwyg';
import { Add, Trash } from 'iconsax-react';
import { ProductDto, ProductPersonalWork } from 'types/product-dto';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import { ProductVariantUpdateDto } from 'types/product-variant';

const Editor = dynamic<EditorProps>(
  () => import('react-draft-wysiwyg').then((mod) => mod.Editor) as Promise<React.ComponentType<EditorProps>>,
  { ssr: false }
);
interface Props {
  formik: FormikProps<ProductDto> | FormikProps<ProductVariantUpdateDto>;
}

export const PersonalizedInfoForm: FC<Props> = ({ formik }) => {
  const [editorState, setEditorState] = useState<EditorState>(EditorState.createEmpty());
  const [showAddButton, setShowAddButton] = useState<boolean>(!Boolean(formik.values.personalWork));

  useEffect(() => {
    const raw = formik.values.personalWork?.workLevel;

    if (!raw) return;

    try {
      const contentState = convertFromRaw(JSON.parse(raw));
      if (!editorState.getCurrentContent().hasText()) {
        setEditorState(EditorState.createWithContent(contentState));
      }
    } catch {
      if (!editorState.getCurrentContent().hasText()) {
        setEditorState(EditorState.createEmpty());
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.values.personalWork]);

  const handleEditorChange = (state: EditorState) => {
    setEditorState(state);

    const content = editorState.getCurrentContent();
    if (content.hasText()) {
      formik.setFieldValue(`personalWork.workLevel`, JSON.stringify(convertToRaw(content)));
    } else {
      formik.setFieldValue('personalWork', { workLevel: '' });
    }
  };

  const handleAdd = () => {
    formik.setFieldValue('personalWork', { workLevel: '' });
    setEditorState(EditorState.createEmpty());
    setShowAddButton(false);
  };

  const handleDelete = () => {
    formik.setFieldValue('personalWork', undefined);
    formik.setFieldError('personalWork', undefined);
    formik.setFieldTouched('personalWork', false);
    setEditorState(EditorState.createEmpty());
    setShowAddButton(true);
  };

  return (
    <TableContainer component={Paper}>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>Crafting or Customization Info</TableCell>
            <TableCell align="right">
              {showAddButton && (
                <IconButton size="small" color="primary" onClick={handleAdd}>
                  <Add size="16" />
                </IconButton>
              )}
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {!showAddButton && (
            <TableRow>
              <TableCell>
                <Box
                  sx={{
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 1,
                    p: 1,
                    minHeight: 200
                  }}
                >
                  <Editor
                    editorState={editorState}
                    onEditorStateChange={(state) => handleEditorChange(state)}
                    toolbarClassName="toolbar-class"
                    wrapperClassName="wrapper-class"
                    editorClassName="editor-class"
                    placeholder="Describe how you personalize or craft this product — e.g., handmade embroidery, custom engraving, or unique design steps."
                    onBlur={() => {
                      formik.setFieldTouched('personalWork.workLevel', true);
                    }}
                  />
                </Box>
                {typeof formik.touched.personalWork === 'object' && formik.touched.personalWork && (
                  <Typography color="error" variant="body2" sx={{ mt: 1 }}>
                    {typeof formik.errors.personalWork === 'object' &&
                      (formik.errors.personalWork as FormikErrors<ProductPersonalWork>)?.workLevel}
                  </Typography>
                )}
              </TableCell>
              <TableCell align="right">
                <IconButton size="small" color="error" onClick={() => handleDelete()}>
                  <Trash fontSize="small" />
                </IconButton>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
