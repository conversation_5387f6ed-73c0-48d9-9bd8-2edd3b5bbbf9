import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Paper, IconButton } from '@mui/material';
import { FormikProps } from 'formik';
import { Add, Trash } from 'iconsax-react';
import { FC } from 'react';
import { ProductDto } from 'types/product-dto';
import { ProductVariantUpdateDto } from 'types/product-variant';

interface Props {
  formik: FormikProps<ProductDto> | FormikProps<ProductVariantUpdateDto>;
}

export const SpecificationFormTable: FC<Props> = ({ formik }) => {
  const handleDelete = (index: number) => {
    const updatedspecifications = [...formik.values.specifications];
    updatedspecifications.splice(index, 1);
    formik.setFieldValue('specifications', updatedspecifications);
  };

  const handleAdd = () => {
    const updatedspecifications = [...formik.values.specifications, { specifications: '' }];
    formik.setFieldValue('specifications', updatedspecifications);
  };

  return (
    <TableContainer component={Paper}>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>Specifications</TableCell>
            <TableCell></TableCell>
            <TableCell width="60px" align="right">
              <IconButton size="small" color="primary" onClick={handleAdd}>
                <Add fontSize="small" />
              </IconButton>
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {formik.values.specifications.map((specification, idx) => (
            <TableRow key={idx}>
              <TableCell>
                <TextField
                  fullWidth
                  name={`specifications[${idx}].name`}
                  value={specification.name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  size="small"
                  error={
                    !!formik.touched.specifications?.[idx]?.name &&
                    typeof formik.errors.specifications?.[idx] === 'object' &&
                    !!formik.errors.specifications?.[idx]?.name
                  }
                  helperText={
                    formik.touched.specifications?.[idx]?.name &&
                    typeof formik.errors.specifications?.[idx] === 'object' &&
                    formik.errors.specifications?.[idx]?.name
                  }
                  placeholder="Enter Item name"
                />
              </TableCell>
              <TableCell>
                <TextField
                  fullWidth
                  name={`specifications[${idx}].value`}
                  value={specification.value}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  size="small"
                  error={
                    !!formik.touched.specifications?.[idx]?.value &&
                    typeof formik.errors.specifications?.[idx] === 'object' &&
                    !!formik.errors.specifications?.[idx]?.value
                  }
                  helperText={
                    formik.touched.specifications?.[idx]?.value &&
                    typeof formik.errors.specifications?.[idx] === 'object' &&
                    formik.errors.specifications?.[idx]?.value
                  }
                  placeholder="Enter Value"
                />
              </TableCell>
              <TableCell align="right">
                <IconButton size="small" color="error" onClick={() => handleDelete(idx)}>
                  <Trash fontSize="small" />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
