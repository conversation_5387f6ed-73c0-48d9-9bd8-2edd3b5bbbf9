'use client';

import { Box, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography } from '@mui/material';
import { FormikErrors, FormikProps } from 'formik';
import dynamic from 'next/dynamic';
import { FC, useEffect, useState } from 'react';
import { convertFromRaw, convertToRaw, EditorState } from 'draft-js';
import { EditorProps } from 'react-draft-wysiwyg';
import { Add, Trash } from 'iconsax-react';
import { ProductDto } from 'types/product-dto';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import { ProductDisclaimer } from 'types/product';
import { ProductVariantUpdateDto } from 'types/product-variant';

const Editor = dynamic<EditorProps>(
  () => import('react-draft-wysiwyg').then((mod) => mod.Editor) as Promise<React.ComponentType<EditorProps>>,
  { ssr: false }
);
interface Props {
  formik: FormikProps<ProductDto> | FormikProps<ProductVariantUpdateDto>;
}

export const DisclaimerForm: FC<Props> = ({ formik }) => {
  const [editorState, setEditorState] = useState<EditorState>(EditorState.createEmpty());
  const [showAddButton, setShowAddButton] = useState<boolean>(!Boolean(formik.values.disclaimer));

  useEffect(() => {
    const raw = formik.values.disclaimer?.disclaimer;

    if (!raw) return;

    try {
      const contentState = convertFromRaw(JSON.parse(raw));
      if (!editorState.getCurrentContent().hasText()) {
        setEditorState(EditorState.createWithContent(contentState));
      }
    } catch {
      if (!editorState.getCurrentContent().hasText()) {
        setEditorState(EditorState.createEmpty());
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.values.disclaimer]);

  const handleEditorChange = (state: EditorState) => {
    setEditorState(state);

    const content = editorState.getCurrentContent();
    if (content.hasText()) {
      formik.setFieldValue(`disclaimer.disclaimer`, JSON.stringify(convertToRaw(content)));
    } else {
      formik.setFieldValue('disclaimer', { disclaimer: '' });
    }
  };

  const handleAdd = () => {
    formik.setFieldValue('disclaimer', { disclaimer: '' });
    setEditorState(EditorState.createEmpty());
    setShowAddButton(false);
  };

  const handleDelete = () => {
    formik.setFieldValue('disclaimer', undefined);
    formik.setFieldError('disclaimer', undefined);
    formik.setFieldTouched('disclaimer', false);
    setEditorState(EditorState.createEmpty());
    setShowAddButton(true);
  };

  return (
    <TableContainer component={Paper}>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>Product Disclaimers</TableCell>
            <TableCell align="right">
              {showAddButton && (
                <IconButton size="small" color="primary" onClick={handleAdd}>
                  <Add size="16" />
                </IconButton>
              )}
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {!showAddButton && (
            <TableRow>
              <TableCell>
                <Box
                  sx={{
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 1,
                    p: 1,
                    minHeight: 200
                  }}
                >
                  <Editor
                    editorState={editorState}
                    onEditorStateChange={(state) => handleEditorChange(state)}
                    toolbarClassName="toolbar-class"
                    wrapperClassName="wrapper-class"
                    editorClassName="editor-class"
                    onBlur={() => {
                      formik.setFieldTouched('disclaimer.disclaimer', true);
                    }}
                  />
                </Box>
                {typeof formik.touched.disclaimer === 'object' && formik.touched.disclaimer && (
                  <Typography color="error" variant="body2" sx={{ mt: 1 }}>
                    {typeof formik.errors.disclaimer === 'object' &&
                      (formik.errors.disclaimer as FormikErrors<ProductDisclaimer>)?.disclaimer}
                  </Typography>
                )}
              </TableCell>
              <TableCell align="right">
                <IconButton size="small" color="error" onClick={() => handleDelete()}>
                  <Trash fontSize="small" />
                </IconButton>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
