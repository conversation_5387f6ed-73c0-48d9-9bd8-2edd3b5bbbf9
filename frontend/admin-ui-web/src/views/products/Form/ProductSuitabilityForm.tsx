'use client';

import { Box, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography } from '@mui/material';
import { FormikErrors, FormikProps } from 'formik';
import dynamic from 'next/dynamic';
import { FC, useEffect, useState } from 'react';
import { convertFromRaw, convertToRaw, EditorState } from 'draft-js';
import { EditorProps } from 'react-draft-wysiwyg';
import { Add, Trash } from 'iconsax-react';
import { ProductDto, ProductSuitability } from 'types/product-dto';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import { ProductVariantUpdateDto } from 'types/product-variant';

const Editor = dynamic<EditorProps>(
  () => import('react-draft-wysiwyg').then((mod) => mod.Editor) as Promise<React.ComponentType<EditorProps>>,
  { ssr: false }
);
interface Props {
  formik: FormikProps<ProductDto> | FormikProps<ProductVariantUpdateDto>;
}

export const SuitabilityForm: FC<Props> = ({ formik }) => {
  const [editorState, setEditorState] = useState<EditorState>(EditorState.createEmpty());
  const [showAddButton, setShowAddButton] = useState<boolean>(!Boolean(formik.values.suitability));

  useEffect(() => {
    const raw = formik.values.suitability?.suitableFor;

    if (!raw) return;

    try {
      const contentState = convertFromRaw(JSON.parse(raw));
      if (!editorState.getCurrentContent().hasText()) {
        setEditorState(EditorState.createWithContent(contentState));
      }
    } catch {
      if (!editorState.getCurrentContent().hasText()) {
        setEditorState(EditorState.createEmpty());
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.values.suitability]);

  const handleEditorChange = (state: EditorState) => {
    setEditorState(state);

    const content = editorState.getCurrentContent();
    if (content.hasText()) {
      formik.setFieldValue(`suitability.suitableFor`, JSON.stringify(convertToRaw(content)));
    } else {
      formik.setFieldValue('suitability', { suitableFor: '' });
    }
  };

  const handleAdd = () => {
    formik.setFieldValue('suitability', { suitableFor: '' });
    setEditorState(EditorState.createEmpty());
    setShowAddButton(false);
  };

  const handleDelete = () => {
    formik.setFieldValue('suitability', undefined);
    formik.setFieldError('suitability', undefined);
    formik.setFieldTouched('suitability', false);
    setEditorState(EditorState.createEmpty());
    setShowAddButton(true);
  };

  return (
    <TableContainer component={Paper}>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>Product suitabe for?</TableCell>
            <TableCell align="right">
              {showAddButton && (
                <IconButton size="small" color="primary" onClick={handleAdd}>
                  <Add size="16" />
                </IconButton>
              )}
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {(!showAddButton ? [editorState] : []).map((state, index) => (
            <TableRow key={index}>
              <TableCell>
                <Box
                  sx={{
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 1,
                    p: 1,
                    minHeight: 200
                  }}
                >
                  <Editor
                    editorState={state}
                    onEditorStateChange={(_state) => handleEditorChange(_state)}
                    toolbarClassName="toolbar-class"
                    wrapperClassName="wrapper-class"
                    editorClassName="editor-class"
                    placeholder="Who is this product perfect for? Mention suitable occasions, age groups, lifestyles, or needs — e.g., ideal for pet lovers, great for wedding gifts, or perfect for daily use."
                    onBlur={() => {
                      formik.setFieldTouched('suitability.suitableFor', true);
                    }}
                  />
                </Box>
                {typeof formik.touched.suitability === 'object' && formik.touched.suitability && (
                  <Typography color="error" variant="body2" sx={{ mt: 1 }}>
                    {typeof formik.errors.suitability === 'object' &&
                      (formik.errors.suitability as FormikErrors<ProductSuitability>)?.suitableFor}
                  </Typography>
                )}
              </TableCell>
              <TableCell align="right">
                <IconButton size="small" color="error" onClick={() => handleDelete()}>
                  <Trash fontSize="small" />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
