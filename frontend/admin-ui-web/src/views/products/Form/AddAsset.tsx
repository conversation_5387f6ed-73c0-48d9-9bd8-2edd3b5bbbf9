import { LoadingButton } from '@mui/lab';
import { Stack } from '@mui/material';
import { ChangeEvent, FC } from 'react';
interface Props {
  handleChange: (e: ChangeEvent<HTMLInputElement>) => void;
}
export const AddAsset: FC<Props> = ({ handleChange }) => {
  return (
    <Stack alignItems="center">
      <LoadingButton size="small" variant="outlined" component="label">
        Add Media
        <input
          type="file"
          hidden
          onChange={handleChange}
          accept="image/png, image/jpeg, image/jpg, image/webp, video/mp4, video/webm, video/ogg"
          multiple
        />{' '}
      </LoadingButton>
    </Stack>
  );
};
