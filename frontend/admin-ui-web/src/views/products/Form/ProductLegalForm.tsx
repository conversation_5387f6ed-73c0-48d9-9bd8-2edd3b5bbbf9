import { Grid } from '@mui/material';
import MainCard from 'components/MainCard';
import { FormikProps } from 'formik';
import { FC } from 'react';
import { ProductDto } from 'types/product-dto';
import { ReturnPolicyForm } from './ReturnPolicy';
import { TermsFormTable } from './ProductTerms';
import { DisclaimerForm } from './ProductDisclaimer';
import { ProductVariantUpdateDto } from 'types/product-variant';

interface Props {
  formik: FormikProps<ProductDto> | FormikProps<ProductVariantUpdateDto>;
}

export const ProductLegalForm: FC<Props> = ({ formik }) => {
  return (
    <MainCard title="Product Legal Information" content={false} sx={{ padding: 0, margin: 0 }}>
      <Grid container spacing={2} sx={{ p: 2 }} rowGap={2}>
        <Grid item xs={12}>
          <ReturnPolicyForm formik={formik} />
        </Grid>
        <Grid item xs={12}>
          <TermsFormTable formik={formik} />
        </Grid>
        <Grid item xs={12}>
          <DisclaimerForm formik={formik} />
        </Grid>
      </Grid>
    </MainCard>
  );
};
