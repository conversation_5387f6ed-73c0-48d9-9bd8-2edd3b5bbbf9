'use client';

import {
  Box,
  Button,
  Grid,
  TextField,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
  useTheme,
  useMediaQuery,
  InputAdornment,
  CircularProgress
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useEffect, useState, useMemo } from 'react';

import { Gender } from 'enums/customer.enum';
import { extractCountryCode } from 'utils/countryCode';
import { useApiErrorHandler } from 'hooks/useApiErrorHandler';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { useGetAdminsByIdQuery, useUpdateAdminMutation } from 'redux/app/admin/adminApiSlice';

import { useAuth } from 'contexts/AuthContext';

export default function ProfileForm() {
  const [editableFields, setEditableFields] = useState({
    name: false,
    email: false,
    phoneNumber: false
  });

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const handleError = useApiErrorHandler();
  const { user } = useAuth();
  const adminId = user?.profileId;

  const {
    data: adminData,
    isLoading,
    refetch
  } = useGetAdminsByIdQuery(
    {
      id: adminId,
      include: [
        {
          relation: 'userTenant',
          scope: {
            include: [{ relation: 'user' }]
          }
        }
      ]
    },
    { skip: !adminId }
  );

  const userInfo = adminData?.userTenant?.user;

  const parsedPhone = useMemo(() => {
    if (!userInfo?.phone) return null;

    const phone = userInfo.phone.startsWith('+') ? userInfo.phone : `+${userInfo.phone}`;
    return extractCountryCode(phone);
  }, [userInfo?.phone]);

  const [updateAdmin, { error, reset }] = useUpdateAdminMutation();

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      firstName: userInfo?.firstName ?? '',
      lastName: userInfo?.lastName ?? '',
      phoneNumber: parsedPhone?.phoneNumber ?? '',
      email: userInfo?.email ?? '',
      gender: userInfo?.gender ?? ''
    },
    validationSchema: Yup.object({
      firstName: Yup.string().required('First name is required'),
      lastName: Yup.string().required('Last name is required'),
      email: Yup.string().email('Invalid email').required('Email is required'),
      phoneNumber: Yup.string().length(10, 'Must be 10 digits').required('Phone number is required')
    }),
    onSubmit: async (values) => {
      if (!userInfo?.id) return;

      const formData = new FormData();
      formData.append('firstName', values.firstName.trim());
      formData.append('lastName', values.lastName.trim());
      formData.append('email', values.email);
      formData.append('phone', `${parsedPhone?.countryCode ?? ''}${values.phoneNumber}`);
      formData.append('gender', values.gender ?? '');

      try {
        await updateAdmin({ id: adminId, data: formData }).unwrap();
        await refetch();
        setEditableFields({
          name: false,
          email: false,
          phoneNumber: false
        });

        openSnackbar({
          open: true,
          message: 'Profile updated successfully',
          variant: 'alert',
          alert: { color: 'success' }
        } as SnackbarProps);
      } catch (err) {
        console.error('Failed to update admin:', err);
      }
    }
  });

  useEffect(() => {
    if (error) {
      handleError(error);
      reset();
    }
  }, [error, handleError, reset]);

  if (isLoading || !userInfo) return <CircularProgress />;

  const editButtonSx = {
    textTransform: 'none',
    boxShadow: 'none',
    backgroundColor: 'transparent',
    minWidth: 0,
    padding: 0,
    fontWeight: 500,
    marginRight: '1rem',
    fontSize: '0.875rem',
    color: '#3f51b5',
    '&:hover': {
      backgroundColor: 'transparent',
      boxShadow: 'none',
      color: '#3f51b5' // prevent color shift
    }
  };

  return (
    <Box component="form" onSubmit={formik.handleSubmit} p={2}>
      <Grid container spacing={2}>
        {/* Name */}
        <Grid item xs={12}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="h6">Name</Typography>
            <Button onClick={() => setEditableFields((prev) => ({ ...prev, name: true }))} sx={editButtonSx}>
              Edit
            </Button>
          </Box>
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            label="First Name"
            fullWidth
            name="firstName"
            value={formik.values.firstName}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            disabled={!editableFields.name}
            error={formik.touched.firstName && Boolean(formik.errors.firstName)}
            helperText={formik.touched.firstName && formik.errors.firstName}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            label="Last Name"
            fullWidth
            name="lastName"
            value={formik.values.lastName}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            disabled={!editableFields.name}
            error={formik.touched.lastName && Boolean(formik.errors.lastName)}
            helperText={formik.touched.lastName && formik.errors.lastName}
          />
        </Grid>

        {/* Gender */}
        <Grid item xs={12}>
          <Typography variant="h6" mt={2}>
            Gender
          </Typography>
          <RadioGroup row name="gender" value={formik.values.gender} onChange={formik.handleChange} sx={{ ml: 1 }}>
            <FormControlLabel value={Gender.Male} control={<Radio />} label="Male" />
            <FormControlLabel value={Gender.Female} control={<Radio />} label="Female" />
            <FormControlLabel value={Gender.Others} control={<Radio />} label="Other" />
          </RadioGroup>
        </Grid>

        {/* Email */}
        <Grid item xs={12} sm={6}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="h6">Email</Typography>
            <Button onClick={() => setEditableFields((prev) => ({ ...prev, email: true }))} sx={editButtonSx}>
              Edit
            </Button>
          </Box>
          <TextField
            fullWidth
            name="email"
            value={formik.values.email}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            disabled={!editableFields.email}
            error={formik.touched.email && Boolean(formik.errors.email)}
            helperText={formik.touched.email && formik.errors.email}
          />
        </Grid>

        {/* Phone */}
        <Grid item xs={12} sm={6}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="h6">Phone Number</Typography>
            <Button onClick={() => setEditableFields((prev) => ({ ...prev, phoneNumber: true }))} sx={editButtonSx}>
              Edit
            </Button>
          </Box>
          <TextField
            fullWidth
            name="phoneNumber"
            value={formik.values.phoneNumber}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            disabled={!editableFields.phoneNumber}
            error={formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)}
            helperText={formik.touched.phoneNumber && formik.errors.phoneNumber}
            inputProps={{ maxLength: 10 }}
            InputProps={{
              startAdornment: <InputAdornment position="start">+{parsedPhone?.countryCode ?? ''}</InputAdornment>
            }}
          />
        </Grid>

        {/* Submit */}
        {(editableFields.name ||
          editableFields.email ||
          editableFields.phoneNumber ||
          formik.values.gender !== formik.initialValues.gender) && (
          <Grid item xs={12} mt={2}>
            <Button
              type="submit"
              variant="contained"
              fullWidth={isMobile}
              sx={{
                backgroundColor: '#00004F',
                color: '#fff',
                borderRadius: '20px',
                padding: '10px'
              }}
            >
              Save Changes
            </Button>
          </Grid>
        )}
      </Grid>
    </Box>
  );
}
