'use client';

import { Box, Button, TextField, Grid, useMediaQuery, useTheme } from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useChangePasswordMutation } from 'redux/auth/authApiSlice';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { useAuth } from 'contexts/AuthContext';

export default function ChangePasswordSection() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useAuth();
  const [changePassword] = useChangePasswordMutation();

  const formik = useFormik({
    initialValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    },
    validationSchema: Yup.object({
      currentPassword: Yup.string().required('Current password is required'),
      newPassword: Yup.string().min(6, 'Password must be at least 6 characters').required('New password is required'),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref('newPassword')], 'Passwords must match')
        .required('Confirm your new password')
    }),
    onSubmit: async (values, { resetForm }) => {
      if (!user?.username) {
        openSnackbar({
          open: true,
          message: 'Unable to fetch user info. Please log in again.',
          variant: 'alert',
          alert: { color: 'error' }
        } as SnackbarProps);
        return;
      }

      try {
        const payload = {
          username: user.username,
          password: values.newPassword,
          oldPassword: values.currentPassword
        };

        await changePassword(payload).unwrap();

        openSnackbar({
          open: true,
          message: 'Password changed successfully',
          variant: 'alert',
          alert: { color: 'success' }
        } as SnackbarProps);

        resetForm();
      } catch (error: any) {
        openSnackbar({
          open: true,
          message: error?.data?.message || 'Failed to change password',
          variant: 'alert',
          alert: { color: 'error' }
        } as SnackbarProps);
      }
    }
  });

  return (
    <form onSubmit={formik.handleSubmit}>
      <Grid container spacing={2} mt={1} sx={{ p: 2 }}>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            name="currentPassword"
            type="password"
            label="Current Password"
            value={formik.values.currentPassword}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.currentPassword && Boolean(formik.errors.currentPassword)}
            helperText={formik.touched.currentPassword && formik.errors.currentPassword}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            name="newPassword"
            type="password"
            label="New Password"
            value={formik.values.newPassword}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.newPassword && Boolean(formik.errors.newPassword)}
            helperText={formik.touched.newPassword && formik.errors.newPassword}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            name="confirmPassword"
            type="password"
            label="Confirm Password"
            value={formik.values.confirmPassword}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
            helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
          />
        </Grid>
        <Grid item xs={12}>
          <Box mt={2}>
            <Button
              type="submit"
              variant="contained"
              fullWidth={isMobile}
              sx={{
                backgroundColor: '#00004F',
                color: 'white',
                fontWeight: 'bold',
                borderRadius: '20px',
                padding: '12px',
                textTransform: 'none'
              }}
            >
              Change Password
            </Button>
          </Box>
        </Grid>
      </Grid>
    </form>
  );
}
