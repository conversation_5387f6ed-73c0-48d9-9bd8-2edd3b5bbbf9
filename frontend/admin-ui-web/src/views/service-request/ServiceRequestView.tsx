'use client';

import React, { useEffect, useState } from 'react';
import {
  Grid,
  Typography,
  <PERSON>,
  Card,
  CardContent,
  Button,
  Stack,
  TextField,
  MenuItem,
  Select,
  InputLabel,
  FormControl
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';

import { useGetServiceRequestByIdQuery, useUpdateServiceRequestMutation } from 'redux/app/service/serviceRequestApiSlice';
import { useGetServicesQuery } from 'redux/app/service/serviceApiSlice';
import { useGetAdminsQuery } from 'redux/app/admin/adminApiSlice';

import { Dayjs } from 'dayjs';
import { ServiceRequestStatus } from 'enums/request-status.enum';
import { DocumentText1 } from 'iconsax-react';
import { useCreateRequestNoteMutation } from 'redux/app/service/requestNoteApiSlice';

interface ViewServiceRequestProps {
  requestId: string;
  onUpdate: () => void;
}
const allowedServiceRequestStatusMap: Record<ServiceRequestStatus, ServiceRequestStatus[]> = {
  [ServiceRequestStatus.UPCOMING]: [ServiceRequestStatus.PENDING],
  [ServiceRequestStatus.PENDING]: [ServiceRequestStatus.ACCEPTED, ServiceRequestStatus.REJECTED, ServiceRequestStatus.ACTION_REQUIRED],
  [ServiceRequestStatus.ACCEPTED]: [ServiceRequestStatus.PROCESSING],
  [ServiceRequestStatus.PROCESSING]: [ServiceRequestStatus.COMPLETED],
  [ServiceRequestStatus.COMPLETED]: [],
  [ServiceRequestStatus.REJECTED]: [],
  [ServiceRequestStatus.ACTION_REQUIRED]: [ServiceRequestStatus.PENDING, ServiceRequestStatus.REJECTED],
  [ServiceRequestStatus.PENDING_PAYMENT]: [ServiceRequestStatus.PENDING, ServiceRequestStatus.REJECTED],
  [ServiceRequestStatus.ON_HOLD]: [ServiceRequestStatus.PENDING, ServiceRequestStatus.REJECTED, ServiceRequestStatus.COMPLETED]
};

const ViewServiceRequest = ({ requestId, onUpdate }: ViewServiceRequestProps) => {
  const {
    data: requestData,
    isLoading,
    error,
    refetch
  } = useGetServiceRequestByIdQuery({
    id: requestId,

    filter: {
      include: [
        {
          relation: 'seller',
          scope: {
            include: [
              {
                relation: 'userTenant',
                scope: { include: [{ relation: 'user' }] }
              }
            ]
          }
        },
        { relation: 'ecomdukeservice' },
        { relation: 'documents' },
        { relation: 'admins' }
      ]
    }
  });

  const { data: allServices = [] } = useGetServicesQuery();
  const { data: allAdmins = [] } = useGetAdminsQuery({
    limit: 100,
    include: [
      {
        relation: 'userTenant',
        scope: {
          include: [
            { relation: 'user' },
            {
              relation: 'role'
            }
          ]
        }
      }
    ]
  });

  const subAdmins = allAdmins.filter((admin: any) => admin?.userTenant?.role?.name === 'Sub Admin');

  const [updateServiceRequestStatus, { isLoading: isUpdating }] = useUpdateServiceRequestMutation();
  const [createRequestNote] = useCreateRequestNoteMutation();
  const [newStatus, setNewStatus] = useState<ServiceRequestStatus | ''>('');
  const [note, setNote] = useState('');
  const [assignedSubAdmin, setAssignedSubAdmin] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [recurringDate, setRecurringDate] = useState<Dayjs | null>(null);

  useEffect(() => {
    refetch();
  }, [refetch]);

  const handleStatusUpdate = async (status: ServiceRequestStatus.ACCEPTED | ServiceRequestStatus.REJECTED) => {
    if (!requestData?.id) return;

    const payload: any = { status };
    if (status === ServiceRequestStatus.REJECTED) {
      payload.rejectionReason = rejectionReason;
      payload.subAdminId = assignedSubAdmin;
    }

    await updateServiceRequestStatus({
      id: requestData.id,
      data: payload
    }).unwrap();

    await createRequestNote({
      ecomdukeserviceRequestId: requestData.id,
      notes: rejectionReason || '-', // fallback if empty
      previousStatus: requestData.status,
      changedStatus: status
    }).unwrap();

    onUpdate();

    setRejectionReason('');
  };

  const handleUpdateStatus = async () => {
    if (!requestData?.id || !newStatus) return;

    await updateServiceRequestStatus({
      id: requestData.id,
      data: {
        status: newStatus,
        rejectionReason: rejectionReason,
        recurringPaymentDate: recurringDate?.toISOString(),
        assigneeId: assignedSubAdmin || undefined
      }
    }).unwrap();

    await createRequestNote({
      ecomdukeserviceRequestId: requestData.id,
      notes: note || '', // fallback if note is empty
      previousStatus: requestData.status,
      changedStatus: newStatus
    }).unwrap();

    refetch();
    onUpdate();
    setNewStatus('');
    setNote('');
    setRecurringDate(null);
    setAssignedSubAdmin('');
  };

  if (isLoading) return <Typography>Loading...</Typography>;
  if (error || !requestData) return <Typography color="error">Error fetching service request</Typography>;

  const service = allServices.find((s) => s.id === requestData.ecomdukeserviceId);
  const serviceName = service?.name ?? 'N/A';

  return (
    <Grid container spacing={2} sx={{ mt: 1 }}>
      <Grid item xs={12}>
        <Card sx={{ p: 2, boxShadow: 3 }}>
          <CardContent>
            <Typography variant="h4" gutterBottom>
              Service Request Details
            </Typography>

            {requestData.status == ServiceRequestStatus.ACTION_REQUIRED && (
              <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
                <Button
                  variant="contained"
                  color="success"
                  onClick={() => handleStatusUpdate(ServiceRequestStatus.ACCEPTED)}
                  disabled={isUpdating}
                >
                  Approve
                </Button>
                <TextField
                  label="Rejection Reason"
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  size="small"
                  sx={{ width: 300 }}
                />
                <Button
                  variant="contained"
                  color="error"
                  onClick={() => handleStatusUpdate(ServiceRequestStatus.REJECTED)}
                  disabled={isUpdating || !rejectionReason}
                >
                  Reject
                </Button>
              </Stack>
            )}

            {/* Static Details */}
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <DisplayItem label="Service Name" value={serviceName} />
              </Grid>
              <Grid item xs={12} md={6}>
                <DisplayItem
                  label="Seller"
                  value={
                    requestData.seller?.userTenant?.user
                      ? `${requestData.seller.userTenant.user.firstName} ${requestData.seller.userTenant.user.lastName}`
                      : '-'
                  }
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <DisplayItem label="Status" value={requestData.status} />
              </Grid>
              {requestData.paidAmount !== undefined && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Paid Amount" value={requestData.paidAmount} />
                </Grid>
              )}
              {requestData.paymentReference && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Payment Reference" value={requestData.paymentReference} />
                </Grid>
              )}
              {requestData.notes && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Notes" value={requestData.notes} isMultiline />
                </Grid>
              )}
              {requestData.paidOn && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Paid On" value={new Date(requestData.paidOn).toLocaleString()} />
                </Grid>
              )}
              {requestData.rejectionReason && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Notes" value={requestData.rejectionReason} isMultiline />
                </Grid>
              )}
              {requestData.createdOn && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Created On" value={new Date(requestData.createdOn).toLocaleString()} />
                </Grid>
              )}
              {Array.isArray(requestData.documents) && requestData.documents.length > 0 && (
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                    Documents:
                  </Typography>

                  <Stack direction="row" spacing={3} flexWrap="wrap">
                    {requestData.documents.map((doc, idx) => {
                      const docUrl = typeof doc === 'string' ? doc : doc?.url || '';
                      const isImage = /\.(png|jpe?g|gif|webp)$/i.test(docUrl);
                      const rawName = typeof doc === 'string' ? docUrl.split('/').pop() : doc?.name || docUrl.split('/').pop();
                      const fileName = decodeURIComponent(rawName ?? `Document-${idx + 1}`);

                      return (
                        <Box
                          key={typeof doc === 'string' ? idx : doc?.id || idx}
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1,
                            p: 1,
                            borderRadius: 2,
                            backgroundColor: '#f9f9f9'
                          }}
                        >
                          {isImage ? (
                            <img
                              src={docUrl}
                              alt={fileName}
                              style={{
                                width: 40,
                                height: 40,
                                objectFit: 'cover',
                                borderRadius: 4,
                                border: '1px solid #ccc'
                              }}
                            />
                          ) : (
                            <Box
                              sx={{
                                width: 40,
                                height: 40,
                                backgroundColor: '#eee',
                                borderRadius: 1,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: 20,
                                fontWeight: 'bold',
                                color: '#555'
                              }}
                            >
                              <DocumentText1 size={28} color="#555" />
                            </Box>
                          )}

                          <a
                            href={docUrl}
                            target="_blank"
                            rel="noreferrer"
                            style={{
                              textDecoration: 'underline',
                              color: '#1976d2',
                              wordBreak: 'break-all',
                              maxWidth: 120
                            }}
                          >
                            {fileName}
                          </a>
                        </Box>
                      );
                    })}
                  </Stack>
                </Grid>
              )}
            </Grid>
            {Array.isArray(requestData.ecomdukeRequestNotes) && requestData.ecomdukeRequestNotes.length > 0 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Request Notes
                </Typography>
                <Stack spacing={2}>
                  {requestData.ecomdukeRequestNotes.map((note) => (
                    <Box
                      key={note.id}
                      sx={{
                        p: 2,
                        background: '#f9f9f9',
                        borderRadius: 2,
                        border: '1px solid #e0e0e0'
                      }}
                    >
                      <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                        {note.notes}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Status Changed: {note.previousStatus ?? '-'} → {note.changedStatus ?? '-'}
                      </Typography>
                    </Box>
                  ))}
                </Stack>
              </Box>
            )}

            {/* Status Management */}
            {requestData.status !== ServiceRequestStatus.REJECTED && (
              <Box sx={{ mt: 4 }}>
                <Typography variant="h5" gutterBottom>
                  Change Status
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Status</InputLabel>
                      <Select value={newStatus} onChange={(e) => setNewStatus(e.target.value as ServiceRequestStatus)}>
                        {Object.entries(ServiceRequestStatus).map(([key, label]) => (
                          <MenuItem
                            key={key}
                            value={label}
                            disabled={!allowedServiceRequestStatusMap[requestData.status as ServiceRequestStatus].includes(label)}
                          >
                            {label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DatePicker
                        label="Recurring Payment Date"
                        value={recurringDate}
                        onChange={(newDate) => setRecurringDate(newDate)}
                        slotProps={{ textField: { fullWidth: true } }}
                      />
                    </LocalizationProvider>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Assign Sub Admin</InputLabel>
                      <Select value={assignedSubAdmin} onChange={(e) => setAssignedSubAdmin(e.target.value)}>
                        {subAdmins.map((admin: any) => {
                          const user = admin?.userTenant?.user;
                          const fullName = user ? `${user.firstName ?? ''} ${user.lastName ?? ''}`.trim() : 'Unnamed';
                          return (
                            <MenuItem key={admin.id} value={admin.id}>
                              {fullName}
                            </MenuItem>
                          );
                        })}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Note"
                      value={note}
                      onChange={(e) => setNote(e.target.value)}
                      multiline
                      minRows={3}
                      maxRows={6}
                      InputProps={{
                        sx: {
                          maxHeight: 140, // Adjust as needed (e.g. 6 rows)
                          overflow: 'auto'
                        }
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} display="flex" justifyContent="flex-end" alignItems="flex-end">
                    <Button variant="contained" onClick={handleUpdateStatus} disabled={!newStatus}>
                      Update Status
                    </Button>
                  </Grid>
                </Grid>
              </Box>
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

const DisplayItem = ({ label, value, isMultiline = false }: { label: string; value: string | number; isMultiline?: boolean }) => (
  <Box sx={{ mb: 1.5 }}>
    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
      {label}:
    </Typography>
    <Box
      sx={{
        backgroundColor: '#f5f5f5',
        px: 2,
        py: 1.2,
        borderRadius: 2,
        fontSize: '0.95rem',
        color: 'text.primary',
        whiteSpace: isMultiline ? 'pre-line' : 'nowrap'
      }}
    >
      {value}
    </Box>
  </Box>
);

export default ViewServiceRequest;
