'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import { ColumnDef, ColumnFiltersState, SortingState, PaginationState } from '@tanstack/react-table';

import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Tooltip from '@mui/material/Tooltip';
import IconButton from 'components/@extended/IconButton';
import { Add, Edit, Eye, Trash } from 'iconsax-react';
import { useTheme } from '@mui/material/styles';

import Loader from 'components/Loader';
import IndeterminateCheckbox from 'components/third-party/react-table/IndeterminateCheckbox';
import ServiceTable from './ServiceTable';
import AlertServiceDelete from './AlertServiceDelete';

import { ThemeMode } from 'config';
import { useAuth } from 'contexts/AuthContext';
import withPermission from 'hoc/withPermission';
import { PermissionKeys } from 'enums/permission-keys.enum';

import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from 'utils/table-filter';

import { EcomDukeService } from 'types/service';
import { useGetServiceCountQuery, useGetServicesQuery } from 'redux/app/service/serviceApiSlice';

const ServiceListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });

  const [deleteId, setDeleteId] = useState<string>('');
  const [serviceName, setServiceName] = useState<string>('');
  const [open, setOpen] = useState<boolean>(false);

  const handleClose = useCallback(() => setOpen((prev) => !prev), []);

  const canCreate = hasPermission(PermissionKeys.CreateEcomdukeService);
  const canEdit = hasPermission(PermissionKeys.UpdateEcomdukeService);
  const canDelete = hasPermission(PermissionKeys.DeleteEcomdukeService);

  const {
    data: serviceList,
    isLoading: serviceListLoading,
    refetch
  } = useGetServicesQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name', 'description']),
    ...convertPaginationToLoopback(pagination)
  });

  const { data: serviceCount, isLoading: serviceCountLoading } = useGetServiceCountQuery({});

  useEffect(() => {
    refetch?.();
  }, [refetch]);

  const columns = useMemo<ColumnDef<EcomDukeService>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      },
      {
        header: 'Name',
        accessorKey: 'name',
        cell: ({ row }) => <Typography>{row.original.name ?? '-'}</Typography>
      },
      {
        header: 'Description',
        accessorKey: 'description',
        cell: ({ row }) => <Typography>{row.original.description ?? '-'}</Typography>
      },
      { header: 'Price', accessorKey: 'price', cell: ({ row }) => <Typography>{row.original.price ?? '-'}</Typography> },
      { header: 'Currency', accessorKey: 'currency', cell: ({ row }) => <Typography>{row.original.currency ?? '-'}</Typography> },
      {
        header: 'Service Type',
        accessorKey: 'serviceType',
        cell: ({ row }) => <Typography>{row.original.serviceType ?? '-'}</Typography>
      },
      {
        header: 'Payment Type',
        accessorKey: 'paymentType',
        cell: ({ row }) => <Typography>{row.original.paymentType ?? '-'}</Typography>
      },
      {
        header: 'Actions',
        enableSorting: false,
        cell: ({ row }) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add
                style={{
                  color: theme.palette.error.main,
                  transform: 'rotate(45deg)'
                }}
              />
            ) : (
              <Eye />
            );
          return (
            <Stack direction="row" alignItems="center" spacing={0}>
              <Tooltip title="View">
                <IconButton
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>

              <Tooltip title="Edit">
                <IconButton
                  onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    router.push(`/ecomduke-service/edit/${row.original.id}`);
                  }}
                  disabled={!canEdit}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  <Edit />
                </IconButton>
              </Tooltip>

              <Tooltip title="Delete">
                <IconButton
                  color="error"
                  onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    setDeleteId(row.original.id as string);
                    setServiceName(row.original.name || '');
                    handleClose();
                  }}
                  disabled={!canDelete}
                  sx={{
                    ':hover': {
                      color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
                    }
                  }}
                >
                  <Trash />
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    [theme, canEdit, canDelete, router, handleClose]
  );

  return (
    <>
      {serviceListLoading || serviceCountLoading ? (
        <Loader />
      ) : (
        <ServiceTable
          {...{
            data: serviceList ?? [],
            columns,
            setSorting,
            sorting,
            columnFilters,
            setColumnFilters,
            loading: serviceListLoading,
            globalFilter,
            setGlobalFilter,
            pagination,
            setPagination,
            totalRows: serviceCount?.count ?? 0,
            canCreate,
            refetch
          }}
        />
      )}

      <AlertServiceDelete refetch={refetch} id={deleteId} name={serviceName} open={open} handleClose={handleClose} />
    </>
  );
};

export default withPermission(PermissionKeys.ViewEcomdukeService)(ServiceListPage);
