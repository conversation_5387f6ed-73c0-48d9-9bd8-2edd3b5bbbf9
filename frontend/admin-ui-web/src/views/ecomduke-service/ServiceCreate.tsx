'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Container,
  Grid,
  TextField,
  Typography,
  MenuItem,
  InputLabel,
  Switch,
  FormControlLabel,
  Button,
  Checkbox
} from '@mui/material';
import { useFormik } from 'formik';
import { useRouter } from 'next/navigation';
import { useCreateEcomServiceMutation, useUpdateEcomServiceMutation } from 'redux/app/service/serviceApiSlice';
import { SnackbarProps } from 'types/snackbar';
import { openSnackbar } from 'api/snackbar';
import { useGetTaxCategoriesQuery } from 'redux/app/tax/taxCategoryApiSlice';
import { serviceValidationSchema } from '../../../validations/ecomdukeservice';
import { PaymentType, RecurringInterval, ServiceType } from 'enums/ecomDukeService.enum';

interface EcomDukeServiceFormProps {
  initialValues?: any;
  isEdit?: boolean;
  serviceId?: string;
  refetch?: () => void;
}

const currencyOptions = ['INR', 'USD', 'EUR'];
const serviceTypes = Object.values(ServiceType);
const recurringOptions = Object.values(RecurringInterval);
const paymentTypes = Object.values(PaymentType);

export default function EcomDukeServiceCreate({ initialValues, isEdit = false, serviceId, refetch }: EcomDukeServiceFormProps) {
  const router = useRouter();
  const [createService] = useCreateEcomServiceMutation();
  const [updateService] = useUpdateEcomServiceMutation();
  const { data: taxCategories = [] } = useGetTaxCategoriesQuery();

  const [isRecurring, setIsRecurring] = useState(initialValues?.serviceType === 'Recurring');
  const [enablePartialPayment, setEnablePartialPayment] = useState(initialValues?.paymentType === 'Partial');

  const formik = useFormik({
    validateOnBlur: true,
    validateOnChange: true,
    initialValues: {
      name: initialValues?.name || '',
      description: initialValues?.description || '',
      price: initialValues?.price || 0.0,
      currency: initialValues?.currency || 'INR',
      taxCategoryId: initialValues?.taxCategoryId || '',
      isActive: initialValues?.isActive ?? true,
      fileUploadRequired: initialValues?.fileUploadRequired ?? false,
      serviceType: initialValues?.serviceType || ServiceType.OneTime,
      recurringInterval: initialValues?.recurringInterval || '',
      paymentType: initialValues?.paymentType || PaymentType.Full,
      firstPartAmount: initialValues?.firstPartAmount || 0.0,
      secondPartAmount: initialValues?.secondPartAmount || 0.0,
      lastDateToPay: initialValues?.lastDateToPay || ''
    },
    validationSchema: serviceValidationSchema,
    onSubmit: async (values) => {
      const payload = {
        ...values,
        price: parseFloat(values.price as any),
        firstPartAmount: values.firstPartAmount ? parseFloat(values.firstPartAmount as any) : 0,
        secondPartAmount: values.secondPartAmount ? parseFloat(values.secondPartAmount as any) : 0,
        lastDateToPay: values.lastDateToPay ? new Date(values.lastDateToPay).toISOString() : undefined
      };

      if (isEdit && serviceId) {
        await updateService({ id: serviceId, data: payload }).unwrap();
        openSnackbar({
          open: true,
          message: 'Service updated successfully',
          variant: 'alert',
          alert: { color: 'success' }
        } as SnackbarProps);
      } else {
        await createService(payload).unwrap();
        openSnackbar({
          open: true,
          message: 'Service created successfully',
          variant: 'alert',
          alert: { color: 'success' }
        } as SnackbarProps);
      }
      refetch?.();
      router.push('/ecomduke-service');
    }
  });

  return (
    <Container maxWidth="md" sx={{ mt: 4 }}>
      <Card elevation={2}>
        <CardContent>
          <Typography variant="h5" fontWeight={600} gutterBottom>
            {isEdit ? 'Update Service' : 'Create Service'}
          </Typography>
          <form onSubmit={formik.handleSubmit} noValidate>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <InputLabel htmlFor="name">Service Name</InputLabel>
                <TextField
                  fullWidth
                  id="name"
                  variant="outlined"
                  {...formik.getFieldProps('name')}
                  error={formik.touched.name && Boolean(formik.errors.name)}
                  helperText={formik.touched.name && typeof formik.errors.name === 'string' ? formik.errors.name : ''}
                />
              </Grid>

              <Grid item xs={12}>
                <InputLabel htmlFor="description">Description</InputLabel>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  id="description"
                  variant="outlined"
                  {...formik.getFieldProps('description')}
                  error={formik.touched.description && Boolean(formik.errors.description)}
                  helperText={formik.touched.description && typeof formik.errors.description === 'string' ? formik.errors.description : ''}
                />
              </Grid>

              <Grid item xs={6}>
                <InputLabel htmlFor="price">Price</InputLabel>
                <TextField
                  fullWidth
                  type="number"
                  id="price"
                  variant="outlined"
                  {...formik.getFieldProps('price')}
                  error={formik.touched.price && Boolean(formik.errors.price)}
                  helperText={formik.touched.price && typeof formik.errors.price === 'string' ? formik.errors.price : ''}
                />
              </Grid>

              <Grid item xs={6}>
                <InputLabel htmlFor="currency">Currency</InputLabel>
                <TextField
                  select
                  fullWidth
                  id="currency"
                  variant="outlined"
                  {...formik.getFieldProps('currency')}
                  error={formik.touched.currency && Boolean(formik.errors.currency)}
                  helperText={formik.touched.currency && typeof formik.errors.currency === 'string' ? formik.errors.currency : ''}
                >
                  {currencyOptions.map((cur) => (
                    <MenuItem key={cur} value={cur}>
                      {cur}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12}>
                <InputLabel htmlFor="taxCategoryId">Tax Category</InputLabel>
                <TextField
                  select
                  fullWidth
                  id="taxCategoryId"
                  {...formik.getFieldProps('taxCategoryId')}
                  error={formik.touched.taxCategoryId && Boolean(formik.errors.taxCategoryId)}
                  helperText={
                    formik.touched.taxCategoryId && typeof formik.errors.taxCategoryId === 'string' ? formik.errors.taxCategoryId : ''
                  }
                >
                  <MenuItem value="" disabled>
                    Select tax category
                  </MenuItem>
                  {taxCategories.map((tax) => (
                    <MenuItem key={tax.id} value={tax.id}>
                      {tax.name}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={formik.values.fileUploadRequired}
                      onChange={(e) => formik.setFieldValue('fileUploadRequired', e.target.checked)}
                    />
                  }
                  label="Files upload required"
                />
              </Grid>

              <Grid item xs={12}>
                <InputLabel>Service Type</InputLabel>
                <TextField
                  select
                  fullWidth
                  value={formik.values.serviceType}
                  onChange={(e) => {
                    const type = e.target.value as ServiceType;
                    formik.setFieldValue('serviceType', type);
                    setIsRecurring(type === ServiceType.Recurring);
                  }}
                >
                  {serviceTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              {isRecurring && (
                <Grid item xs={12}>
                  <InputLabel>Recurring Interval</InputLabel>
                  <TextField
                    select
                    fullWidth
                    {...formik.getFieldProps('recurringInterval')}
                    error={formik.touched.recurringInterval && Boolean(formik.errors.recurringInterval)}
                    helperText={
                      formik.touched.recurringInterval && typeof formik.errors.recurringInterval === 'string'
                        ? formik.errors.recurringInterval
                        : ''
                    }
                  >
                    {recurringOptions.map((interval) => (
                      <MenuItem key={interval} value={interval}>
                        {interval}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
              )}

              <Grid item xs={12}>
                <InputLabel>Payment Type</InputLabel>
                <TextField
                  select
                  fullWidth
                  value={formik.values.paymentType}
                  onChange={(e) => {
                    const type = e.target.value as PaymentType;
                    formik.setFieldValue('paymentType', type);
                    setEnablePartialPayment(type === PaymentType.Partial);
                  }}
                >
                  {paymentTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type === PaymentType.Full ? 'Full Payment' : 'Enable Partial Payment'}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              {enablePartialPayment && (
                <>
                  <Grid item xs={6}>
                    <InputLabel>1st Part Amount (Upfront)</InputLabel>
                    <TextField
                      fullWidth
                      type="number"
                      {...formik.getFieldProps('firstPartAmount')}
                      error={formik.touched.firstPartAmount && Boolean(formik.errors.firstPartAmount)}
                      helperText={
                        formik.touched.firstPartAmount && typeof formik.errors.firstPartAmount === 'string'
                          ? formik.errors.firstPartAmount
                          : ''
                      }
                    />

                    {/* <TextField fullWidth type="number" {...formik.getFieldProps('firstPartAmount')} /> */}
                  </Grid>
                  <Grid item xs={6}>
                    <InputLabel>2nd Part Amount</InputLabel>
                    <TextField
                      fullWidth
                      type="number"
                      {...formik.getFieldProps('secondPartAmount')}
                      error={formik.touched.firstPartAmount && Boolean(formik.errors.secondPartAmount)}
                      helperText={
                        formik.touched.secondPartAmount && typeof formik.errors.secondPartAmount === 'string'
                          ? formik.errors.secondPartAmount
                          : ''
                      }
                    />
                    {/* <TextField fullWidth type="number" {...formik.getFieldProps('secondPartAmount')} /> */}
                  </Grid>
                </>
              )}

              <Grid item xs={12}>
                <InputLabel>Last Date to Make Payment (optional)</InputLabel>
                <TextField
                  fullWidth
                  type="date"
                  {...formik.getFieldProps('lastDateToPay')}
                  error={formik.touched.lastDateToPay && Boolean(formik.errors.lastDateToPay)}
                  helperText={
                    formik.touched.lastDateToPay && typeof formik.errors.lastDateToPay === 'string' ? formik.errors.lastDateToPay : ''
                  }
                />

                {/* <TextField fullWidth type="date" {...formik.getFieldProps('lastDateToPay')} /> */}
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={<Switch checked={formik.values.isActive} onChange={(e) => formik.setFieldValue('isActive', e.target.checked)} />}
                  label="Active"
                />
              </Grid>

              <Grid item xs={12} display="flex" justifyContent="flex-end">
                <Button type="submit" variant="contained" color="primary">
                  {isEdit ? 'Update Service' : 'Create Service'}
                </Button>
              </Grid>
            </Grid>
          </form>
        </CardContent>
      </Card>
    </Container>
  );
}
