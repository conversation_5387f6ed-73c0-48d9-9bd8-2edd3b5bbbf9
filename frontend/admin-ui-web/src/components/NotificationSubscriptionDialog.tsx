import React from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, CircularProgress } from '@mui/material';
import { UserNotification } from 'types/user-notification';

interface NotificationSubscriptionDialogProps {
  open: boolean;
  notification: UserNotification | null;
  onClose: () => void;
  onSubscribe: () => void;
  onMarkAsRead: () => void;
  isSubscribing: boolean;
}

const NotificationSubscriptionDialog: React.FC<NotificationSubscriptionDialogProps> = ({
  open,
  notification,
  onClose,
  onSubscribe,
  onMarkAsRead,
  isSubscribing
}) => {
  const handleSubscribe = () => {
    onSubscribe();
  };

  const handleMarkAsRead = () => {
    onMarkAsRead();
    onClose();
  };

  if (!notification) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 1
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Typography variant="h5" component="div">
          Notification Subscription
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ py: 2 }}>
        <Box sx={{ mb: 2 }}>
          <Typography variant="h6" sx={{ mb: 1, fontWeight: 'bold' }}>
            {notification.notification?.subject || 'Notification'}
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
            {notification.notification?.body || 'No content available'}
          </Typography>
        </Box>

        <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
          <Typography variant="body1" sx={{ mb: 1 }}>
            Would you like to subscribe to receive updates and latest information about this topic?
          </Typography>
          <Typography variant="body2" color="text.secondary">
            By subscribing, you&apos;ll receive notifications about similar topics and stay updated with the latest information.
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2, gap: 1 }}>
        <Button onClick={handleMarkAsRead} variant="outlined" color="secondary" disabled={isSubscribing}>
          No, Just Mark as Read
        </Button>
        <Button
          onClick={handleSubscribe}
          variant="contained"
          color="primary"
          disabled={isSubscribing}
          startIcon={isSubscribing ? <CircularProgress size={16} /> : null}
        >
          {isSubscribing ? 'Subscribing...' : 'Yes, Subscribe'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default NotificationSubscriptionDialog;
