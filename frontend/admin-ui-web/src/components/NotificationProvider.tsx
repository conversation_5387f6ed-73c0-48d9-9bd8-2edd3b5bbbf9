// utils/hooks/useFcmToken.ts
import { useEffect, useState } from 'react';
import { getMessaging, getToken } from 'firebase/messaging';
import firebaseApp from 'utils/firebase/firebase';

const useFcmToken = () => {
  const [fcmToken, setFcmToken] = useState<string | null>(null);

  useEffect(() => {
    const fetchToken = async () => {
      try {
        const messaging = getMessaging(firebaseApp);
        const token = await getToken(messaging, {
          vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY
        });
        setFcmToken(token);
      } catch {}
    };

    fetchToken();
  }, []);

  return fcmToken;
};

export default useFcmToken;
