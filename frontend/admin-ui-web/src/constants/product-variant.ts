import { fieldsExcludeMetaFields } from './shared';

const directIncludes = [
  'productCustomizationFields',
  'productSpecifications',
  'productDetail',
  'productMoreInfo',
  'productDisclaimer',
  'productReturnPolicy',
  'productTermsAndCondition',
  'productBoxContents',
  'productUniqueness',
  'productPersonalWork',
  'featuredAsset',
  'productSuitability',
  'productVariantPrice'
].map((relation) => ({
  relation,
  scope: {
    fields: fieldsExcludeMetaFields
  }
}));

export const variantCustomIncludes = [
  ...directIncludes,
  {
    relation: 'productVariantAssets',
    scope: {
      fields: fieldsExcludeMetaFields,
      order: ['position ASC'],
      include: [
        {
          relation: 'asset',
          scope: {
            fields: fieldsExcludeMetaFields
          }
        }
      ]
    }
  },

  {
    relation: 'productVariantFacetValues',
    scope: {
      include: [
        {
          relation: 'facetValue',
          scope: {
            include: [
              {
                relation: 'facet',
                scope: {
                  fields: fieldsExcludeMetaFields
                }
              }
            ],
            fields: fieldsExcludeMetaFields
          }
        }
      ]
    }
  }
];

export const manageVariantInludes = [
  {
    relation: 'productVariantOptions',
    scope: {
      fields: fieldsExcludeMetaFields,

      include: [
        {
          required: true,
          relation: 'productOption',
          scope: {
            fields: fieldsExcludeMetaFields,
            include: [
              {
                required: true,
                relation: 'productOptionGroup',
                scope: {
                  fields: fieldsExcludeMetaFields
                }
              }
            ]
          }
        }
      ]
    }
  }
];
