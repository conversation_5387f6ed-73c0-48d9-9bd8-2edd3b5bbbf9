import { apiSlice } from '../../apiSlice';
import { ApiSliceIdentifier } from 'enums/api.enum';
import { Count } from 'types/api';
import { IFilter } from '../types/filter';
import { buildFilterParams } from 'utils/buildFilterParams';
import { EcomdukeRequestNote } from 'types/service';

export const ecomRequestNoteApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getRequestNotes: builder.query<EcomdukeRequestNote[], IFilter | void>({
      query: (filter) => ({
        url: '/ecomduke-request-notes',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),

    getRequestNoteById: builder.query<EcomdukeRequestNote, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => ({
        url: `/ecomduke-request-notes/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),

    getRequestNoteCount: builder.query<
      Count,
      {
        where?: Record<string, unknown>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ where, include }) => ({
        url: '/ecomduke-request-notes/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            where,
            include
          })
        }
      })
    }),

    createRequestNote: builder.mutation<void, Partial<EcomdukeRequestNote>>({
      query: (data) => ({
        url: '/ecomduke-request-notes',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),

    updateRequestNote: builder.mutation<void, { id: string; data: Partial<EcomdukeRequestNote> }>({
      query: ({ id, data }) => ({
        url: `/ecomduke-request-notes/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),

    deleteRequestNote: builder.mutation<void, string>({
      query: (id) => ({
        url: `/ecomduke-request-notes/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    })
  })
});
export const {
  useGetRequestNotesQuery,
  useLazyGetRequestNotesQuery,
  useGetRequestNoteByIdQuery,
  useGetRequestNoteCountQuery,
  useCreateRequestNoteMutation,
  useUpdateRequestNoteMutation,
  useDeleteRequestNoteMutation
} = ecomRequestNoteApiSlice;
