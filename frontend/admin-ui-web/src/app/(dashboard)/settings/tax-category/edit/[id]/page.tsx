'use client';

import { Container, CircularProgress, Typography } from '@mui/material';
import { useGetTaxCategoryByIdQuery } from 'redux/app/tax/taxCategoryApiSlice';
import TaxCategoryCreate from 'views/tax-category/TaxCategoryCreate';

const EditTaxCategoryPage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const { data: taxCategoryData, isLoading, error, refetch } = useGetTaxCategoryByIdQuery({ id });

  if (isLoading) {
    return (
      <Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error || !taxCategoryData) {
    return (
      <Container sx={{ textAlign: 'center', mt: 4 }}>
        <Typography color="error" variant="h6">
          Failed to load tax category
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="md">
      <TaxCategoryCreate
        isEdit
        taxCategoryId={id}
        initialValues={{
          name: taxCategoryData.name ?? '',
          description: taxCategoryData.description ?? '',
          hsnCode: taxCategoryData.hsnCode ?? '',
          taxRate: taxCategoryData.taxRate ?? 0,
          isDefault: taxCategoryData.isDefault ?? false
        }}
        refetch={refetch}
      />
    </Container>
  );
};

export default EditTaxCategoryPage;
