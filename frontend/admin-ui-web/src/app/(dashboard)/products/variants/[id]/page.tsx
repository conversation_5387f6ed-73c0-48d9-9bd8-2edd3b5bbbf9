'use client';
import Loader from 'components/Loader';
import { variantCustomIncludes } from 'constants/product-variant';
import { useParams } from 'next/navigation';
import { useMemo } from 'react';
import { useGetProductVariantByIdQuery } from 'redux/app/products/productApiSlice';
import { Customization } from 'types/product-dto';
import { ProductVariantUpdateDto } from 'types/product-variant';
import UpdateProductVariant from 'views/product-variants/ProductVariantForm';

export default function ProductVariant() {
  const params = useParams();
  const {
    data: variant,
    isLoading,
    isFetching
  } = useGetProductVariantByIdQuery(
    { id: params.id as string, filter: { include: variantCustomIncludes } },
    { skip: !params.id, refetchOnMountOrArgChange: true }
  );

  const initialValue: ProductVariantUpdateDto | undefined = useMemo(() => {
    if (!variant) return undefined;
    return {
      name: variant.name,
      sku: variant.sku,
      price: Number(variant.productVariantPrice.price),
      mrp: Number(variant.productVariantPrice.mrp),
      priceWithoutTax: Number(variant.productVariantPrice.priceWithoutTax),
      assets: variant?.productVariantAssets ? variant?.productVariantAssets.map((item) => item.assetId) : [variant.featuredAssetId],
      facets: variant.productVariantFacetValues?.map((item) => item.facetValueId),
      featuredAssetId: variant.featuredAssetId,
      outOfStockThreshold: variant.outOfStockThreshold,
      enabled: variant.enabled,
      taxCategoryId: variant.taxCategoryId,
      suitability: variant.productSuitability?.suitableFor ? { suitableFor: variant.productSuitability.suitableFor } : undefined,
      uniqueness: variant.productUniqueness?.uniqueness ? { uniqueness: variant.productUniqueness.uniqueness } : undefined,
      personalWork: variant.productPersonalWork?.workLevel ? { workLevel: variant.productPersonalWork.workLevel } : undefined,
      terms: variant?.productTermsAndCondition?.terms ? { terms: variant.productTermsAndCondition?.terms } : undefined,
      returnPolicy: variant?.productReturnPolicy?.returnPolicy ? { returnPolicy: variant.productReturnPolicy?.returnPolicy } : undefined,
      disclaimer: variant?.productDisclaimer?.disclaimer ? { disclaimer: variant.productDisclaimer?.disclaimer } : undefined,
      details: variant?.productDetail?.details ? { details: variant.productDetail?.details } : undefined,
      // facets: variant.productFacetValues?.map((item) => item.facetValueId),
      boxContents: variant.productBoxContents?.map((item) => ({ itemName: item.itemName, quantity: item.quantity })) ?? [],
      specifications: variant.productSpecifications?.map((item) => ({ name: item.name, value: item.value })) ?? [],
      customizations:
        variant.productCustomizationFields?.map(
          (item) =>
            ({
              name: item.name,
              label: item.label,
              placeholder: item.placeholder,
              fieldType: item.fieldType,
              isRequired: item.isRequired,
              options: []
            }) as Customization
        ) ?? []
    } as ProductVariantUpdateDto;
  }, [variant]);
  const assets = useMemo(() => {
    if (!variant) return [];
    return (
      variant.productVariantAssets?.map((item) => ({
        id: item.assetId,
        preview: item.asset?.previewUrl,
        mimeType: item.asset?.mimeType ?? 'image'
      })) ?? []
    );
  }, [variant]);

  if (!variant || isFetching || !initialValue || isLoading) return <Loader />;
  return (
    <UpdateProductVariant
      initialValue={initialValue}
      id={params.id as string}
      initialAssets={assets}
      initialFacets={variant.productVariantFacetValues ?? []}
    />
  );
}
