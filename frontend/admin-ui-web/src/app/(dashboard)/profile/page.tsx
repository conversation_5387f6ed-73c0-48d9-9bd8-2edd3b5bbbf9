'use client';

import React from 'react';
import { <PERSON>, Typo<PERSON>, Card, CardHeader, Divider } from '@mui/material';
import ProfileForm from 'views/profile/ProfileForm';
import ChangePasswordSection from 'views/profile/ChangePasswordSection';

export default function AdminProfilePage() {
  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Admin Profile
      </Typography>

      <Card sx={{ mt: 2 }}>
        <CardHeader title="Profile Information" />
        <Divider />
        <ProfileForm />
      </Card>

      <Card sx={{ mt: 4 }}>
        <CardHeader title="Change Password" />
        <Divider />
        <ChangePasswordSection />
      </Card>
    </Box>
  );
}
