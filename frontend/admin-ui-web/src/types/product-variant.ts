import {
  Product,
  TaxCategory,
  ProductBoxContent,
  Asset,
  ProductCustomField,
  ProductDetail,
  ProductDisclaimer,
  ProductReturnPolicy,
  ProductSpecification,
  ProductMoreInfo,
  ProductTermsAndCondition,
  ProductAsset,
  ProductFacetValue,
  ProductVariantPrice
} from './product';
import {
  ProductSuitability,
  ProductUniqueness,
  ProductPersonalWork,
  Customization,
  BoxContent,
  Detail,
  Disclaimer,
  ReturnPolicy,
  Specification,
  Terms
} from './product-dto';
export interface ProductVariantUpdateDto {
  name: string;
  sku: string;
  price: number;
  mrp: number;
  priceWithoutTax: number;
  featuredAssetId: string;
  outOfStockThreshold: number;
  optionIds: number[];
  taxCategoryId: string;
  enabled?: boolean;
  suitability?: ProductSuitability;
  uniqueness?: ProductUniqueness;
  personalWork?: ProductPersonalWork;
  boxContents: BoxContent[];
  details?: Detail;
  specifications: Specification[];
  returnPolicy?: ReturnPolicy;
  disclaimer?: Disclaimer;
  terms?: Terms;
  facets: string[];
  customizations: Customization[];
  assets: string[];
  moreInfo?: ProductMoreInfo;
}

export interface ProductOptionGroup {
  id?: string;
  code: string;
  name: string;
  unit: string;
}

export interface ProductOption {
  id: string;
  name: string;
  code: string;
  productOptionGroupId: string;
  productOptionGroup: ProductOptionGroup;
}

export interface ProductVariantOption {
  id: string;
  productVariantId: string;
  productOptionId: string;
  productOption: ProductOption;
}

export interface ProductVariant {
  deleted: boolean;
  deletedOn: string;
  deletedBy: string;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
  id: string;
  name: string;
  enabled: boolean;
  sku: string;
  outOfStockThreshold: number;
  trackInventory: string;
  productId: string;
  featuredAssetId: string;
  taxCategoryId: string;
  product: Product;
  taxCategory: TaxCategory;
  productBoxContents: ProductBoxContent[];
  featuredAsset: Asset;
  assets: Asset[];
  productCustomizationFields: ProductCustomField[];
  productVariantPrice: ProductVariantPrice;
  productDetail: ProductDetail;
  productDisclaimer: ProductDisclaimer;
  productReturnPolicy: ProductReturnPolicy;
  productUniqueness: ProductUniqueness;
  productPersonalWork: ProductPersonalWork;
  productSpecifications: ProductSpecification[];
  productMoreInfo: ProductMoreInfo;
  productSuitability: ProductSuitability;
  productTermsAndCondition: ProductTermsAndCondition;
  productVariantAssets: ProductAsset[];
  productVariantFacetValues: ProductFacetValue[];
  productVariantOptions: ProductVariantOption[];
  isPinned?: boolean;
  pinnedProductId?: string;
}

export type GroupedOption = ProductOptionGroup & {
  options: ProductOption[];
};
export type OptionDto = Omit<ProductOption, 'productOptionGroupId' | 'productOptionGroup' | 'code'>;
export interface OptionGroupDto extends Omit<ProductOptionGroup, 'code'> {
  options: OptionDto[];
}

export interface IOptionForm {
  groups: OptionGroupDto[];
}
export interface PinnedProduct {
  id?: string;
  sellerId: string;
  productVariantId: string;
  createdOn?: string;
  updatedOn?: string;
}
