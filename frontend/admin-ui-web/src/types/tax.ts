export interface TaxCategory {
  id?: string;
  name: string;
  isDefault?: boolean;
  hsnCode?: string;
  taxRate?: number;
  createdBy?: string;
  updatedBy?: string;
  createdOn?: string;
  modifiedOn?: string;
  description?: string;
}

export interface TaxCategoryFormProps {
  isEdit?: boolean;
  taxCategoryId?: string;
  initialValues?: {
    name: string;
    hsnCode?: string;
    taxRate?: number;
    isDefault?: boolean;
    description?: string;
  };
  refetch?: () => void;
}
