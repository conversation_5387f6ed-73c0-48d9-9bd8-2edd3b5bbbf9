import { ReactNode } from 'react';
import { User } from './user-profile';

// third-party

// ==============================|| TYPES - AUTH  ||============================== //

export type GuardProps = {
  children: ReactNode;
};

export type SignupDto = {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
};

export type LoginResponse = {
  code: string;
  error?: { message: { message: string } };
};

export type SignupResponse = {
  email: string;
  password: string;
};

export interface AuthProps {
  isLoggedIn: boolean;
  isInitialized?: boolean;
  user?: User | null;
  token?: string | null;
}

export interface AuthActionProps {
  type: string;
  payload?: AuthProps;
}

export interface JWTDataProps {
  userId: string;
}

export type JWTContextType = {
  isLoggedIn: boolean;
  isInitialized?: boolean;
  user?: User | null | undefined;
  logout: () => void;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, firstName: string, lastName: string) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: VoidFunction;
};

export interface TokenResponse {
  code: string;
  accessToken: string;
  refreshToken: string;
  expires: number;
  pubnubToken: string;
}

export enum Roles {
  GUEST = 'Guest',
  SELLER = 'Seller'
}

export interface UserFormData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  password: string;
  confirmPassword: string;
  countryCode?: string;
  country?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  submit?: any;
}

export interface ISellerStore {
  id?: string;
  storeName: string;
  website?: string;
  signature?: string;
  description?: string;
  fbId?: string;
  instaId?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  country: string;
  pincode: string;
  dpBanner?: string;
  logo?: string;
  sellerId: string;
}

export interface ChangePasswordRequest {
  username: string;
  password: string;
  oldPassword: string;
  refreshToken: string;
}
