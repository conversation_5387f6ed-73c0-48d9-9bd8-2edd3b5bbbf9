import { PaymentType, RecurringInterval, ServiceType } from 'enums/ecomDukeService.enum';

export interface EcomDukeService {
  id?: string;
  name: string;
  description?: string;
  price: number;
  currency: string;
  taxCategoryId: string;
  isActive: boolean;
  fileUploadRequired?: boolean;
  serviceType?: ServiceType;
  recurringInterval?: RecurringInterval;
  paymentType?: PaymentType;
  firstPartAmount?: number | string;
  secondPartAmount?: number | string;
  lastDateToPay?: string;
  createdOn?: string;
}

export interface EcomDukeServiceRequest {
  id?: string;
  sellerId: string;
  ecomdukeserviceId: string;
  status: string;
  paymentReference?: number;
  paidAmount?: number | string;
  paidOn?: string;
  rejectionReason?: string;
  recurringPaymentDate?: string;
  notes?: string;
  assigneeId?: string;
  createdOn?: string;
  updatedOn?: string;
  createdBy?: string;
  updatedBy?: string;

  // Related: Seller
  seller?: {
    id: string;
    sellerId: string;
    userTenant?: {
      id: string;
      user?: {
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        phone: string;
        photoUrl?: string;
      };
    };
  };

  // Related: Service Info
  ecomdukeservice?: {
    id: string;
    name: string;
    description?: string;
    price: number | string;
    currency: string;
  };

  // Related: Documents uploaded by seller
  documents?: {
    id: string;
    name: string;
    url: string;
    uploadedOn?: string;
  }[];

  // Related: Assigned Sub Admin
  subAdmin?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  };

  // Related: Notes
  ecomdukeRequestNotes?: EcomdukeRequestNote[];
}

export interface EcomdukeRequestNote {
  id?: string;
  ecomdukeserviceRequestId: string;
  notes: string;
  previousStatus?: string;
  changedStatus?: string;
  createdOn?: string;
  createdBy?: string;
}
