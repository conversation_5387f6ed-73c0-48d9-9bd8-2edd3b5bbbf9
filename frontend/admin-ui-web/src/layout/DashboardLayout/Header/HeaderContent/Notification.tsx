import { useEffect, useRef, useState } from 'react';

// material-ui
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Badge from '@mui/material/Badge';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Divider from '@mui/material/Divider';
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';

// project-imports
import MainCard from 'components/MainCard';
import IconButton from 'components/@extended/IconButton';
import Transitions from 'components/@extended/Transitions';
import NotificationSubscriptionDialog from 'components/NotificationSubscriptionDialog';
import { ThemeMode } from 'config';

// redux
import { useGetUserQuery } from 'redux/auth/authApiSlice';
import {
  useGetUserNotificationsCountQuery,
  useGetUserNotificationsQuery,
  useMarkAllNotificationsAsReadMutation,
  useMarkNotificationAsReadMutation
} from 'redux/app/notification/userNotificationApiSlice';
import { useSubscribeToTopicMutation } from 'redux/app/subscription/subscriptionApiSlice';

// assets
import { Notification } from 'iconsax-react';

// types
import { UserNotification } from 'types/user-notification';
import useFcmToken from 'components/NotificationProvider';

// ==============================|| HEADER CONTENT - NOTIFICATION ||============================== //

export default function NotificationPage() {
  const fcmToken = useFcmToken();
  const theme = useTheme();
  const matchesXs = useMediaQuery(theme.breakpoints.down('md'));

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const anchorRef = useRef<any>(null);
  const [open, setOpen] = useState(false);
  const [subscriptionDialogOpen, setSubscriptionDialogOpen] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState<UserNotification | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Get user data
  const { data: user } = useGetUserQuery();
  const userTenantId = user?.userTenantId;

  // Get notifications data
  const {
    data: notifications = [],
    isLoading: notificationsLoading,
    refetch: refetchNotifications
  } = useGetUserNotificationsQuery(
    {
      userTenantId: userTenantId!,
      filter: {
        order: ['createdOn DESC'],
        limit: 20
      }
    },
    {
      skip: !userTenantId
    }
  );

  // Get unread count
  const { data: unreadCountData, refetch: refetchUnreadCount } = useGetUserNotificationsCountQuery(
    { userTenantId: userTenantId!, isRead: false },
    {
      skip: !userTenantId
    }
  );

  const unreadCount = unreadCountData?.count || 0;

  // Mutations
  const [markAsRead] = useMarkNotificationAsReadMutation();
  const [markAllAsRead] = useMarkAllNotificationsAsReadMutation();
  const [subscribeToTopic, { isLoading: isSubscribing }] = useSubscribeToTopicMutation();

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event: Event | React.SyntheticEvent) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };

  useEffect(() => {
    if (open) {
      refetchNotifications();
      refetchUnreadCount();
    }
  }, [open, refetchNotifications, refetchUnreadCount]);

  const handleNotificationClick = (notification: UserNotification) => {
    if (!notification.isRead) {
      setSelectedNotification(notification);
      setSubscriptionDialogOpen(true);
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!userTenantId) return;

    try {
      await markAllAsRead(userTenantId).unwrap();
      // Refresh both notifications and unread count
      refetchNotifications();
      refetchUnreadCount();
    } catch {
      // Failed to mark all notifications as read
    }
  };

  const handleSubscribe = async () => {
    if (!selectedNotification || !userTenantId) return;

    try {
      await subscribeToTopic({
        topic: selectedNotification.topicId,
        fcmToken: fcmToken || undefined,
        listKey: selectedNotification.listKey,
        groupId: selectedNotification.groupId,
        userTenantId: userTenantId
      }).unwrap();

      // Mark notification as read after successful subscription
      if (selectedNotification.id) {
        await markAsRead(selectedNotification.id).unwrap();
      }

      // Show success message
      setSnackbarMessage('Successfully subscribed to notifications!');
      setSnackbarOpen(true);

      // Close dialog and refresh data
      setSubscriptionDialogOpen(false);
      setSelectedNotification(null);
      refetchNotifications();
      refetchUnreadCount();
    } catch {
      setSnackbarMessage('Failed to subscribe. Please try again.');
      setSnackbarOpen(true);
    }
  };

  const handleMarkAsReadOnly = async () => {
    if (!selectedNotification?.id) return;

    try {
      await markAsRead(selectedNotification.id).unwrap();

      // Close dialog and refresh data
      setSubscriptionDialogOpen(false);
      setSelectedNotification(null);
      refetchNotifications();
      refetchUnreadCount();
    } catch {
      setSnackbarMessage('Failed to mark as read. Please try again.');
      setSnackbarOpen(true);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const iconBackColorOpen = theme.palette.mode === ThemeMode.DARK ? 'background.paper' : 'secondary.200';
  const iconBackColor = theme.palette.mode === ThemeMode.DARK ? 'background.default' : 'secondary.100';

  return (
    <Box sx={{ flexShrink: 0, ml: 0.5 }}>
      <IconButton
        color="secondary"
        variant="light"
        aria-label="open profile"
        ref={anchorRef}
        aria-controls={open ? 'profile-grow' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
        size="large"
        sx={{
          color: 'secondary.main',
          bgcolor: open ? iconBackColorOpen : iconBackColor,
          p: 1
        }}
      >
        <Badge badgeContent={unreadCount} color="error" sx={{ '& .MuiBadge-badge': { top: 2, right: 4 } }}>
          <Notification variant="Bold" />
        </Badge>
      </IconButton>
      <Popper
        placement={matchesXs ? 'bottom' : 'bottom-end'}
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [matchesXs ? -5 : 0, 9]
              }
            }
          ]
        }}
      >
        {({ TransitionProps }) => (
          <Transitions type="grow" position={matchesXs ? 'top' : 'top-right'} sx={{ overflow: 'hidden' }} in={open} {...TransitionProps}>
            <Paper
              sx={{
                boxShadow: theme.customShadows.z1,
                borderRadius: 1.5,
                width: '100%',
                minWidth: 285,
                maxWidth: 420,
                [theme.breakpoints.down('md')]: {
                  maxWidth: 285
                }
              }}
            >
              <ClickAwayListener onClickAway={handleClose}>
                <MainCard border={false} content={false}>
                  <Box sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="h6">Notifications</Typography>
                      {unreadCount > 0 && (
                        <Button size="small" onClick={handleMarkAllAsRead}>
                          Mark all as read
                        </Button>
                      )}
                    </Box>
                    <Divider />
                  </Box>
                  <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
                    {notificationsLoading ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                        <CircularProgress size={24} />
                      </Box>
                    ) : notifications.length === 0 ? (
                      <Box sx={{ p: 3, textAlign: 'center' }}>
                        <Typography variant="body2" color="textSecondary">
                          No notifications yet
                        </Typography>
                      </Box>
                    ) : (
                      <List sx={{ p: 0 }}>
                        {notifications.map((notification, index) => (
                          <Box key={notification.id || index}>
                            <ListItem
                              component="button"
                              onClick={() => handleNotificationClick(notification)}
                              sx={{
                                bgcolor: notification.isRead ? 'transparent' : 'action.hover',
                                '&:hover': {
                                  bgcolor: 'action.selected'
                                },
                                border: 'none',
                                width: '100%',
                                textAlign: 'left'
                              }}
                            >
                              <ListItemText
                                primary={
                                  <Typography
                                    variant="subtitle2"
                                    sx={{
                                      fontWeight: notification.isRead ? 'normal' : 'bold',
                                      mb: 0.5
                                    }}
                                  >
                                    {notification.notification?.subject || 'Notification'}
                                  </Typography>
                                }
                                secondary={
                                  <Box>
                                    <Typography
                                      variant="body2"
                                      color="textSecondary"
                                      sx={{
                                        display: '-webkit-box',
                                        WebkitLineClamp: 2,
                                        WebkitBoxOrient: 'vertical',
                                        overflow: 'hidden',
                                        mb: 0.5
                                      }}
                                    >
                                      {notification.notification?.body || 'No content'}
                                    </Typography>
                                    <Typography variant="caption" color="textSecondary">
                                      {formatDate(notification.createdOn)}
                                    </Typography>
                                  </Box>
                                }
                              />
                              {!notification.isRead && (
                                <Box
                                  sx={{
                                    width: 8,
                                    height: 8,
                                    borderRadius: '50%',
                                    bgcolor: 'primary.main',
                                    ml: 1
                                  }}
                                />
                              )}
                            </ListItem>
                            {index < notifications.length - 1 && <Divider />}
                          </Box>
                        ))}
                      </List>
                    )}
                  </Box>
                  <Divider />
                  <Box sx={{ p: 2 }}>
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Typography variant="h6">View all</Typography>
                      <Button size="small" onClick={handleMarkAllAsRead}>
                        Mark all as read
                      </Button>
                    </Stack>
                  </Box>
                </MainCard>
              </ClickAwayListener>
            </Paper>
          </Transitions>
        )}
      </Popper>

      {/* Subscription Dialog */}
      <NotificationSubscriptionDialog
        open={subscriptionDialogOpen}
        notification={selectedNotification}
        onClose={() => setSubscriptionDialogOpen(false)}
        onSubscribe={handleSubscribe}
        onMarkAsRead={handleMarkAsReadOnly}
        isSubscribing={isSubscribing}
      />

      {/* Success/Error Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbarMessage.includes('Failed') ? 'error' : 'success'}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
}
