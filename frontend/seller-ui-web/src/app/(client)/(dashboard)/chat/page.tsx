'use client';

import { Grid, Typography, Button } from '@mui/material';
import { AuthProvider } from 'contexts/AuthContext';
import { useFeatureAccess } from 'hooks/useFeatureAccess';
import Chat from 'views/chat/Chat';

const ChatContent = () => {
  const hasFeature = useFeatureAccess();

  if (!hasFeature('messaging')) {
    return (
      <Grid
        container
        spacing={2}
        direction="column"
        alignItems="center"
        justifyContent="center"
        sx={{ minHeight: '60vh', textAlign: 'center' }}
      >
        <Grid item>
          <Typography variant="h6" color="text.secondary">
            You don’t have access to this feature. Upgrade your plan to enable <strong>Messaging</strong>.
          </Typography>
        </Grid>
        <Grid item>
          <Button
            variant="contained"
            sx={{
              backgroundColor: '#9E3393',
              '&:hover': {
                backgroundColor: '#7C276F'
              }
            }}
            onClick={() => (window.location.href = '/account/subscription')}
          >
            Upgrade Plan
          </Button>
        </Grid>
      </Grid>
    );
  }

  return <Chat />;
};

const ChatPage = () => {
  return (
    <AuthProvider>
      <ChatContent />
    </AuthProvider>
  );
};

export default ChatPage;
