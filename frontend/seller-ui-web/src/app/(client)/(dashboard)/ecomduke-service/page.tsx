'use client';

import React, { useState } from 'react';
import { Box, Tabs, Tab } from '@mui/material';
import { AuthProvider } from 'contexts/AuthContext';
import ServiceListPage from 'views/ecomduke-service/ServiceListPage';
import ServiceRequestListPage from 'views/ecomduke-service-request/EcomDukeServiceListPage';

function TabPanel(props: { children: React.ReactNode; value: number; index: number }) {
  const { children, value, index } = props;
  return value === index ? <Box>{children}</Box> : null;
}

export default function SamplePage() {
  const [value, setValue] = useState(0);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <AuthProvider>
      <Box sx={{ width: '100%' }}>
        <Box
          sx={{
            position: 'sticky',
            top: '92px',
            zIndex: 1100,
            backgroundColor: '#fff',
            boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
            borderRadius: 2,
            height: '50px'
          }}
        >
          <Tabs value={value} onChange={handleTabChange}>
            <Tab label="EcomDukes Services" />
            <Tab label="My Service Requests" />
          </Tabs>
        </Box>

        {/* Tab content */}
        <Box>
          <TabPanel value={value} index={0}>
            <ServiceRequestListPage />
          </TabPanel>
          <TabPanel value={value} index={1}>
            <ServiceListPage />
          </TabPanel>
        </Box>
      </Box>
    </AuthProvider>
  );
}
