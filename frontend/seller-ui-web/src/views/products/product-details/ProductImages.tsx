import { useEffect, useState } from 'react';

// material-ui
import { alpha, useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import CardMedia from '@mui/material/CardMedia';
import Box from '@mui/material/Box';
import Avatar from 'components/@extended/Avatar';

// project-imports
import MainCard from 'components/MainCard';
import IconButton from 'components/@extended/IconButton';

// third-party
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';

// assets
import { ArrowRotateRight, Play, SearchZoomIn, SearchZoomOut } from 'iconsax-react';

// types

import { Product } from 'types/product';
import LazyVideo from '../Form/LazyVideo';

export default function ProductImages({ product }: { product: Product }) {
  const theme = useTheme();
  const matchDownLG = useMediaQuery(theme.breakpoints.up('lg'));
  const [selectedId, setSelectedId] = useState('');
  const [modal, setModal] = useState(false);
  const backColor = alpha(theme.palette.primary.lighter, 0);

  useEffect(() => {
    const firstAsset = product?.productAssets?.[0]?.asset;
    setSelectedId(firstAsset?.id ?? '');
  }, [product]);

  const selectedAsset = product.productAssets?.find((pa) => pa.asset.id === selectedId)?.asset;
  const isVideo = selectedAsset?.mimeType?.startsWith('video');

  return (
    <Grid container spacing={0.5}>
      <Grid item xs={12}>
        <MainCard
          content={false}
          border={false}
          boxShadow={false}
          sx={{
            m: '0 auto',
            width: '100%',
            maxWidth: '100%',
            maxHeight: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',

            overflow: 'hidden',
            bgcolor: backColor
          }}
        >
          <TransformWrapper initialScale={1} smooth>
            {({ zoomIn, zoomOut, resetTransform }) => (
              <>
                <TransformComponent
                  wrapperStyle={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    justifyContent: 'start',
                    alignItems: 'start'
                  }}
                >
                  <Box
                    sx={{
                      width: '100%',
                      aspectRatio: '1',
                      maxHeight: 500,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      overflow: 'hidden',
                      position: 'relative'
                    }}
                  >
                    {isVideo ? (
                      <LazyVideo
                        key={selectedAsset?.id}
                        src={selectedAsset?.previewUrl ?? ''}
                        width="100%"
                        height="100%"
                        autoPlay={true}
                        muted={true}
                        loop={true}
                        controls={true}
                        style={{
                          objectFit: 'contain'
                        }}
                      />
                    ) : (
                      <CardMedia
                        component="img"
                        image={selectedAsset?.previewUrl ?? ''}
                        title="Scroll Zoom"
                        onClick={() => setModal(!modal)}
                        sx={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'contain',
                          borderRadius: '4px'
                        }}
                      />
                    )}
                  </Box>
                </TransformComponent>

                {!isVideo && (
                  <Stack
                    direction="row"
                    sx={{
                      position: 'absolute',
                      bottom: 10,
                      right: 10,
                      zIndex: 1,
                      backgroundColor: 'rgba(255, 255, 255, 0.8)',
                      borderRadius: 1
                    }}
                  >
                    <IconButton color="secondary" onClick={() => zoomIn()}>
                      <SearchZoomIn style={{ fontSize: '1.15rem' }} />
                    </IconButton>

                    <IconButton color="secondary" onClick={() => zoomOut()}>
                      <SearchZoomOut style={{ fontSize: '1.15rem' }} />
                    </IconButton>

                    <IconButton color="secondary" onClick={() => resetTransform()}>
                      <ArrowRotateRight style={{ fontSize: '1.15rem' }} />
                    </IconButton>
                  </Stack>
                )}
              </>
            )}
          </TransformWrapper>
        </MainCard>
      </Grid>

      <Grid item xs={12}>
        <Box sx={{ mt: 2, position: 'relative' }}>
          <Box
            id="thumbnail-scroll"
            sx={{
              overflowX: 'auto',
              whiteSpace: 'nowrap',
              display: 'flex',
              gap: 3,
              scrollbarWidth: 'none',
              '&::-webkit-scrollbar': { display: 'none' },
              px: 1.5
            }}
          >
            {product.productAssets?.map((item, index) => {
              const asset = item?.asset;
              const isVideo = asset?.mimeType?.startsWith('video');
              const previewUrl = asset?.previewUrl;
              const isSelected = selectedId === asset.id;

              return (
                <Box
                  key={index}
                  sx={{
                    position: 'relative',
                    display: 'inline-block',
                    width: 80,
                    height: 80,
                    mr: 1,
                    mb: 1,
                    cursor: 'pointer',
                    border: isSelected ? `2px solid ${theme.palette.primary.main}` : '2px solid transparent',
                    borderRadius: 1,
                    overflow: 'hidden'
                  }}
                  onClick={() => setSelectedId(asset.id)}
                >
                  {isVideo ? (
                    <Box
                      sx={{
                        width: '100%',
                        height: '100%',
                        backgroundColor: 'rgb(67, 63, 63)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: 1
                      }}
                    >
                      <Play color="#FFFFFF" size="32" />
                    </Box>
                  ) : (
                    <Avatar
                      variant="rounded"
                      size={matchDownLG ? 'xl' : 'md'}
                      src={previewUrl}
                      alt="Asset"
                      sx={{
                        width: '100%',
                        height: '100%',
                        bgcolor: theme.palette.secondary[200]
                      }}
                    />
                  )}
                </Box>
              );
            })}
          </Box>
        </Box>
      </Grid>
    </Grid>
  );
}
