import { Autocomplete, Chip, Icon<PERSON>utton, Input<PERSON>abel } from '@mui/material';
import { Checkbox, TextField, Grid, Select, MenuItem, FormControlLabel, FormControl } from '@mui/material';
import MainCard from 'components/MainCard';
import { FormikProps } from 'formik';
import { Trash } from 'iconsax-react';
import { FC } from 'react';
import { CustomizationOption, FieldType, ProductDto } from 'types/product-dto';
import { ProductVariantUpdateDto } from 'types/product-variant';

interface Props {
  formik: FormikProps<ProductDto> | FormikProps<ProductVariantUpdateDto>;
}

export const CustomizationForm: FC<Props> = ({ formik }) => {
  return (
    <Grid container spacing={2} sx={{ p: 2 }} rowGap={2}>
      {formik.values.customizations.map((customization, idx) => (
        <Grid item xs={12} key={idx}>
          <MainCard
            content={false}
            title={`Customization ${idx + 1}`}
            sx={{ padding: 2, margin: 0 }}
            secondary={
              <IconButton
                color="error"
                onClick={() => {
                  formik.setFieldValue(
                    'customizations',
                    formik.values.customizations.filter((_, index) => index !== idx)
                  );
                }}
              >
                <Trash />
              </IconButton>
            }
          >
            <Grid container spacing={2} mt={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Name"
                  name={`customizations[${idx}].name`}
                  value={customization.name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    !!formik.touched.customizations?.[idx]?.name &&
                    typeof formik.errors.customizations?.[idx] === 'object' &&
                    !!formik.errors.customizations?.[idx]?.name
                  }
                  helperText={
                    formik.touched.customizations?.[idx]?.name &&
                    typeof formik.errors.customizations?.[idx] === 'object' &&
                    formik.errors.customizations?.[idx]?.name
                  }
                  placeholder="Eg Frame Color"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Label"
                  name={`customizations[${idx}].label`}
                  value={customization.label}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    !!formik.touched.customizations?.[idx]?.label &&
                    typeof formik.errors.customizations?.[idx] === 'object' &&
                    !!formik.errors.customizations?.[idx]?.label
                  }
                  helperText={
                    formik.touched.customizations?.[idx]?.label &&
                    typeof formik.errors.customizations?.[idx] === 'object' &&
                    formik.errors.customizations?.[idx]?.label
                  }
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Placeholder"
                  name={`customizations[${idx}].placeholder`}
                  value={customization.placeholder}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    !!formik.touched.customizations?.[idx]?.placeholder &&
                    typeof formik.errors.customizations?.[idx] === 'object' &&
                    !!formik.errors.customizations?.[idx]?.placeholder
                  }
                  helperText={
                    formik.touched.customizations?.[idx]?.placeholder &&
                    typeof formik.errors.customizations?.[idx] === 'object' &&
                    formik.errors.customizations?.[idx]?.placeholder
                  }
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Checkbox
                      name={`customizations[${idx}].isRequired`}
                      checked={customization.isRequired}
                      onChange={formik.handleChange}
                    />
                  }
                  label="Required"
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Field Type</InputLabel>
                  <Select
                    name={`customizations[${idx}].fieldType`}
                    value={customization.fieldType}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  >
                    <MenuItem value="text">Text</MenuItem>
                    <MenuItem value="dropdown">Dropdown</MenuItem>
                    <MenuItem value="checkbox">Checkbox</MenuItem>
                    <MenuItem value="radio">Radio</MenuItem>
                    <MenuItem value="file">File Upload</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              {[FieldType.DROPDOWN, FieldType.RADIO, FieldType.CHECKBOX].includes(customization.fieldType) && (
                <Grid item xs={12} md={8}>
                  <Autocomplete
                    multiple
                    freeSolo
                    options={[]}
                    value={formik.values.customizations[idx].options?.map((v: CustomizationOption) => v.label) || []}
                    onChange={(_, newValue) => {
                      formik.setFieldValue(
                        `customizations[${idx}].options`,
                        newValue.map((val, id) => ({ label: val, value: val.toLowerCase(), id }))
                      );
                    }}
                    renderTags={(value: readonly string[], getTagProps) =>
                      value.map((option, index) => <Chip variant="outlined" label={option} {...getTagProps({ index })} key={index} />)
                    }
                    size="medium"
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        size="medium"
                        label="Option Values"
                        placeholder="Enter and press enter"
                        error={!!formik.touched.customizations?.[idx]?.options && !!(formik.errors.customizations?.[idx] as any)?.options}
                        helperText={formik.touched.customizations?.[idx]?.options && (formik.errors.customizations?.[idx] as any)?.options}
                      />
                    )}
                  />
                </Grid>
              )}
            </Grid>
          </MainCard>
        </Grid>
      ))}
    </Grid>
  );
};
