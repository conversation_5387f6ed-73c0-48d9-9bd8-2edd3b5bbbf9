import { Box, Button, CardActions, CardContent, Checkbox, ImageList, ImageListItem, Modal, Stack } from '@mui/material';
import MainCard from 'components/MainCard';
import Image from 'next/image';
import { Dispatch, FC, SetStateAction } from 'react';
import { Asset } from 'types/product';
import { AddAsset } from './AddAsset';
import { LoadingButton } from '@mui/lab';
import InfiniteScroll from 'react-infinite-scroll-component';
import Loader from 'components/Loader';
import LazyVideo from './LazyVideo';

interface Props {
  imageUrl?: string;
  altText?: string;
  openGallery: boolean;
  assets: Asset[];
  toggleGallery: () => void;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  isAssetUploading: boolean;
  setSelectedAssets: Dispatch<SetStateAction<{ id: string; preview: string; mimeType: string }[]>>;
  selectedAssets: { id: string; preview: string; mimeType: string }[];
  handleFeatureAssetChange: (id: string) => void;
  featuredAssetId: string;
  assetsCount: number;
  hasMore: boolean;
  loadMoreAssets: () => void;
}

export const ProductAsset: FC<Props> = ({
  imageUrl,
  altText,
  openGallery,
  assets,
  toggleGallery,
  handleFileChange,
  isAssetUploading,
  setSelectedAssets,
  selectedAssets,
  handleFeatureAssetChange,
  featuredAssetId,
  assetsCount,
  hasMore,
  loadMoreAssets
}) => {
  const handleToggle = (id: string, preview: string, mimeType: string) => {
    setSelectedAssets((prev) => {
      const exists = prev.some((item) => item.id === id);
      if (exists) return prev.filter((item) => item.id !== id);
      return [...prev, { id, preview, mimeType }];
    });
  };

  const featuredAsset = selectedAssets.find((a) => a.id === featuredAssetId);

  return (
    <MainCard title="Assets">
      <Stack direction="row" spacing={2} alignItems="flex-start">
        <Box
          sx={{
            width: 210,
            height: 210,
            position: 'relative',
            borderRadius: 1,
            overflow: 'hidden',
            border: '1px solid #ccc'
          }}
        >
          {featuredAsset ? (
            featuredAsset.mimeType?.startsWith('image') ? (
              <Image src={featuredAsset.preview} alt={altText ?? 'Featured Media'} fill style={{ objectFit: 'cover' }} />
            ) : (
              <LazyVideo
                key={featuredAsset.id}
                src={featuredAsset.preview}
                type={featuredAsset.mimeType}
                width="100%"
                height="100%"
                autoPlay={true}
                muted={true}
                loop={true}
                controls={true}
                style={{ objectFit: 'contain' }}
              />
            )
          ) : (
            <Image
              src={imageUrl ?? '/assets/images/icons/image_placeholder.png'}
              alt={altText ?? 'Featured Media'}
              fill
              style={{ objectFit: 'cover' }}
            />
          )}
        </Box>

        {/* Thumbnail Strip + Button */}
        <Stack spacing={1}>
          <Stack direction="row" spacing={1}>
            {selectedAssets.map((item) => {
              const isFeatured = featuredAssetId === item.id;
              const isImage = item.mimeType?.startsWith('image');

              return (
                <Box
                  key={item.id}
                  onClick={() => handleFeatureAssetChange(item.id)}
                  sx={{
                    width: 50,
                    height: 50,
                    position: 'relative',
                    borderRadius: 1,
                    overflow: 'hidden',
                    border: isFeatured ? '2px solid #1976d2' : '1px solid #ccc',
                    cursor: 'pointer'
                  }}
                >
                  {isImage ? (
                    <Image src={item.preview} alt={item.id} fill style={{ objectFit: 'cover' }} />
                  ) : (
                    <LazyVideo src={item.preview} type={item.mimeType} width={50} height={50} controls={false} muted />
                  )}
                </Box>
              );
            })}
          </Stack>
          <Stack direction="row" justifyContent="flex-end">
            <LoadingButton variant="outlined" onClick={toggleGallery} loading={isAssetUploading}>
              Add Media
            </LoadingButton>
          </Stack>
        </Stack>
      </Stack>

      {/* Modal */}
      <Modal open={openGallery}>
        <MainCard title="Choose Media" modal darkTitle content={false} secondary={<AddAsset handleChange={handleFileChange} />}>
          <CardContent
            id="scrollableAssets"
            sx={{
              height: 300, // or any height that ensures scroll
              overflowY: 'auto'
            }}
          >
            <InfiniteScroll
              dataLength={assetsCount}
              loader={<Loader />}
              scrollThreshold={0.9}
              next={loadMoreAssets}
              hasMore={hasMore}
              scrollableTarget="scrollableAssets"
              initialScrollY={0}
            >
              <ImageList sx={{ width: 500 }} cols={4} rowHeight={164}>
                {assets.map((item) => {
                  const isSelected = selectedAssets.some((asset) => asset.id === item.id);
                  const isImage = item.mimeType?.startsWith('image');
                  const imageSelected = selectedAssets.some((a) => a.mimeType?.startsWith('image'));
                  const isDisabled = !isImage && !imageSelected;

                  return (
                    <ImageListItem
                      key={item.id}
                      onClick={() => {
                        if (!isDisabled) handleToggle(item.id, item.previewUrl, item.mimeType);
                      }}
                      sx={{
                        position: 'relative',
                        border: isSelected && !isDisabled ? '2px solid #1976d2' : '2px solid transparent',
                        borderRadius: 1,
                        cursor: isDisabled ? 'not-allowed' : 'pointer',
                        overflow: 'hidden',
                        opacity: isDisabled ? 0.5 : 1,
                        pointerEvents: isDisabled ? 'none' : 'auto'
                      }}
                    >
                      {isImage ? (
                        <Image
                          width={100}
                          height={100}
                          src={item.previewUrl}
                          alt={item.name}
                          loading="lazy"
                          style={{ objectFit: 'cover' }}
                        />
                      ) : (
                        <LazyVideo src={item.previewUrl} type={item.mimeType} width={100} height={100} controls={false} muted />
                      )}
                      <Box
                        sx={{
                          position: 'absolute',
                          backgroundColor: 'rgba(255,255,255,0.7)',
                          borderRadius: '50%'
                        }}
                      >
                        <Checkbox checked={isSelected} sx={{ padding: 0.5 }} color="primary" />
                      </Box>
                    </ImageListItem>
                  );
                })}
              </ImageList>
            </InfiniteScroll>
          </CardContent>
          <CardActions sx={{ justifyContent: 'flex-end' }}>
            <Button onClick={toggleGallery} color="secondary" variant="outlined">
              Cancel
            </Button>
            <LoadingButton variant="outlined" onClick={toggleGallery} loading={isAssetUploading} disabled={!selectedAssets?.length}>
              {selectedAssets?.length ? `Add ${selectedAssets?.length} Media` : 'Add Media'}
            </LoadingButton>
          </CardActions>
        </MainCard>
      </Modal>
    </MainCard>
  );
};
