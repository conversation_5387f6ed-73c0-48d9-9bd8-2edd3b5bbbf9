import { Button, Grid, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import { FormikProps } from 'formik';
import { FC } from 'react';
import { FieldType, ProductDto } from 'types/product-dto';
import { CustomizationForm } from './CustomizationForm';
import { ProductVariantUpdateDto } from 'types/product-variant';
import { useFeatureAccess } from 'hooks/useFeatureAccess';
import { Box } from '@mui/material';

interface ProductVariantFormProps {
  formik: FormikProps<ProductDto> | FormikProps<ProductVariantUpdateDto>;
}
export const ProductCustomizationForm: FC<ProductVariantFormProps> = ({ formik }) => {
  const hasFeature = useFeatureAccess();

  const handleAddCustomization = () => {
    formik.setFieldValue('customizations', [
      ...formik.values.customizations,
      {
        name: '',
        label: '',
        placeholder: '',
        fieldType: FieldType.TEXT,
        isRequired: false,
        options: [],
        values: []
      }
    ]);
  };

  return (
    <MainCard title="Product Customization" content={false} sx={{ padding: 0, margin: 0 }}>
      {hasFeature('product-customisation-&-personalisation-options') ? (
        <Grid container spacing={2} sx={{ p: 2 }} rowGap={2}>
          <Grid item xs={12}>
            <Button variant="outlined" onClick={handleAddCustomization}>
              Add Customization
            </Button>
          </Grid>
          {!!formik.values.customizations?.length && (
            <Grid item xs={12}>
              <CustomizationForm formik={formik} />
            </Grid>
          )}
        </Grid>
      ) : (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            py: 4,
            gap: 2,
            textAlign: 'center'
          }}
        >
          <Grid item>
            <Typography variant="h6" color="text.secondary">
              You don’t have access to this feature. Upgrade your plan to enable <strong>Customization</strong>.
            </Typography>
          </Grid>
          <Button
            variant="contained"
            sx={{
              backgroundColor: '#9E3393',
              '&:hover': {
                backgroundColor: '#7C276F'
              }
            }}
            onClick={() => (window.location.href = '/account/subscription')}
          >
            Upgrade Plan
          </Button>
        </Box>
      )}
    </MainCard>
  );
};
