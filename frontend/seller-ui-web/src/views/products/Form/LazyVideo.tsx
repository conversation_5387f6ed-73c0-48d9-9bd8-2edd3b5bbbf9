'use client';
import { useEffect, useRef, useState } from 'react';

type LazyVideoProps = {
  src: string;
  type?: string;
  width?: number | string;
  height?: number | string;
  poster?: string;
  autoPlay?: boolean;
  muted?: boolean;
  loop?: boolean;
  controls?: boolean;
  style?: React.CSSProperties;
};

export default function LazyVideo({
  src,
  type = 'video/mp4',
  width = '100%',
  height = 'auto',
  poster,
  autoPlay = false,
  muted = true,
  loop = true,
  controls = true,
  style
}: LazyVideoProps) {
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const placeholderRef = useRef<HTMLDivElement | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.25 }
    );

    const target = placeholderRef.current ?? videoRef.current;
    if (target) observer.observe(target);

    return () => observer.disconnect();
  }, []);

  return (
    <div style={{ width, height }}>
      {isVisible ? (
        <video
          ref={videoRef}
          width="100%"
          height="100%"
          autoPlay={autoPlay}
          muted={muted}
          loop={loop}
          controls={controls}
          poster={poster}
          playsInline
          style={style}
        >
          <source src={src} type={type} />
          Your browser does not support the video tag.
        </video>
      ) : (
        <div
          ref={placeholderRef}
          style={{
            width: '100%',
            height: height || '300px',
            backgroundColor: '#000',
            backgroundImage: poster ? `url(${poster})` : undefined,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        />
      )}
    </div>
  );
}
