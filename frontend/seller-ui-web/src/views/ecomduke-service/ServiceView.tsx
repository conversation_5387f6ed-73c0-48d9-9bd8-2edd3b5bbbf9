'use client';

import React, { useEffect, useRef, useState } from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Stack,
  Snackbar,
  Alert
} from '@mui/material';
import Loader from 'components/Loader';
import {
  useCreatePaymentLinkMutation,
  useGetServiceRequestByIdQuery,
  useGetServicesQuery,
  useLazyDownloadServiceRequestInvoicePdfQuery,
  useUpdateServiceRequestMutation
} from 'redux/app/service-request/requestApislice';
import { Add, BagCross, DocumentText1, DocumentUpload } from 'iconsax-react';
import { useFileUploadOnboardMutation } from 'redux/ecom/ecomApiSlice';
import { PaymentType } from 'enums/ecomduke-service.enum';

type Props = {
  requestId: string;
  refetchParent: () => void;
};

const ViewServiceRequest = ({ requestId, refetchParent }: Props) => {
  const { data, isLoading, error, refetch } = useGetServiceRequestByIdQuery({ id: requestId });
  const { data: allServices = [] } = useGetServicesQuery();
  const [updateDocuments] = useUpdateServiceRequestMutation();
  const [uploadFile] = useFileUploadOnboardMutation();
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const service = allServices.find((s: any) => s.id === data?.ecomdukeserviceId);
  const serviceName = service?.name ?? 'N/A';
  const [createPaymentLink] = useCreatePaymentLinkMutation();
  const [uploadDocs, setUploadedDocs] = useState<string[]>([]);

  useEffect(() => {
    refetch();
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const MAX_FILE_SIZE = 3 * 1024 * 1024; // 3MB

    const files = Array.from(e.target.files || []);
    const validFiles = files.filter((file) => {
      if (file.size > MAX_FILE_SIZE) {
        setSnackbarMessage(`${file.name} exceeds the 3MB limit`);
        setSnackbarOpen(true);
        return false;
      }
      return true;
    });

    const newPreviews = validFiles.map((file) => URL.createObjectURL(file));

    setSelectedFiles((prev) => [...prev, ...validFiles]);
    setPreviewUrls((prev) => [...prev, ...newPreviews]);
  };

  const [triggerDownloadPdf] = useLazyDownloadServiceRequestInvoicePdfQuery();

  const handleDownloadInvoice = async (requestId: string) => {
    try {
      const response = await triggerDownloadPdf(requestId).unwrap();
      const blob = new Blob([response], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `invoice-${requestId}.pdf`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download invoice PDF:', error);
    }
  };

  const handleDeleteFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
    setPreviewUrls((prev) => prev.filter((_, i) => i !== index));
  };

  useEffect(() => {
    return () => {
      previewUrls.forEach((url) => URL.revokeObjectURL(url));
    };
  }, [previewUrls]);

  const handleUpload = async () => {
    if (!selectedFiles.length || !data?.id) return;

    try {
      setUploading(true);

      const uploadedUrls: string[] = [];

      for (const file of selectedFiles) {
        const cleanName = file.name.replace(/[^a-zA-Z0-9._-]/g, '_');
        const cleanFile = new File([file], cleanName, { type: file.type });

        const formData = new FormData();
        formData.append('file', cleanFile);

        const uploadResponse = await uploadFile({
          fileUploadOnboard: { file: formData }
        }).unwrap();

        if (uploadResponse?.file?.key) {
          uploadedUrls.push(uploadResponse.file.key);
        }
      }

      await updateDocuments({
        id: data.id,
        data: { documents: uploadedUrls }
      }).unwrap();

      refetch();
      refetchParent();
      setSelectedFiles([]);
    } finally {
      setUploading(false);
    }
  };

  // const handleDeleteUploadedFile = async (index: number) => {
  //   if (!data?.id) return;

  //   const updatedDocs = [...uploadedDocs];
  //   updatedDocs.splice(index, 1);

  //   try {
  //     await updateDocuments({
  //       id: data.id,
  //       data: { documents: updatedDocs }
  //     }).unwrap();
  //   } catch (error) {
  //     console.error('Failed to delete document', error);
  //   }
  // };

  const handleDeleteUploadedFile = async (index: number) => {
    if (!data?.id) return;

    const updatedDocs = [...uploadedDocs];
    updatedDocs.splice(index, 1); // remove file at index

    try {
      await updateDocuments({
        id: data.id,
        data: { documents: updatedDocs }
      }).unwrap();

      setUploadedDocs(updatedDocs); // optional: refetch(); if needed elsewhere
    } catch (error) {
      console.error('Failed to delete document', error);
    }
  };

  const handlePayNow = async () => {
    if (!data?.id || !service) return;

    const paidAmount = service.paymentType === PaymentType.Partial ? Number(service.secondPartAmount) : Number(service.price);

    try {
      const paymentResponse = await createPaymentLink({
        linkAmount: paidAmount,
        linkNotes: {
          ecomdukeserviceRequestId: data.id
        }
      }).unwrap();

      if (paymentResponse?.link_url) {
        window.location.href = paymentResponse.link_url;
      }
    } catch (error) {
      console.error('Payment link creation failed', error);
    }
    refetch();
  };

  const handleCancelRecurring = async () => {
    if (!data?.id) return;

    try {
      await updateDocuments({
        id: data.id,
        data: { status: 'Cancelled' }
      }).unwrap();

      refetch();
      refetchParent();
    } catch (error) {
      console.error('Failed to cancel recurring service', error);
    }
  };

  if (isLoading) return <Loader />;
  if (error || !data) return <Typography color="error">Error fetching service request.</Typography>;

  const uploadedDocs = data.documents ?? [];

  return (
    <Grid container spacing={2} sx={{ mt: 1 }}>
      <Grid item xs={12}>
        <Card sx={{ p: 2, boxShadow: 3 }}>
          <CardContent>
            <Typography variant="h4" gutterBottom>
              Service Request Details
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <DisplayItem label="Service Name" value={serviceName} />
              </Grid>
              <Grid item xs={12} md={6}>
                <DisplayItem
                  label="Seller"
                  value={
                    data.seller?.userTenant?.user ? `${data.seller.userTenant.user.firstName} ${data.seller.userTenant.user.lastName}` : '-'
                  }
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <DisplayItem label="Status" value={data.status} />
              </Grid>
              {data.paidAmount !== undefined && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Paid Amount" value={data.paidAmount} />
                </Grid>
              )}
              {data.paymentReference && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Payment Reference" value={data.paymentReference} />
                </Grid>
              )}
              {data.paidOn && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Paid On" value={new Date(data.paidOn).toLocaleString()} />
                </Grid>
              )}
              {data.notes && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Notes" value={data.notes} isMultiline />
                </Grid>
              )}
              {data.rejectionReason && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Rejection Reason" value={data.rejectionReason} isMultiline />
                </Grid>
              )}
              {data.status !== 'Action Required' && uploadedDocs.length > 0 && (
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                    Documents:
                  </Typography>

                  <Stack direction="row" spacing={3} flexWrap="wrap">
                    {uploadedDocs.map((docUrl, idx) => {
                      const isImage = /\.(png|jpe?g|gif)$/i.test(docUrl);
                      const fileName = decodeURIComponent(docUrl.split('/').pop() ?? `Document-${idx + 1}`);

                      return (
                        <Box
                          key={idx}
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1,
                            p: 1,
                            borderRadius: 2,
                            backgroundColor: '#f9f9f9'
                          }}
                        >
                          {isImage ? (
                            <img
                              src={docUrl}
                              alt={fileName}
                              style={{
                                width: 40,
                                height: 40,
                                objectFit: 'cover',
                                borderRadius: 4,
                                border: '1px solid #ccc'
                              }}
                            />
                          ) : (
                            <Box
                              sx={{
                                width: 40,
                                height: 40,
                                backgroundColor: '#eee',
                                borderRadius: 1,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              <DocumentText1 size={28} color="#555" />
                            </Box>
                          )}

                          <a
                            href={docUrl}
                            target="_blank"
                            rel="noreferrer"
                            style={{
                              textDecoration: 'underline',
                              color: '#1976d2',
                              wordBreak: 'break-all',
                              maxWidth: 120
                            }}
                          >
                            {fileName}
                          </a>
                        </Box>
                      );
                    })}
                  </Stack>
                </Grid>
              )}
            </Grid>
            {Array.isArray(data.ecomdukeRequestNotes) && data.ecomdukeRequestNotes.length > 0 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Request Notes
                </Typography>
                <Stack spacing={2}>
                  {data.ecomdukeRequestNotes.map((note) => (
                    <Box
                      key={note.id}
                      sx={{
                        p: 2,
                        background: '#f9f9f9',
                        borderRadius: 2,
                        border: '1px solid #e0e0e0'
                      }}
                    >
                      <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                        {note.notes}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Status Changed: {note.previousStatus ?? '-'} → {note.changedStatus ?? '-'}
                      </Typography>
                    </Box>
                  ))}
                </Stack>
              </Box>
            )}

            {data.status === 'Action Required' && service?.fileUploadRequired && (
              <Box sx={{ mt: 4 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Upload Required Documents
                </Typography>
                {uploadedDocs.length > 0 && (
                  <Grid item xs={12} md={12}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mt: 2, mb: 1 }}>
                      Uploaded Documents
                    </Typography>
                    <List dense>
                      {uploadedDocs.map((docUrl, idx) => {
                        const isImage = /\.(png|jpe?g|gif)$/i.test(docUrl);
                        const fileName = decodeURIComponent(docUrl.split('/').pop() ?? `Document-${idx + 1}`);

                        return (
                          <ListItem key={idx} sx={{ display: 'flex', alignItems: 'center' }}>
                            {/* Thumbnail / Icon */}
                            {isImage ? (
                              <img
                                src={docUrl}
                                alt={fileName}
                                style={{
                                  width: 60,
                                  height: 60,
                                  objectFit: 'cover',
                                  marginRight: 16,
                                  borderRadius: 4,
                                  border: '1px solid #ccc'
                                }}
                              />
                            ) : (
                              <Box
                                sx={{
                                  width: 40,
                                  height: 40,
                                  backgroundColor: '#eee',
                                  marginRight: 2,
                                  borderRadius: 1,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}
                              >
                                <DocumentText1 size={28} color="#555" />
                              </Box>
                            )}

                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <a
                                    href={docUrl}
                                    target="_blank"
                                    rel="noreferrer"
                                    style={{ textDecoration: 'underline', color: '#1976d2', wordBreak: 'break-all' }}
                                  >
                                    {fileName}
                                  </a>
                                  {data.status === 'Action Required' && (
                                    <IconButton size="small" onClick={() => handleDeleteUploadedFile(idx)}>
                                      <BagCross fontSize="small" color="red" />
                                    </IconButton>
                                  )}
                                </Box>
                              }
                            />
                          </ListItem>
                        );
                      })}
                    </List>
                  </Grid>
                )}

                {/* Select files */}
                <input
                  type="file"
                  ref={fileInputRef}
                  multiple
                  hidden
                  onChange={handleFileSelect}
                  accept=".pdf,.doc,.docx,.png,.jpg,.jpeg"
                />
                <Button variant="outlined" startIcon={<Add />} onClick={() => fileInputRef.current?.click()} sx={{ mb: 2 }}>
                  Select Files
                </Button>

                {selectedFiles.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>
                      Files to Upload:
                    </Typography>
                    <List dense sx={{ maxWidth: 500 }}>
                      {selectedFiles.map((file, index) => {
                        const isImage = file.type.startsWith('image/');

                        return (
                          <ListItem key={index} sx={{ display: 'flex', alignItems: 'center' }}>
                            {/* Thumbnail / Icon */}
                            {isImage ? (
                              <img
                                src={previewUrls[index]}
                                alt={file.name}
                                style={{
                                  width: 60,
                                  height: 60,
                                  objectFit: 'cover',
                                  marginRight: 16,
                                  borderRadius: 4,
                                  border: '1px solid #ccc'
                                }}
                              />
                            ) : (
                              <Box
                                sx={{
                                  width: 40,
                                  height: 40,
                                  backgroundColor: '#eee',
                                  marginRight: 2,
                                  borderRadius: 1,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}
                              >
                                <DocumentText1 size={28} color="#555" />
                              </Box>
                            )}

                            {/* File name and delete inline */}
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Typography sx={{ wordBreak: 'break-all' }}>{file.name}</Typography>
                                  <IconButton size="small" onClick={() => handleDeleteFile(index)}>
                                    <BagCross fontSize="small" color="red" />
                                  </IconButton>
                                </Box>
                              }
                            />
                          </ListItem>
                        );
                      })}
                    </List>
                  </Box>
                )}

                {/* Upload button */}
                {selectedFiles.length > 0 && (
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<DocumentUpload />}
                    onClick={handleUpload}
                    disabled={uploading}
                    sx={{ mt: 2 }}
                  >
                    {uploading ? 'Uploading...' : 'Upload'}
                  </Button>
                )}
              </Box>
            )}

            {data.status === 'Upcoming' && (
              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                <Button variant="contained" color="primary" onClick={handlePayNow}>
                  Pay Now
                </Button>
              </Box>
            )}

            <Grid container alignItems="center" justifyContent="space-between" mt={2}>
              {data.paidOn && (
                <Grid item>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Typography variant="subtitle2" fontWeight={600}>
                      Invoice:
                    </Typography>
                    <Button
                      variant="contained"
                      onClick={() => handleDownloadInvoice(data.id ?? '')}
                      sx={{
                        borderRadius: '20px',
                        backgroundColor: '#151B54',
                        fontSize: '12px',
                        padding: '6px 16px',
                        textTransform: 'none'
                      }}
                    >
                      Download
                    </Button>
                  </Box>
                </Grid>
              )}

              {service?.serviceType === 'Recurring' && data.status !== 'Cancelled' && data.status !== 'Completed' && (
                <Grid item>
                  <Button
                    variant="outlined"
                    color="error"
                    onClick={handleCancelRecurring}
                    sx={{ borderRadius: '20px', fontSize: '12px', padding: '6px 16px', textTransform: 'none' }}
                  >
                    Cancel Service Request
                  </Button>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>
      </Grid>
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={() => setSnackbarOpen(false)} severity="warning" sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Grid>
  );
};

const DisplayItem = ({ label, value, isMultiline = false }: { label: string; value: string | number; isMultiline?: boolean }) => (
  <Box sx={{ mb: 1.5 }}>
    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
      {label}:
    </Typography>
    <Box
      sx={{
        backgroundColor: '#f5f5f5',
        px: 2,
        py: 1.2,
        borderRadius: 2,
        fontSize: '0.95rem',
        color: 'text.primary',
        whiteSpace: isMultiline ? 'pre-line' : 'nowrap'
      }}
    >
      {value}
    </Box>
  </Box>
);

export default ViewServiceRequest;
