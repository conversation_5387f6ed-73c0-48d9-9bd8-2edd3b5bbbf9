'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Box, Button, Card, CardContent, Grid, MenuItem, TextField, Typography, CircularProgress } from '@mui/material';
import { useFormik } from 'formik';
import {
  useCreateServiceRequestMutation,
  useGetServicesQuery,
  useCreatePaymentLinkMutation
} from 'redux/app/service-request/requestApislice';
import { PaymentType } from 'enums/ecomduke-service.enum';
import { ServiceRequestStatus } from 'types/service-request';

export default function EcomDukeserviceRequestCreate() {
  const [createRequest] = useCreateServiceRequestMutation();
  const [createPaymentLink] = useCreatePaymentLinkMutation();
  const { data: services, isLoading, refetch } = useGetServicesQuery();

  const searchParams = useSearchParams();
  const defaultServiceId = searchParams.get('ecomdukeserviceId') || '';

  const [selectedPaymentMode, setSelectedPaymentMode] = useState<'full' | 'partial'>('full');

  const formik = useFormik({
    initialValues: {
      ecomdukeserviceId: '',
      notes: ''
    },
    onSubmit: async (values, { resetForm }) => {
      const selectedService = services?.find((s) => s.id === values.ecomdukeserviceId);
      if (!selectedService) return;

      const paidAmount =
        selectedService.paymentType === PaymentType.Partial
          ? selectedPaymentMode === 'partial'
            ? Number(selectedService.firstPartAmount)
            : Number(selectedService.price)
          : Number(selectedService.price);

      const payload = {
        ecomdukeserviceId: values.ecomdukeserviceId,
        notes: values.notes,
        paidAmount,
        status: ServiceRequestStatus.PENDING
      };

      const request = await createRequest(payload).unwrap();
      if (!request?.id) return;

      const paymentResponse = await createPaymentLink({
        linkAmount: paidAmount,
        linkNotes: {
          ecomdukeserviceRequestId: request.id
        }
      }).unwrap();

      if (paymentResponse?.link_url) {
        window.open(paymentResponse.link_url, '_blank');
      }
      resetForm();
      refetch();
      setSelectedPaymentMode('full');
    }
  });

  useEffect(() => {
    if (defaultServiceId && !formik.values.ecomdukeserviceId) {
      formik.setFieldValue('ecomdukeserviceId', defaultServiceId);
    }
  }, [defaultServiceId]);

  const selectedService = services?.find((s) => s.id === formik.values.ecomdukeserviceId);

  return (
    <Card sx={{ mt: 4, p: 2, mb: 4 }}>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          Request a Service
        </Typography>

        {isLoading ? (
          <Box display="flex" justifyContent="center" my={3}>
            <CircularProgress />
          </Box>
        ) : (
          <form onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                {defaultServiceId ? (
                  <TextField fullWidth label="Service" value={selectedService?.name || 'N/A'} disabled />
                ) : (
                  <TextField
                    select
                    fullWidth
                    name="ecomdukeserviceId"
                    label="Service"
                    value={formik.values.ecomdukeserviceId}
                    onChange={(e) => {
                      formik.handleChange(e);
                      setSelectedPaymentMode('full'); // reset payment mode
                    }}
                  >
                    {services?.map((service) => (
                      <MenuItem key={service.id} value={service.id}>
                        {service.name}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              </Grid>

              {selectedService && (
                <>
                  {selectedService.paymentType === PaymentType.Partial && (
                    <Grid item xs={12} md={6}>
                      <TextField
                        select
                        fullWidth
                        label="Choose Payment Mode"
                        value={selectedPaymentMode}
                        onChange={(e) => setSelectedPaymentMode(e.target.value as 'full' | 'partial')}
                      >
                        <MenuItem value="full">Full Payment</MenuItem>
                        <MenuItem value="partial">Partial Payment</MenuItem>
                      </TextField>
                    </Grid>
                  )}

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Payable Amount"
                      value={
                        selectedService.paymentType === PaymentType.Partial
                          ? selectedPaymentMode === 'partial'
                            ? `₹${selectedService.firstPartAmount}`
                            : `₹${selectedService.price}`
                          : `₹${selectedService.price}`
                      }
                      disabled
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      name="notes"
                      label="Notes"
                      value={formik.values.notes}
                      onChange={formik.handleChange}
                    />
                  </Grid>

                  <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <Button variant="contained" type="submit">
                      Submit Request
                    </Button>
                  </Grid>
                </>
              )}
            </Grid>
          </form>
        )}
      </CardContent>
    </Card>
  );
}
