'use client';

import { useMemo, useState } from 'react';
import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';
import { Button, Typography } from '@mui/material';
import { useAuth } from 'contexts/AuthContext';
import Loader from 'components/Loader';
import withPermission from 'hoc/withPermission';
import { PermissionKeys } from 'enums/permission-key.enum';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from 'utils/table-filter';
import EcomdukeServiceTable from './EcomeDukeServiceTable';
import { EcomdukeService } from 'types/service-request';
import { useGetServicesCountQuery, useGetServicesQuery } from 'redux/app/service-request/requestApislice';
import IndeterminateCheckbox from 'components/third-party/react-table/IndeterminateCheckbox';
import { Add, Eye } from 'iconsax-react';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import IconButton from 'components/@extended/IconButton';
import { useRouter } from 'next/navigation';

const EcomdukeServiceListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();
  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });

  const canCreate = hasPermission(PermissionKeys.CreateServiceRequest);

  const {
    data: services,
    isLoading,
    refetch
  } = useGetServicesQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name', 'description']),
    ...convertPaginationToLoopback(pagination)
  });

  const { data: countData, isLoading: countLoading } = useGetServicesCountQuery({
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name', 'description'])
  });

  const columns = useMemo<ColumnDef<EcomdukeService>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      },
      {
        header: 'Name',
        accessorKey: 'name',
        cell: ({ row }) => <Typography>{row.original.name}</Typography>
      },
      {
        header: 'Price',
        accessorKey: 'price',
        cell: ({ row }) => <Typography>{row.original.price}</Typography>
      },
      {
        header: 'Currency',
        accessorKey: 'currency',
        cell: ({ row }) => <Typography>{row.original.currency}</Typography>
      },
      {
        header: 'Status',
        accessorKey: 'isActive',
        cell: ({ row }) => <Typography>{row.original.isActive ? 'Active' : 'Inactive'}</Typography>
      },
      {
        header: 'Payment Type',
        accessorKey: 'paymentType',
        cell: ({ row }) => <Typography>{row.original.paymentType}</Typography>
      },
      {
        header: 'Service Type',
        accessorKey: 'serviceType',
        cell: ({ row }) => <Typography>{row.original.serviceType}</Typography>
      },
      {
        header: 'Actions',
        enableSorting: false,
        cell: ({ row }) => {
          const expandIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add style={{ color: theme.palette.error.main, transform: 'rotate(45deg)' }} />
            ) : (
              <Eye />
            );
          const handlePayment = () => {
            const id = row.original?.id;
            if (id) {
              router.push(`/ecomduke-service/create?ecomdukeserviceId=${id}`);
            }
          };

          return (
            <Stack direction="row" alignItems="center" spacing={1}>
              <Tooltip title="View">
                <IconButton onClick={row.getToggleExpandedHandler()}>{expandIcon}</IconButton>
              </Tooltip>
              <Button
                size="small"
                variant="outlined"
                onClick={handlePayment}
                sx={{
                  borderColor: 'primary.main',
                  color: 'primary.main',
                  '&:hover': {
                    borderColor: 'primary.main',
                    backgroundColor: 'primary.main',
                    color: 'secondary.lighter'
                  }
                }}
              >
                Choose & Pay
              </Button>
            </Stack>
          );
        }
      }
    ],
    [theme]
  );

  return isLoading || countLoading ? (
    <Loader />
  ) : (
    <EcomdukeServiceTable
      data={services || []}
      columns={columns}
      sorting={sorting}
      setSorting={setSorting}
      columnFilters={columnFilters}
      setColumnFilters={setColumnFilters}
      globalFilter={globalFilter}
      setGlobalFilter={setGlobalFilter}
      pagination={pagination}
      setPagination={setPagination}
      totalRows={countData?.count ?? 0}
      canCreate={canCreate}
      loading={isLoading}
      refetch={refetch}
    />
  );
};

export default withPermission(PermissionKeys.ViewServiceRequest)(EcomdukeServiceListPage);
