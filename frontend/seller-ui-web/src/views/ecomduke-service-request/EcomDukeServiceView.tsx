'use client';

import React, { useEffect } from 'react';
import { Grid, Card, CardContent, Typography, Box } from '@mui/material';
import Loader from 'components/Loader';
import { useGetEcomdukeServiceByIdQuery } from 'redux/app/service-request/requestApislice';

type Props = {
  serviceId: string;
};

const ViewEcomdukeService = ({ serviceId }: Props) => {
  const { data, isLoading, error, refetch } = useGetEcomdukeServiceByIdQuery({ id: serviceId });

  useEffect(() => {
    refetch();
  }, [refetch]);

  if (isLoading) return <Loader />;
  if (error || !data) return <Typography color="error">Error fetching service details.</Typography>;

  return (
    <Grid container spacing={2} sx={{ mt: 1 }}>
      <Grid item xs={12}>
        <Card sx={{ p: 2, boxShadow: 3 }}>
          <CardContent>
            <Typography variant="h4" gutterBottom>
              Service Details
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <DisplayItem label="Name" value={data.name} />
              </Grid>
              <Grid item xs={12} md={6}>
                <DisplayItem label="Price" value={data.price} />
              </Grid>
              <Grid item xs={12} md={6}>
                <DisplayItem label="Currency" value={data.currency} />
              </Grid>
              <Grid item xs={12} md={6}>
                <DisplayItem label="Is Active" value={data.isActive ? 'Yes' : 'No'} />
              </Grid>
              {data.fileUploadRequired !== undefined && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="File Upload Required" value={data.fileUploadRequired ? 'Yes' : 'No'} />
                </Grid>
              )}
              {data.serviceType && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Service Type" value={data.serviceType} />
                </Grid>
              )}
              {data.paymentType && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Payment Type" value={data.paymentType} />
                </Grid>
              )}
              {data.recurringInterval && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Recurring Interval" value={data.recurringInterval} />
                </Grid>
              )}
              {data.firstPartAmount && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="First Part Amount" value={data.firstPartAmount} />
                </Grid>
              )}
              {data.secondPartAmount && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Second Part Amount" value={data.secondPartAmount} />
                </Grid>
              )}
              {data.lastDteToPay && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Last Payment Date" value={new Date(data.lastDteToPay).toLocaleDateString()} />
                </Grid>
              )}
              {data.description && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Description" value={data.description} isMultiline />
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

const DisplayItem = ({ label, value, isMultiline = false }: { label: string; value: string | number; isMultiline?: boolean }) => (
  <Box sx={{ mb: 1.5 }}>
    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
      {label}:
    </Typography>
    <Box
      sx={{
        backgroundColor: '#f5f5f5',
        px: 2,
        py: 1.2,
        borderRadius: 2,
        fontSize: '0.95rem',
        color: 'text.primary',
        whiteSpace: isMultiline ? 'pre-line' : 'nowrap'
      }}
    >
      {value}
    </Box>
  </Box>
);

export default ViewEcomdukeService;
