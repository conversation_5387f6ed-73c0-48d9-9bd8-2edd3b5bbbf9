'use client';

import React, { useState } from 'react';
import { Box, Grid, Table, TableBody, TableCell, TableContainer, TableRow, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import MainCard from 'components/MainCard';
import SmallCardContent from 'components/forms/SmallCardContent';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useGetOrderQuery } from 'redux/app/order/orderApiSlice';
const orders = [
  {
    orderId: '2323',
    productId: 'P12345',
    price: '₹1,500',
    payment: 'Online',
    details: 'Paid via UPI',
    status: 'Pending'
  },
  {
    orderId: '2324',
    productId: 'P67890',
    price: '₹3,000',
    payment: 'COD',
    details: 'Cash on Delivery',
    status: 'Successful'
  },
  {
    orderId: '2325',
    productId: 'P11122',
    price: '₹750',
    payment: 'Card',
    details: 'Paid via Credit Card',
    status: 'Failed'
  }
];

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'pending':
      return 'orange';
    case 'successful':
      return 'green';
    case 'failed':
      return 'red';
    case 'not done':
      return 'gray';
    default:
      return 'black';
  }
};

const AnalyticsPage = () => {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const { data: orderLineItems = [], isLoading: isOrderPriceLoading } = useGetOrderQuery({});
  const totalSalesAmount = orderLineItems.reduce((sum, item) => sum + Number(item.totalPrice ?? 0), 0);

  return (
    <Box sx={{ mt: 4, mb: 4, px: { xs: 2, md: 8 } }}>
      <Typography variant="h4" sx={{ textAlign: 'left', mb: 3 }}>
        Dashboard
      </Typography>
      <Grid container spacing={3} justifyContent="center">
        <Grid container item xs={12} spacing={3} justifyContent="center">
          <Grid item xs={12} sm={6} md={4}>
            <SmallCardContent
              title="Total Sales"
              image="/assets/images/icons/total-sales.svg"
              subtitle="Total Sales"
              amount="₹ 1,975,512"
              color={'#9E3393'}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <SmallCardContent
              title="Total Order"
              image="/assets/images/icons/total-order.svg"
              subtitle="Total Order"
              amount={isOrderPriceLoading ? 'Loading...' : `₹ ${totalSalesAmount.toLocaleString(undefined, { maximumFractionDigits: 2 })}`}
              color={'#A4A4A4'}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <SmallCardContent
              title="Total Products"
              image="/assets/images/icons/total-product.svg"
              subtitle="Total Products"
              amount="₹ 987,654"
              color={'#00004F'}
            />
          </Grid>
        </Grid>

        <Grid container item xs={12} spacing={3} justifyContent="center">
          <Grid item xs={12} md={8}>
            <MainCard>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Stack direction={{ xs: 'column', sm: 'row' }} alignItems="center" justifyContent="space-between" gap={2}>
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DatePicker label="Start Date" value={startDate} onChange={(newValue) => setStartDate(newValue)} />
                    </LocalizationProvider>

                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DatePicker label="End Date" value={endDate} onChange={(newValue) => setEndDate(newValue)} />
                    </LocalizationProvider>
                  </Stack>
                </Grid>

                <Grid item xs={12}>
                  <Grid container direction="row" spacing={3} justifyContent="space-between">
                    <Grid item xs={12} sm={6}>
                      <SmallCardContent
                        title="New Orders 2"
                        image="/assets/images/icons/total-sales.svg"
                        subtitle="Total Sales"
                        amount="₹ 1,234,567"
                        color={'#9E3393'}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <SmallCardContent
                        title="New Orders 2"
                        image="/assets/images/icons/total-order.svg"
                        subtitle="Total Order"
                        amount="₹ 1,234,567"
                        color={'#A4A4A4'}
                      />
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </MainCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <MainCard
              sx={{
                height: { xs: 'auto', md: '100%' },
                p: 2
              }}
            >
              <Grid item xs={12}>
                <Stack direction="row" alignItems="center" justifyContent="space-between">
                  <Typography variant="h5">Payout</Typography>
                </Stack>
                <Stack spacing={1} mt={1}>
                  <Typography>Total Payment Due :</Typography>
                  <Typography>Total Payment Received :</Typography>
                  <Typography>24/03/23 - Rs 5000/-</Typography>
                  <Typography>12/04/23 - Rs 2000/-</Typography>
                </Stack>
              </Grid>
            </MainCard>
          </Grid>
        </Grid>

        <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center' }}>
          <MainCard
            sx={{
              width: { xs: '100%' },
              minHeight: '100%',
              overflow: 'hidden',
              boxShadow: 'none'
            }}
          >
            <TableContainer
              component={Box}
              sx={{
                maxHeight: '400px',
                overflowY: 'auto'
              }}
            >
              <Table>
                <TableBody>
                  {orders.map((order, index) => (
                    <TableRow key={index}>
                      <TableCell align="center">ID {order.orderId}</TableCell>
                      <TableCell align="center">Product ID : {order.productId}</TableCell>
                      <TableCell align="left"> Product Price : {order.price}</TableCell>
                      <TableCell align="left">Payment : {order.payment}</TableCell>
                      <TableCell align="left">Payment Details : {order.details}</TableCell>
                      <TableCell align="center">
                        <span
                          style={{
                            color: 'white',
                            backgroundColor: getStatusColor(order.status),
                            padding: '5px 10px',
                            borderRadius: '22px',
                            display: 'inline-block',
                            minWidth: '120px'
                          }}
                        >
                          {order.status}
                        </span>
                      </TableCell>
                      <TableCell align="center">Payment</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </MainCard>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AnalyticsPage;
