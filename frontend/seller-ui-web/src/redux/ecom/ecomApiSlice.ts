import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from '../apiSlice';
import { Feature, Plan, Subscription } from 'types/auth';

interface FileUploadOnboardRequest {
  file: FormData;
}

export const ecomApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    fileUploadOnboard: builder.mutation({
      query: ({ fileUploadOnboard }: { fileUploadOnboard: FileUploadOnboardRequest }) => ({
        url: '/files/upload',
        method: 'POST',
        body: fileUploadOnboard.file,
        formData: true,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE
      })
    }),
    getPlans: builder.query<Plan[], void>({
      query: () => ({
        url: `/plans`,
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: { status: 'ACTIVE' },
            include: [
              {
                relation: 'planFeatureValues',
                scope: {
                  include: [
                    {
                      relation: 'featureValue',
                      scope: {
                        include: ['feature']
                      }
                    }
                  ]
                }
              },
              {
                relation: 'planPricings',
                scope: {
                  order: ['minSalesThreshold ASC']
                }
              }
            ],
            skip: 0,
            order: 'name ASC'
          })
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    getFeatures: builder.query<Feature[], void>({
      query: () => ({
        url: `/features`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    getSubscription: builder.query<Subscription[], string>({
      query: (sellerId) => ({
        url: `/subscriptions`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
        params: {
          filter: JSON.stringify({
            where: { status: 'ACTIVE', subscriberId: sellerId },
            limit: 1
          })
        }
      })
    }),
    createSubscription: builder.mutation<Subscription, { subscriberId: string; planId: string }>({
      query: ({ subscriberId, planId }) => ({
        url: `/subscriptions`,
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
        body: {
          subscriberId,
          planId,
          startDate: new Date().toISOString()
        }
      })
    })
  })
});

export const {
  useFileUploadOnboardMutation,
  useGetPlansQuery,
  useGetFeaturesQuery,
  useGetSubscriptionQuery,
  useLazyGetPlansQuery,
  useLazyGetSubscriptionQuery,
  useCreateSubscriptionMutation
} = ecomApiSlice;
