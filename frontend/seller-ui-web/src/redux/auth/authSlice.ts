/* eslint-disable camelcase */
import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { RootState } from 'redux/store';
export interface AuthData {
  guestToken: string | null;
  accessToken: string | null;
  refreshToken: string | null;
  expires: number | null;
  authNotice: string | null;
  plan?: string | null;
  features?: string[];
}

export interface AuthResData {
  accessToken: string;
  expires: number;
  refreshToken: string;
  token_type?: string;
  'not-before-policy'?: number;
  session_state?: string;
  scope?: string;
}

export interface AuthState extends AuthData {
  isLoggedIn: boolean;
  authNotice: string | null;
  featuresLoaded: boolean;
}

const initialState: AuthState = {
  accessToken: typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null,
  refreshToken: typeof window !== 'undefined' ? localStorage.getItem('refreshToken') : null,
  guestToken: typeof window !== 'undefined' ? localStorage.getItem('guestToken') : null,

  expires: null,
  isLoggedIn: typeof window !== 'undefined' ? !!localStorage.getItem('accessToken') : false,
  authNotice: null,
  plan: typeof window !== 'undefined' ? localStorage.getItem('plan') : null,
  features: typeof window !== 'undefined' && localStorage.getItem('features') ? JSON.parse(localStorage.getItem('features') as string) : [],
  featuresLoaded: typeof window !== 'undefined' && localStorage.getItem('features') ? true : false
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setCredentials: (state, action: PayloadAction<AuthResData & { plan?: string; features?: string[] }>) => {
      const { accessToken, refreshToken, expires, plan, features } = action.payload;
      state.accessToken = accessToken;
      state.refreshToken = refreshToken;
      state.expires = expires;
      state.isLoggedIn = true;
      state.plan = plan ?? null;
      state.features = features ?? [];
      state.featuresLoaded = true;
    },

    unsetCredentials: (state) => {
      state.accessToken = null;
      state.refreshToken = null;
      state.expires = null;
      state.isLoggedIn = false;
      state.plan = null;
      state.features = [];
      state.featuresLoaded = false;
    },
    setGuestToken: (state, action: PayloadAction<AuthResData>) => {
      const { accessToken, expires } = action.payload;
      state.guestToken = accessToken;
      state.expires = expires;
      state.isLoggedIn = false;
    },
    setAuthNotice: (state, action: PayloadAction<string>) => {
      state.authNotice = action.payload;
    },
    clearAuthNotice: (state) => {
      state.authNotice = null;
    },
    setPlan: (state, action: PayloadAction<string>) => {
      state.plan = action.payload;
    },
    // setFeatures: (state, action: PayloadAction<string[]>) => {
    //   state.features = action.payload ?? [];
    // }
    setFeatures: (state, action: PayloadAction<string[]>) => {
      state.features = action.payload ?? [];
      state.featuresLoaded = true;
    }
  }
});

export const { setCredentials, unsetCredentials, setGuestToken, setAuthNotice, clearAuthNotice, setPlan, setFeatures } = authSlice.actions;

export default authSlice.reducer;

// Selectors
export const selectCurrentLoginStatus = (state: RootState) => state.auth.isLoggedIn;
export const selectCurrentAccessToken = (state: RootState) => state.auth.accessToken;
export const selectCurrentRefreshToken = (state: RootState) => state.auth.refreshToken;
export const selectCurrentAuthState = (state: RootState) => state.auth;
export const selectCurrentAuthNotice = (state: RootState) => state.auth.authNotice;
export const selectCurrentPlan = (state: RootState) => state.auth.plan;
export const selectCurrentFeatures = (state: RootState) => state.auth.features ?? [];
export const selectFeaturesLoaded = (state: RootState) => state.auth.featuresLoaded;
