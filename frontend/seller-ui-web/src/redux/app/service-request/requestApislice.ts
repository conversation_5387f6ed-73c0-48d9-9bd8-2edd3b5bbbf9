import { apiSlice } from '../../apiSlice';
import { ApiSliceIdentifier } from 'enums/api.enum';
import { Count } from 'types/api';
import { IFilter } from '../types/filter';
import { EcomdukeserviceRequest, EcomdukeService, CashfreePaymentLinkResponse } from 'types/service-request';
import { buildFilterParams } from 'utils/buildFilterParams';

export const ecomServiceRequestApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // GET: List of requests
    getServiceRequests: builder.query<EcomdukeserviceRequest[], IFilter | void>({
      query: (filter) => ({
        url: '/ecomdukeservice-requests',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            include: [
              {
                relation: 'seller',
                scope: {
                  include: [
                    {
                      relation: 'userTenant',

                      scope: {
                        include: [{ relation: 'user' }]
                      }
                    }
                  ]
                }
              },
              {
                relation: 'ecomdukeservice' // <-- this matches the `@belongsTo` name in the model
              }
            ]
          })
        }
      })
    }),

    // GET: Single request by ID (with relations)
    getServiceRequestById: builder.query<EcomdukeserviceRequest, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => {
        const defaultInclude = [
          {
            relation: 'seller',
            scope: {
              include: [
                {
                  relation: 'userTenant',
                  scope: {
                    include: [{ relation: 'user' }]
                  }
                }
              ]
            }
          },
          {
            relation: 'ecomdukeservice'
          }
        ];

        return {
          url: `/ecomdukeservice-requests/${id}`,
          method: 'GET',
          apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
          params: {
            filter: JSON.stringify({
              ...filter,
              include: defaultInclude
            })
          }
        };
      }
    }),

    // POST: Create a new request
    createServiceRequest: builder.mutation<EcomdukeserviceRequest, Partial<EcomdukeserviceRequest>>({
      query: (data) => ({
        url: '/ecomdukeservice-requests',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),

    // PATCH: Update request by ID
    updateServiceRequest: builder.mutation<void, { id: string; data: Partial<EcomdukeserviceRequest> }>({
      query: ({ id, data }) => ({
        url: `/ecomdukeservice-requests/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),

    // DELETE: Remove request by ID
    deleteServiceRequest: builder.mutation<void, string>({
      query: (id) => ({
        url: `/ecomdukeservice-requests/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),

    // GET: Count requests
    getServiceRequestCount: builder.query<Count, { where?: Record<string, unknown> }>({
      query: ({ where }) => ({
        url: '/ecomdukeservice-requests/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          where: JSON.stringify(where)
        }
      })
    }),
    getServices: builder.query<EcomdukeService[], IFilter | void>({
      query: (filter) => ({
        url: '/ecomdukeservices',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    getServicesCount: builder.query<Count, { where?: Record<string, unknown> }>({
      query: ({ where }) => ({
        url: '/ecomdukeservices/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          where: JSON.stringify(where)
        }
      })
    }),
    getEcomdukeServiceById: builder.query<EcomdukeService, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => {
        const defaultInclude = [
          {
            relation: 'taxCategory'
          }
        ];

        return {
          url: `/ecomdukeservices/${id}`,
          method: 'GET',
          apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
          params: {
            filter: JSON.stringify({
              ...filter,
              include: defaultInclude
            })
          }
        };
      }
    }),
    downloadServiceRequestInvoicePdf: builder.query<Blob, string>({
      query: (invoiceId) => ({
        url: `/invoices/service-request/${invoiceId}/download`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        responseHandler: (response: Response) => response.blob()
      })
    }),
    createPaymentLink: builder.mutation<CashfreePaymentLinkResponse, { linkAmount: number; linkNotes: Record<string, string> }>({
      query: (body) => ({
        url: '/payments/link',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body
      })
    })
  })
});
export const {
  useGetServiceRequestsQuery,
  useGetServiceRequestByIdQuery,
  useCreateServiceRequestMutation,
  useUpdateServiceRequestMutation,
  useDeleteServiceRequestMutation,
  useGetServiceRequestCountQuery,
  useGetServicesQuery,
  useCreatePaymentLinkMutation,
  useGetServicesCountQuery,
  useGetEcomdukeServiceByIdQuery,
  useDownloadServiceRequestInvoicePdfQuery,
  useLazyDownloadServiceRequestInvoicePdfQuery
} = ecomServiceRequestApiSlice;
