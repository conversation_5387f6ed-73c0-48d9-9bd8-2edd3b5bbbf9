'use client';

import { PermissionKeys } from 'enums/permission-key.enum';
import { createContext, useContext, ReactNode, useEffect } from 'react';
import { useGetUserQuery } from 'redux/auth/authApiSlice';
import { useGetPlansQuery, useGetSubscriptionQuery } from 'redux/ecom/ecomApiSlice';
import { useAppDispatch } from 'redux/hooks';
import { User } from 'types/user-profile';
import { setFeaturestoCache } from 'utils/subscription-cache';
interface AuthContextType {
  user: User;
  hasPermission: (permission: PermissionKeys) => boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const dispatch = useAppDispatch();

  const { data: currentUser } = useGetUserQuery();
  const user = currentUser!;
  const { data: plans = [] } = useGetPlansQuery(undefined, {
    skip: !user?.profileId
  });

  const { data: subscriptions = [] } = useGetSubscriptionQuery(user?.profileId ?? '', {
    skip: !user?.profileId
  });

  useEffect(() => {
    setFeaturestoCache(subscriptions, plans, dispatch);
  }, [subscriptions, plans]);

  const hasPermission = (permission: PermissionKeys) => {
    return user?.role === 'Admin' || user?.permissions.includes(permission);
  };

  return <AuthContext.Provider value={{ user, hasPermission }}>{children}</AuthContext.Provider>;
};
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
