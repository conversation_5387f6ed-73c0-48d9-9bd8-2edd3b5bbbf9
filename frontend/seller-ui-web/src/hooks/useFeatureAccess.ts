'use client';
import { useSelector } from 'react-redux';
import { selectCurrentFeatures, selectFeaturesLoaded } from 'redux/auth/authSlice';

export const useFeatureAccess = () => {
  const features = useSelector(selectCurrentFeatures);
  const featuresLoaded = useSelector(selectFeaturesLoaded);

  const hasFeature = (featureKey: string): boolean => {
    if (!featuresLoaded) return false;
    return features.includes(featureKey);
  };

  return hasFeature;
};
