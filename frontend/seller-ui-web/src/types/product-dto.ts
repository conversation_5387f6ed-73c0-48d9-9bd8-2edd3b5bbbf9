import { AutoCompleteOption } from './common';
import { ProductMoreInfo } from './product';
import { ProductStatus } from 'enums/product-status.enum';

export interface ProductDto {
  name: string;
  description: string;
  enabled: boolean;
  assets: string[];
  options: Option[];
  facets: string[];
  boxContents: BoxContent[];
  details?: Detail;
  specifications: Specification[];
  returnPolicy?: ReturnPolicy;
  disclaimer?: Disclaimer;
  terms?: Terms;
  sellerId: string;
  featuredAssetId: string;
  collectionId: string;
  taxCategoryId?: string;
  variants: ProductVariantDto[];
  customizations: Customization[];
  sellerOption?: AutoCompleteOption;
  collectionOption?: AutoCompleteOption;
  isGiftWrapAvailable?: boolean;
  suitability?: ProductSuitability;
  uniqueness?: ProductUniqueness;
  personalWork?: ProductPersonalWork;
  moreInfo?: ProductMoreInfo;
  isGiftWrapCharge?: number;
  status?: ProductStatus;
  rejectedReason?: string;
  averageWeight?: number;
  turnAroundTime?: number;
}
export interface ProductVariantDto {
  id: number;
  name: string;
  price: number;
  mrp: number;
  featuredAssetId?: string;
  outOfStockThreshold: number;
  optionIds: number[];
  enabled?: boolean;
}

export interface Option {
  id: number;
  name: string;
  unit: string;
  values: OptionValue[];
}

export interface OptionValue {
  id: number;
  name: string;
}

export interface BoxContent {
  itemName: string;
  quantity: number;
}

export interface Detail {
  details: string;
}

export interface Specification {
  name: string;
  value: string;
}

export interface ReturnPolicy {
  returnPolicy: string;
}

export interface ProductSuitability {
  suitableFor: string;
}

export interface ProductUniqueness {
  uniqueness: string;
}
export interface ProductPersonalWork {
  workLevel: string;
}
export interface Disclaimer {
  disclaimer: string;
}

export interface Terms {
  terms: string;
}

export enum FieldType {
  DROPDOWN = 'dropdown',
  TEXT = 'text',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  COLOR = 'color',
  TEXTAREA = 'textarea',
  FILE = 'file'
}

export interface CustomizationOption {
  id: string;
  value: string;
  label: string;
}

export interface Customization {
  name: string;
  label: string;
  placeholder?: string;
  fieldType: FieldType;
  isRequired: boolean;
  options?: CustomizationOption[];
}
