import { setFeatures, setPlan } from 'redux/auth/authSlice';
import { AppDispatch } from 'redux/store';
import { Plan, Subscription } from 'types/auth';

export const setFeaturestoCache = (subscriptions: Subscription[], plans: Plan[], dispatch: AppDispatch) => {
  let currentPlan: Plan | undefined;

  if (subscriptions.length && plans.length) {
    const activeSubscription = subscriptions.find((sub) => sub.status === 'ACTIVE');
    const planId = activeSubscription?.planId;
    if (!planId) return;

    currentPlan = plans.find((plan) => plan.id === planId);
  } else if (plans.length) {
    currentPlan = plans.find((plan) => plan.key === 'advanced-plan');
  }

  if (currentPlan) {
    dispatch(setPlan(currentPlan.name));

    const featureKeys =
      currentPlan.planFeatureValues
        ?.filter((pfv) => pfv.featureValue?.value === 'true')
        .map((pfv) => pfv.featureValue?.feature?.key)
        .filter(Boolean) ?? [];

    localStorage.setItem('features', JSON.stringify(featureKeys));
    dispatch(setFeatures(featureKeys));
  }
};
