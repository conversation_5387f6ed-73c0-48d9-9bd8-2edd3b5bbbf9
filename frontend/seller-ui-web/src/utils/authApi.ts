import axios, { AxiosRequestConfig } from 'axios';
import { ApiError } from 'types/api';

const authApiService = axios.create({
  baseURL: process.env.NEXT_PUBLIC_ECOM_API_FACADE_URL
});

// ==============================|| AXIOS - FOR MOCK SERVICES ||============================== //

/**
 * Request interceptor to add Authorization token to request
 */
authApiService.interceptors.request.use(
  async (config) => {
    config.headers['x-origin'] = 'ecomdukes-seller';
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

authApiService.interceptors.response.use(
  (response) => response,
  (error) => {
    const apiError = error.response?.data as ApiError;
    return Promise.reject(apiError?.error?.message || 'API failed with unknown error');
  }
);

export default authApiService;

export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
  const [url, config] = Array.isArray(args) ? args : [args];

  const res = await authApiService.get(url, { ...config });

  return res.data;
};

export const fetcherPost = async (args: string | [string, AxiosRequestConfig]) => {
  const [url, config] = Array.isArray(args) ? args : [args];

  const res = await authApiService.post(url, { ...config });

  return res.data;
};
