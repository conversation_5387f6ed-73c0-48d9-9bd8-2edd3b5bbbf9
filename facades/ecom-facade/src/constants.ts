import {OrderItemStatus} from '@local/core';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {randomUUID} from 'crypto';

export const systemUser: IAuthUserWithPermissions = {
  firstName: 'System',
  lastName: 'User',
  email: '<EMAIL>',
  username: '<EMAIL>',
  permissions: [],
  authClientId: 0,
  role: 'SYSTEM_USER',
  id: randomUUID(),
  userTenantId: randomUUID(),
};

export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export enum FeatureValueType {
  BOOLEAN = 'BOOLEAN',
  STRING = 'STRING',
  NUMBER = 'NUMBER',
}
export const UPLOAD_FILE_SIZE = 10 * 1024 * 1024;

export const MAX_FILES = 5;

export const FILTER_CACHE_TTL = 300000; //5 minutes

export const metaFields = [
  'deleted',
  'deletedOn',
  'deletedBy',
  'createdBy',
  'createdOn',
  'modifiedOn',
  'modifiedBy',
];

export const MINIMUM_DISCOUNT_THRESHOLD = 10;

export const fieldsExcludeMetaFields = metaFields.reduce(
  (acc, field) => {
    acc[field] = false;
    return acc;
  },
  {} as Record<string, boolean>,
);

export enum ConfigurationLabel {
  AndroidSellerVersion = 'Android Seller Version',
  AndroidSellerForceUpdate = 'Android Seller Force Update',
  IosSellerVersion = 'iOS Seller Version',
  IosSellerForceUpdate = 'iOS Seller Force Update',
  AndroidCustomerVersion = 'Android Customer Version',
  AndroidCustomerForceUpdate = 'Android Customer Force Update',
  IosCustomerVersion = 'iOS Customer Version',
  IosCustomerForceUpdate = 'iOS Customer Force Update',
  DukesCoinsMaxApplicable = 'Max Applicable Dukes Coins per Transaction',
  DukesCoinsProgramEnabled = 'Dukes Coins Program Enabled',
}

export const allowedOrderItemTransitions: Record<
  OrderItemStatus,
  OrderItemStatus[]
> = {
  [OrderItemStatus.New]: [
    OrderItemStatus.Pending,
    OrderItemStatus.Paid,
    OrderItemStatus.PaymentFailed,
  ],
  [OrderItemStatus.Pending]: [
    OrderItemStatus.Accepted,
    OrderItemStatus.Rejected,
    OrderItemStatus.Cancelled,
  ],
  [OrderItemStatus.Paid]: [OrderItemStatus.Accepted],
  [OrderItemStatus.Accepted]: [
    OrderItemStatus.Processing,
    OrderItemStatus.ReadyToDispatch,
  ],
  [OrderItemStatus.Processing]: [OrderItemStatus.Dispatched],
  [OrderItemStatus.Dispatched]: [OrderItemStatus.Delivered],
  [OrderItemStatus.Delivered]: [OrderItemStatus.ReturnRefund],
  [OrderItemStatus.ReturnRefund]: [OrderItemStatus.RefundCompleted],
  [OrderItemStatus.RefundCompleted]: [],
  [OrderItemStatus.Rejected]: [],
  [OrderItemStatus.Cancelled]: [],
  [OrderItemStatus.ReadyToDispatch]: [OrderItemStatus.Dispatched],
  [OrderItemStatus.PaymentFailed]: [],
};

export const excludeAuditFields = {
  deleted: false,
  deletedOn: false,
  modifiedOn: false,
  createdOn: false,
  deletedBy: false,
  createdBy: false,
  modifiedBy: false,
};
