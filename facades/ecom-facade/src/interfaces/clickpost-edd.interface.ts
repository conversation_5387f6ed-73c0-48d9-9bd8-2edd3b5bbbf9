/* eslint-disable @typescript-eslint/naming-convention */
export interface ClickPostPredictedSlaOptional {
  cp_id?: number;
  awb?: string;
  account_code?: string;
}

export interface ClickPostPredictedSlaRequestItem {
  pickup_pincode: string;
  drop_pincode: string;
  optional?: ClickPostPredictedSlaOptional;
}

export type ClickPostPredictedSlaRequest = ClickPostPredictedSlaRequestItem[];

export interface ClickPostPredictedSlaMeta {
  success: boolean;
  message: string;
  status: number;
}

export interface CarrierSlaMap {
  [carrierName: string]: {
    predicted_sla_min: number;
    predicted_sla_max: number;
  };
}

export interface ClickPostPredictedSlaResultItem {
  predicted_sla_min: number;
  predicted_sla_max: number;
  predicted_exact_sla: number;
  all_map: CarrierSlaMap;
  min_sla_cp_id: number;
  pickup_date: string;
}

export interface ClickPostPredictedSlaResponse {
  meta: ClickPostPredictedSlaMeta;
  result: ClickPostPredictedSlaResultItem[];
}
