/* eslint-disable @typescript-eslint/naming-convention */
export interface ClickpostRegisterShipmentRequest {
  waybill: string;
  courier_partner: number;
  account_code: string;
  shipment_info: ShipmentInfo;
  pickup_info: ContactInfo;
  drop_info: ContactInfo;
  additional?: AdditionalInfo;
}

interface ShipmentInfo {
  order_type: 'COD' | 'PREPAID' | string;
  invoice_value: string;
  cod_amount: string;
  currency_code: string;
  reference_number: string;
  order_id: string;
  length: number;
  height: number;
  weight: number;
  breadth: number;
  items: ShipmentItem[];
}

interface ShipmentItem {
  sku: string;
  description: string;
  quantity: number;
  price: number;
  images: string;
  return_days: number;
  length: number;
  height: number;
  breadth: number;
  weight: number;
}

interface ContactInfo {
  name: string;
  email: string;
  phone_code: string;
  phone: string;
  address: string;
  postal_code: string;
  city: string;
  district: string;
  state: string;
  country_code: string;
  lat: number;
  long: number;
}

interface AdditionalInfo {
  enable_whatsapp: boolean;
  order_date: string; // ISO date string e.g. '2017-02-14'
  ship_date: string;
  min_edd: number;
  max_edd: number;
  estimated_delivery_date: string; // ISO date string e.g. '2025-06-14'
  language_code: string;
}

export interface ClickpostRegisterShipmentResponse {
  meta: ResponseMeta;
  result: ShipmentCreationResult;
}

interface ResponseMeta {
  status: number;
  message: string;
  success: boolean;
}

interface ShipmentCreationResult {
  consumer_details: {
    id: number;
  };
  shipment_info: {
    id: number;
  };
  tracking_id: number;
  security_key: string;
}
