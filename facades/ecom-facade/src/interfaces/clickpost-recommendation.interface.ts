/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/naming-convention */
export interface ClickPostRecommendationResponse {
  meta: {
    success: boolean;
    message: string;
    status: number;
  };
  result: RecommendationResult[];
}

export interface RecommendationResult {
  preference_array: CourierPreference[];
  filters_ran: FiltersRan;
  pincode_serviceable: boolean;
}

export interface CourierPreference {
  cp_name: string;
  cp_id: number;
  courier_name: string;
  account_code: string;
  async: boolean;
  account_id: number;
  priority: number;
  scores_computation: Record<string, any>;
  shipping_charge: number | null;
}

export interface FiltersRan {
  '1_active_accounts_filter': string[];
  '2_serviceable_accounts_filter': string[];
  '2_1_serviceable_oda_accounts_filter': string[];
  '3_clickpost_define_and_custom_filters_accounts': string[];
  order_list_matched_rule: string;
  '4_ordered_list_account_names_filters_applied': boolean;
  ordered_list_filter_accounts: string[];
}

export interface ClickPostRecommendationRequest {
  pickup_pincode: string;
  drop_pincode: string;
  order_type: string;
  reference_number: string;
  item: string;
  invoice_value: number;
  delivery_type: string;
  weight: number;
  height: number;
  length: number;
  breadth: number;
  additional?: {
    custom_fields: Array<{
      key: string;
      value: string;
    }>;
  };
}

export type ClickPostRecommendationRequestBatch =
  ClickPostRecommendationRequest[];
