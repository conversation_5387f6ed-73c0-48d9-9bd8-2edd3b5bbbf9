/* eslint-disable @typescript-eslint/naming-convention */
export interface ClickPostOrderRequest {
  pickup_name: string;
  pickup_phone: string;
  pickup_address: string;
  pickup_city: string;
  pickup_state: string;
  pickup_country: string;
  pickup_pincode: string;
  pickup_time: string; // ISO date-time string (e.g., "2017-02-14T18:00:00+05:30")

  drop_name: string;
  drop_phone: string;
  drop_address: string;
  drop_city: string;
  drop_state: string;
  drop_country: string;
  drop_pincode: string;

  return_info?: {
    pincode: string;
    city: string;
    name: string;
    state: string;
    country: string;
    phone: number | string;
    address: string;
  };

  tin?: string;
  invoice_date: string; // e.g., "2016-12-16"
  order_type: 'PREPAID' | 'COD';
  cod_value: number;

  items: Array<{
    product_url: string;
    price: string;
    description: string;
    sku: string;
    quantity: string;
    images: string; // comma-separated URLs
  }>;

  invoice_number: string;
  invoice_value: number;
  reference_number: string;
  email?: string;

  weight: number; // in grams
  length: number;
  height: number;
  breadth: number;

  courier_partner?: number;

  gst_info?: {
    seller_gstin: string;
    taxable_value: number;
    ewaybill_serial_number: string;
    is_seller_registered_under_gst: boolean;
    sgst_tax_rate: number;
    place_of_supply: string;
    gst_discount: number;
    hsn_code: string;
    sgst_amount: number;
    enterprise_gstin: string;
    gst_total_tax: number;
    igst_amount: number;
    cgst_amount: number;
    gst_tax_base: number;
    consignee_gstin: string;
    igst_tax_rate: number;
    invoice_reference: string;
    cgst_tax_rate: number;
  };
}

export interface ClickPostOrderResponse {
  meta: {
    message: string;
    status: number;
    success: boolean;
  };
  result: {
    reference_number: string;
    waybill: string;
    label: string;
    security_key: string;
    sort_code: string;
  };
}
