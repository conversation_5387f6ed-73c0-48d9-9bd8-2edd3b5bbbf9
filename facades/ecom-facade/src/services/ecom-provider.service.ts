import {inject, Provider} from '@loopback/core';
import {getService} from '@loopback/service-proxy';
import {EcomServiceDataSource} from '../datasources';
import {Count, Filter, Where} from '@loopback/repository';
import {Warehouse} from '../models';

export interface EcomProvider {
  getWarehouses(
    token: string,
    filter?: Filter<Warehouse>,
  ): Promise<Warehouse[]>;
  getWarehousesById(id: string, token: string): Promise<Warehouse>;
  createWarehouse(warehouseData: Warehouse, token: string): Promise<Warehouse>;
  updateWarehouseById(
    id: string,
    partialWarehouse: Partial<Warehouse>,
    token: string,
  ): Promise<void>;
  replaceWarehouseById(
    id: string,
    warehouseData: Warehouse,
    token: string,
  ): Promise<void>;
  getCount(token: string, where?: string): Promise<Count>;
  deleteWarehouse(id: string): Promise<void>;
}

export class EcomProviderProvider implements Provider<EcomProvider> {
  constructor(
    // ecomService must match the name in the datasource JSON
    @inject('datasources.ecomService')
    protected dataSource: EcomServiceDataSource = new EcomServiceDataSource(),
  ) {}

  value(): Promise<EcomProvider> {
    return getService(this.dataSource);
  }
}
