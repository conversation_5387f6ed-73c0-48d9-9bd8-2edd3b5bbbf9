import {injectable, BindingScope, service, inject} from '@loopback/core';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {
  Asset,
  AssetResponseDto,
  Collection,
  CollectionWithRelations,
  Facet,
  FacetValue,
  FilterGroup,
  FilterValue,
  Product,
  ProductFacetValue,
  ProductVariant,
  ProductVariantFacetValue,
  ProductVariantWithRelations,
} from '../models';
import {OptionUnit} from '@local/core';
import {AnyObject, Filter, repository, Where} from '@loopback/repository';
import {ProductVariantService} from './product-variant.service';
import {SearchFilterRepository} from '../repositories';
import {FILTER_CACHE_TTL, MINIMUM_DISCOUNT_THRESHOLD} from '../constants';
import {getDiscountLabel, getProductSearchFilter} from '../utils/search.util';

@injectable({scope: BindingScope.TRANSIENT})
export class SearchService {
  constructor(
    @restService(Product)
    private readonly productProxy: ModifiedRestService<Product>,
    @restService(ProductVariant)
    private readonly productVariantProxy: ModifiedRestService<ProductVariant>,
    @restService(ProductVariantFacetValue)
    private readonly productVariantFacetValueProxy: ModifiedRestService<ProductVariantFacetValue>,
    @restService(ProductFacetValue)
    private readonly productFacetValueProxy: ModifiedRestService<ProductFacetValue>,
    @restService(FacetValue)
    private readonly facetValueProxy: ModifiedRestService<FacetValue>,
    @restService(Facet)
    private readonly facetProxy: ModifiedRestService<Facet>,
    @restService(Collection)
    private readonly collectionProxy: ModifiedRestService<Collection>,
    @service(ProductVariantService)
    private readonly productVariantService: ProductVariantService,
    @repository(SearchFilterRepository)
    private readonly searchFilterRepository: SearchFilterRepository,
  ) {}

  private async prepareCondition(
    keyword: string,
  ): Promise<Where<ProductVariant>> {
    const cachedFilter = await this.searchFilterRepository.get(keyword);
    if (cachedFilter?.where) {
      return cachedFilter.where;
    }

    const facetValues = await this.facetValueProxy.find({
      where: {name: {ilike: `%${keyword}%`}},
      fields: {id: true, name: true, facetId: true},
      include: [
        {
          relation: 'facet',
          scope: {
            fields: {id: true, name: true},
          },
        },
      ],
    });

    const collections = await this.collectionProxy.find({
      where: {name: {ilike: `%${keyword}%`}},
      fields: {name: true, id: true},
      include: [
        {relation: 'childrens', scope: {fields: {name: true, id: true}}},
      ],
    });

    const productFacetValues = await this.productFacetValueProxy.find({
      where: {facetValueId: {inq: facetValues.map(item => item.id ?? '')}},
      fields: {id: true, facetValueId: true, productId: true},
    });

    const productVariantFacets = await this.productVariantFacetValueProxy.find({
      where: {facetValueId: {inq: facetValues.map(item => item.id ?? '')}},
      fields: {id: true, facetValueId: true, productVariantId: true},
    });
    const collectionIds = (collections as CollectionWithRelations[]).reduce(
      (acc: string[], collection) => {
        acc.push(collection.id ?? ''); // add parent ID
        if (collection.childrens && Array.isArray(collection.childrens)) {
          acc.push(...collection.childrens.map(child => child.id ?? '')); // add children IDs
        }
        if (collection.parentId) {
          acc.push(collection.parentId);
        }
        return acc;
      },
      [],
    );

    const products = await this.productProxy.find({
      where: {
        or: [
          {collectionId: {inq: collectionIds}},
          {name: {ilike: `%${keyword}%`}},
        ],
      },
    });

    const productIds = [
      ...products.map(p => p.id ?? ''),
      ...productFacetValues.map(pf => pf.productId ?? ''),
    ];
    const where: Where<ProductVariant> = {
      or: [],
    };

    if (keyword) {
      where.or.push({name: {ilike: `%${keyword}%`}});
    }

    if (productIds.length > 0) {
      where.or.push({productId: {inq: productIds}});
    }
    if (productVariantFacets.length) {
      where.or.push({
        id: {inq: productVariantFacets.map(pvf => pvf.productVariantId ?? '')},
      });
    }
    await this.searchFilterRepository.set(
      keyword,
      {keyword, where},
      {ttl: FILTER_CACHE_TTL},
    );
    return where;
  }

  private async prepareConditionFromFacetOrCollection({
    keyword,
    facetValueIds,
    collectionIds,
  }: {
    keyword?: string;
    facetValueIds?: string[];
    collectionIds?: string[];
  }): Promise<Where<ProductVariant>> {
    const where: Where<ProductVariant> = {
      or: [],
    };

    if (keyword) {
      where.or.push({name: {ilike: `%${keyword}%`}});
    }

    if (collectionIds?.length) {
      const products = await this.productProxy.find({
        where: {
          collectionId: {inq: collectionIds},
        },
        fields: {id: true},
      });
      const productIds = products.map(p => p.id ?? '');
      where.or.push({productId: {inq: productIds}});
    }

    if (facetValueIds?.length) {
      const facetVariantMappings =
        await this.productVariantFacetValueProxy.find({
          where: {
            facetValueId: {inq: facetValueIds},
          },
          fields: {productVariantId: true},
        });
      const variantIds = facetVariantMappings.map(
        m => m.productVariantId ?? '',
      );
      if (variantIds.length) {
        where.or.push({id: {inq: variantIds}});
      }
    }

    return where;
  }

  async searchSuggestions(keyword: string): Promise<Partial<ProductVariant[]>> {
    const where = await this.prepareCondition(keyword);
    const products = await this.productVariantProxy.find({
      where,
      fields: {id: true, name: true},
      include: [
        {
          relation: 'product',
          scope: {fields: {sellerId: true, status: true}},
        },
      ],
    });

    const filteredProductsFromInactiveSeller =
      await this.productVariantService.filterOutInactiveSellerProductvariants(
        products as ProductVariantWithRelations[],
      );
    return filteredProductsFromInactiveSeller;
  }

  async search({
    keyword,
    facetValueIds,
    collectionIds,
    priceRange,
    filter,
  }: {
    keyword?: string;
    facetValueIds?: string[];
    collectionIds?: string[];
    priceRange?: number[];
    filter?: Filter<ProductVariant>;
  }): Promise<ProductVariant[]> {
    let where: Where<ProductVariant> = {...(filter?.where ?? {})};
    const andConditions: Where<ProductVariant>[] = [];
    if (priceRange?.length === 2) {
      const priceWhereIds = await this.preparePriceFilter(priceRange);
      andConditions.push({id: {inq: priceWhereIds}});
    }
    if (keyword) {
      const keywordWhere = await this.prepareCondition(keyword);
      andConditions.push(keywordWhere);
    }

    // 🧩 Facet filtering
    if (facetValueIds?.length) {
      const pvFacetMatches = await this.productVariantFacetValueProxy.find({
        where: {facetValueId: {inq: facetValueIds}},
        fields: {productVariantId: true},
      });
      const matchedVariantIds = pvFacetMatches.map(
        v => v.productVariantId ?? '',
      );
      if (matchedVariantIds.length) {
        andConditions.push({id: {inq: matchedVariantIds}});
      }
    }

    // 📚 Collection filtering
    if (collectionIds?.length) {
      const productMatches = await this.productProxy.find({
        where: {collectionId: {inq: collectionIds}},
        fields: {id: true},
      });
      const productIds = productMatches.map(p => p.id ?? '');
      if (productIds.length) {
        andConditions.push({productId: {inq: productIds}});
      }
    }

    // ➕ Add any remaining valid filters
    if (Object.keys(where).length > 0) {
      andConditions.unshift(where);
    }

    if (andConditions.length > 0) {
      where = {and: andConditions};
    }

    const mergedIncludes = [
      ...(filter?.include ?? []),
      {
        relation: 'product',
        scope: {fields: {sellerId: true, status: true}},
      },
      {
        relation: 'productVariantPrice',
        scope: {
          fields: {price: true, mrp: true},
        },
      },
    ];

    const variants = await this.productVariantProxy.find({
      ...filter,
      where,
      include: mergedIncludes,
    });

    const filteredVariants =
      await this.productVariantService.filterOutInactiveSellerProductvariants(
        variants as ProductVariantWithRelations[],
      );

    const finalVariants = await Promise.all(
      filteredVariants.map(item =>
        this.productVariantService.getProductVariantWithPresignedUrl(item),
      ),
    );

    return finalVariants;
  }

  async getFilters(
    keyword?: string,
    facetValueIdsStr?: string,
    collectionIdsStr?: string,
  ): Promise<FilterGroup[]> {
    const facetValueIds = facetValueIdsStr?.split(',').filter(Boolean);
    const collectionIds = collectionIdsStr?.split(',').filter(Boolean);

    let where: Where<ProductVariant>;

    if (facetValueIds?.length ?? collectionIds?.length) {
      // build filter conditions manually
      where = await this.prepareConditionFromFacetOrCollection({
        keyword,
        facetValueIds,
        collectionIds,
      });
    } else {
      // fallback to keyword-only search
      where = await this.prepareCondition(keyword ?? '');
    }
    const variants = await this.productVariantProxy.find({
      ...getProductSearchFilter(),
      where,
    });

    return this.buildFilters(variants as ProductVariantWithRelations[]);
  }

  private async buildFilters(
    variants: ProductVariantWithRelations[],
  ): Promise<FilterGroup[]> {
    let collectionMap: Map<
      string,
      {variantIds: string[]; collection: CollectionWithRelations}
    > = new Map();
    let discountMap: Map<string, {name: string; variantIds: string[]}> =
      new Map();
    let customMap: Map<string, {name: string; variantIds: string[]}> =
      new Map();
    let colorMap: Map<string, {name: string; variantIds: string[]}> = new Map();
    let priceMap: Map<
      string,
      {min: number; max: number; variantIds: string[]}
    > = new Map();
    let facetVariantMappings: Map<string, string[]> = new Map();

    for (const variant of variants) {
      const variantId = variant.id!;

      // Facet filter
      facetVariantMappings = this.prepareFilterForFacet(
        variant,
        facetVariantMappings,
        variantId,
      );

      // Collection filter
      collectionMap = this.prepareFilterForCollection(
        variant,
        collectionMap,
        variantId,
      );

      // Discount filter
      discountMap = this.prepareFilterForDiscount(
        variant,
        discountMap,
        variantId,
      );

      // Price filter
      priceMap = this.prepareFilterForPrice(variant, priceMap, variantId);

      // Custom filter
      customMap = this.prepareCustomFilter(variant, customMap, variantId);

      // Color filter
      colorMap = this.prepareColorFilter(variant, colorMap, variantId);
    }

    // Get all unique facet value IDs from the mappings
    const facetValueIds = Array.from(facetVariantMappings.keys());

    // Fetch facet values
    const facetValues = await this.facetValueProxy.find({
      where: {id: {inq: facetValueIds}},
      fields: {id: true, name: true, facetId: true},
    });

    // Get unique facet IDs and fetch facet data separately
    const facetIds = [
      ...new Set(facetValues.map(fv => fv.facetId).filter(Boolean)),
    ];

    // You'll need to create a facet proxy or use the appropriate service to fetch facets
    // For now, I'll assume you have a facet service/proxy. Replace this with your actual facet service
    const facets = await this.facetValueProxy.find({
      where: {id: {inq: facetIds}},
      fields: {id: true, name: true},
    });

    // Create a map for quick facet lookup
    const facetMap = new Map(facets.map(facet => [facet.id, facet]));

    const filters: FilterGroup[] = [];

    filters.push(
      new FilterGroup({
        label: 'Pick a Category',
        values: Array.from(collectionMap.entries()).map(
          ([colId, {variantIds, collection}]) => {
            const assetWithPreview = collection.featuredAsset
              ? this.getAssetWithPreview(collection.featuredAsset)
              : undefined;

            return new FilterValue({
              label: collection.name,
              value: colId,
              productVariantIds: variantIds,
              metadata: {
                previewUrl: assetWithPreview?.previewUrl ?? '',
                parentId: collection.parentId ?? null,
                position: collection.position ?? 0,
              },
            });
          },
        ),
      }),
    );

    filters.push(
      new FilterGroup({
        label: 'Discounts',
        values: Array.from(discountMap.entries()).map(([label, value]) => {
          return new FilterValue({
            label: value.name,
            value: label,
            productVariantIds: value.variantIds,
          });
        }),
      }),
    );

    // 1. Extract all prices to calculate global min and max
    let globalMin = Infinity;
    let globalMax = 0;

    for (const {min, max} of priceMap.values()) {
      if (min < globalMin) globalMin = min;
      if (max > globalMax) globalMax = max;
    }

    // 2. Push a single slider filter group instead of multiple discrete price ranges
    filters.push(
      new FilterGroup({
        label: 'Price',
        values: [
          new FilterValue({
            label: `${globalMin} - ${globalMax}`,
            value: `${globalMin}-${globalMax}`,
            productVariantIds: Array.from(priceMap.values()).flatMap(
              v => v.variantIds,
            ),
          }),
        ],
        metadata: {
          min: globalMin,
          max: globalMax,
          type: 'slider',
          facetId: 'price', // Add facetId for price filter
        },
      }),
    );

    filters.push(
      new FilterGroup({
        label: 'Colour',
        values: Array.from(colorMap.entries()).map(([label, value]) => {
          return new FilterValue({
            label: value.name,
            value: label,
            productVariantIds: value.variantIds,
          });
        }),
      }),
    );

    filters.push(
      new FilterGroup({
        label: 'Custom',
        values: Array.from(customMap.entries()).map(([label, value]) => {
          return new FilterValue({
            label: value.name,
            value: label,
            productVariantIds: value.variantIds,
          });
        }),
      }),
    );

    // Group facet values by their facet (parent facet)
    const facetGroups = new Map<
      string,
      {facetName: string; values: FilterValue[]}
    >();

    for (const [facetValueId, variantIds] of facetVariantMappings.entries()) {
      const facetValue = facetValues.find(fv => fv.id === facetValueId);
      if (facetValue && facetValue.facetId) {
        const facetId = facetValue.facetId;
        const facet = facetMap.get(facetId);
        const facetName = facet?.name || `Facet ${facetId}`;

        if (!facetGroups.has(facetId)) {
          facetGroups.set(facetId, {
            facetName: facetName,
            values: [],
          });
        }

        facetGroups.get(facetId)!.values.push(
          new FilterValue({
            label: facetValue.name ?? '',
            value: facetValueId,
            productVariantIds: variantIds,
            metadata: {
              facetId: facetId,
              facetName: facetName,
            },
          }),
        );
      }
    }

    // Create a filter group for each facet
    for (const [facetId, {facetName, values}] of facetGroups.entries()) {
      filters.push(
        new FilterGroup({
          label: facetName,
          isFacet: true,
          values: values,
          metadata: {
            facetId: facetId,
          },
        }),
      );
    }

    return filters;
  }

  private prepareFilterForCollection(
    variant: ProductVariantWithRelations,
    collectionMap: Map<
      string,
      {variantIds: string[]; collection: CollectionWithRelations}
    >,
    variantId: string,
  ) {
    const collection = variant.product?.collection as CollectionWithRelations;
    if (collection?.id) {
      const object = collectionMap.get(collection.id) ?? {
        variantIds: [],
        collection,
      };
      object.variantIds.push(variantId);
      collectionMap.set(collection.id, object);
    }
    return collectionMap;
  }

  private prepareFilterForDiscount(
    variant: ProductVariantWithRelations,
    discountMap: Map<string, {name: string; variantIds: string[]}>,
    variantId: string,
  ) {
    if (variant.productVariantPrice) {
      const {mrp, price} = variant.productVariantPrice;
      const discount = ((mrp - price) / mrp) * 100;
      if (discount >= MINIMUM_DISCOUNT_THRESHOLD) {
        const discountLabel = getDiscountLabel(discount);
        if (discountLabel) {
          const object = discountMap.get(discountLabel) ?? {
            variantIds: [],
            name: discountLabel,
          };
          object.variantIds.push(variantId);
          discountMap.set(discountLabel, object);
        }
      }
    }
    return discountMap;
  }

  private prepareFilterForPrice(
    variant: ProductVariantWithRelations,
    priceMap: Map<string, {min: number; max: number; variantIds: string[]}>,
    variantId: string,
  ) {
    if (variant.productVariantPrice) {
      const priceValue = variant.productVariantPrice.price ?? 0;

      const groupKey = '';

      const existing = priceMap.get(groupKey);
      if (existing) {
        existing.min = Math.min(existing.min, priceValue);
        existing.max = Math.max(existing.max, priceValue);
        existing.variantIds.push(variantId);
      } else {
        priceMap.set(groupKey, {
          min: priceValue,
          max: priceValue,
          variantIds: [variantId],
        });
      }
    }
    return priceMap;
  }

  private prepareCustomFilter(
    variant: ProductVariantWithRelations,
    customMap: Map<string, {name: string; variantIds: string[]}>,
    variantId: string,
  ) {
    if (variant.product?.productCustomizationFields) {
      const object = customMap.get('Personalized') ?? {
        variantIds: [],
        name: 'Personalized',
      };
      object.variantIds.push(variantId);
      customMap.set('Personalized', object);
    }
    return customMap;
  }

  private prepareColorFilter(
    variant: ProductVariantWithRelations,
    colorMap: Map<string, {name: string; variantIds: string[]}>,
    variantId: string,
  ) {
    if (variant.productVariantOptions?.length) {
      for (const variantOption of variant.productVariantOptions) {
        // Check if the productOption and productOptionGroup are loaded
        if (variantOption.productOption?.productOptionGroup) {
          const optionGroup = variantOption.productOption.productOptionGroup;

          // Check if this option group is for colors
          if (optionGroup.unit === OptionUnit.COLOR) {
            const colorName = variantOption.productOption.name;
            const colorKey = colorName.toUpperCase();

            const object = colorMap.get(colorKey) ?? {
              name: colorName,
              variantIds: [],
            };

            object.variantIds.push(variantId);
            colorMap.set(colorKey, object);
          }
        }
      }
    }
    return colorMap;
  }
  private prepareFilterForFacet(
    variant: ProductVariantWithRelations,
    facetVariantMappings: Map<string, string[]>,
    variantId: string,
  ) {
    if (variant.productVariantFacetValues?.length) {
      for (const pvFacet of variant.productVariantFacetValues) {
        const facetValueId = pvFacet.facetValueId;
        const variantIds = facetVariantMappings.get(facetValueId) ?? [];
        variantIds.push(variantId);
        facetVariantMappings.set(facetValueId, variantIds);
      }
    }
    return facetVariantMappings;
  }

  private async preparePriceFilter(priceRange: number[]): Promise<string[]> {
    const [min, max] = priceRange;
    const variants = await this.productVariantProxy.find({
      fields: {id: true},
      include: [
        {
          relation: 'productVariantPrice',
          scope: {
            fields: {price: true},
            where: {price: {between: [min, max]}},
          },
          required: true,
        },
      ],
    } as AnyObject);
    return variants.map(v => v.id ?? '');
  }

  getAssetWithPreview(asset: Asset): AssetResponseDto {
    const previewUrl = `${process.env.CDN_ORIGIN}/${asset.preview}`;
    return new AssetResponseDto({
      ...asset,
      previewUrl,
    });
  }

  async quickFilter(productVariantIds: string): Promise<FilterGroup> {
    const ids = productVariantIds.split(',').filter(Boolean);

    const productVariantFacetValues =
      await this.productVariantFacetValueProxy.find({
        where: {productVariantId: {inq: ids}},
        fields: {facetValueId: true},
      });

    const facetValueIds = Array.from(
      new Set(
        productVariantFacetValues
          .map(pvfv => pvfv.facetValueId)
          .filter((id): id is string => !!id),
      ),
    );

    if (!facetValueIds.length) {
      return new FilterGroup({
        label: 'Quick Filter',
        values: [],
        isFacet: true,
      });
    }

    const facetValues = await this.facetValueProxy.find({
      where: {id: {inq: facetValueIds}},
    });

    const facetIds = Array.from(
      new Set(facetValues.map(fv => fv.facetId).filter(Boolean)),
    );

    const facets = await this.facetProxy.find({
      where: {id: {inq: facetIds}},
    });

    const facetMap = new Map<string, string>();
    for (const facet of facets) {
      if (facet.id && facet.name) {
        facetMap.set(facet.id, facet.name);
      }
    }

    const values = facetValues.map(fv => ({
      label: fv.name,
      value: fv.id,
      metadata: {
        facetId: fv.facetId,
        facetName: facetMap.get(fv.facetId ?? '') ?? '',
      },
    }));

    return new FilterGroup({
      label: 'Quick Filter',
      values,
      isFacet: true,
    } as AnyObject);
  }
}
