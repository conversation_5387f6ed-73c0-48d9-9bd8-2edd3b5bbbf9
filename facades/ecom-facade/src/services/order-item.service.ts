/* eslint-disable @typescript-eslint/naming-convention */
import {injectable, BindingScope, inject, Getter} from '@loopback/core';
import {
  IAuthUserWithTenant,
  Order,
  OrderLineItem,
  OrderLineItemWithRelations,
  OrderWithRelations,
  PromoUsage,
  Seller,
  Shipping,
  ShippingAddress,
  ShippingMethod,
} from '../models';
import {Filter, Where} from '@loopback/repository';
import {AuthenticationBindings} from 'loopback4-authentication';
import {OrderItemStatus, SellerStatus, ShippingMethodType} from '@local/core';
import {HttpErrors} from '@loopback/rest';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {OrderProxyType, SellerProxyType} from '../datasources/configs';
import {PromoUsageProxyType} from '../datasources/configs/promo-usage-proxy.config';
import {NotificationHelperService} from './notification-helper.service';
import {allowedOrderItemTransitions} from '../constants';
import {User} from '@sourceloop/authentication-service';
import {ClickPost} from './click-post.service';
import {ClickPostOrderRequest} from '../interfaces';

@injectable({scope: BindingScope.TRANSIENT})
export class OrderItemService {
  constructor(
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @restService(Seller)
    private readonly sellerProxy: SellerProxyType,
    @restService(Order)
    private orderProxy: OrderProxyType,
    @restService(PromoUsage)
    private promoUsageProxy: PromoUsageProxyType,
    @restService(OrderLineItem)
    private orderItemProxy: ModifiedRestService<OrderLineItem>,
    @inject('services.NotificationHelperService')
    private readonly notificationHelperService: NotificationHelperService,
    @restService(ShippingMethod)
    private readonly shippingProxyService: ModifiedRestService<ShippingMethod>,
    @inject('services.ClickPost')
    private readonly clickpostService: ClickPost,
    @restService(ShippingAddress)
    private readonly shippingAddressProxy: ModifiedRestService<ShippingAddress>,
    @restService(Shipping)
    private shippingProxy: ModifiedRestService<Shipping>,
  ) {}

  async getOrderItemsWithPreviewUrl(
    orderItems: OrderLineItemWithRelations[],
  ): Promise<OrderLineItemWithRelations[]> {
    if (orderItems.length) {
      for (const item of orderItems) {
        if (item.productVariant) {
          const asset = item.productVariant?.featuredAsset;
          if (asset?.preview && process.env.CDN_ORIGIN) {
            asset.previewUrl = `${process.env.CDN_ORIGIN}/${asset.preview}`;
          }
        }
      }
    }
    return orderItems;
  }

  async getOrderItemWithPreviewUrl(
    orderItem: OrderLineItemWithRelations,
  ): Promise<OrderLineItemWithRelations> {
    if (orderItem) {
      if (orderItem.productVariant) {
        const asset = orderItem.productVariant?.featuredAsset;
        if (asset?.preview && process.env.CDN_ORIGIN) {
          asset.previewUrl = `${process.env.CDN_ORIGIN}/${asset.preview}`;
        }
      }
    }
    return orderItem;
  }

  async applyOrderItemScopeFilter(
    baseFilter: Filter<OrderLineItem> = {},
    origin: string,
  ): Promise<Filter<OrderLineItem>> {
    const where = baseFilter.where ?? {};
    const currentUser = await this.getCurrentUser();
    if (origin === 'ecomdukes-seller') {
      const seller = await this.getSeller();
      Object.assign(where, {sellerId: seller.id});
    } else if (origin === 'ecomdukes-customer') {
      Object.assign(where, {createdBy: currentUser.userTenantId});
    }

    return {
      ...baseFilter,
      where,
    };
  }

  async applyOrderItemScopeWhere(
    baseWhere: Where<OrderLineItem> = {},
    origin: string,
  ): Promise<Where<OrderLineItem>> {
    const where = {...baseWhere};
    const currentUser = await this.getCurrentUser();

    if (origin === 'ecomdukes-seller') {
      const seller = await this.getSeller();
      Object.assign(where, {sellerId: seller.id});
    } else if (origin === 'ecomdukes-customer') {
      Object.assign(where, {createdBy: currentUser.userTenantId});
    }

    return where;
  }

  private async getSeller(): Promise<Seller> {
    const user = await this.getCurrentUser();
    const sellers = await this.sellerProxy.find({
      where: {
        userTenantId: user.userTenantId ?? '',
        status: SellerStatus.APPROVED,
      },
      limit: 1,
    });

    if (!sellers?.length) {
      throw new HttpErrors.BadRequest(
        `Your account is either suspended or cannot be found in our system. Please contact support.`,
      );
    }
    return sellers[0];
  }

  async createPromoUsageIfApplicable(
    orderId: string,
    token?: string,
  ): Promise<void> {
    const user = await this.getCurrentUser();
    const sellers = await this.sellerProxy.find({
      where: {
        userTenantId: user.userTenantId ?? '',
      },
      limit: 1,
    });

    if (!sellers.length || sellers[0].status !== SellerStatus.APPROVED) {
      return;
    }

    const order = await this.orderProxy.findById(orderId);
    if (!order) {
      throw new HttpErrors.NotFound('Order not found');
    }

    if (!order.promoCodeId || !order.customerId) {
      return;
    }

    const usage = new PromoUsage({
      promoCodeId: order.promoCodeId,
      customerId: order.customerId,
      orderId: order.id,
    });

    await this.promoUsageProxy.createPromoUsage(usage, token);
  }

  async updateOrderLineItemStatusByOrderId(
    orderId: string,
    newStatus: OrderItemStatus,
    ignoreValidation?: boolean,
    token?: string,
  ): Promise<void> {
    const orderItems = await this.orderItemProxy.find(
      {
        where: {orderId},
        fields: {id: true},
      },
      token,
    );
    await Promise.all(
      orderItems.map(item =>
        this.updateOrderItemStatus(item.id, newStatus, ignoreValidation, token),
      ),
    );
  }

  async updateOrderItemStatus(
    orderItemId: string,
    newStatus: OrderItemStatus,
    ignoreValidation?: boolean,
    token?: string,
  ): Promise<void> {
    const orderItem = (await this.orderItemProxy.findById(
      orderItemId,
      {
        include: [
          {
            relation: 'seller',
            scope: {
              include: [
                {
                  relation: 'userTenant',
                  scope: {
                    include: [
                      {
                        relation: 'user',
                        scope: {
                          fields: {
                            firstName: true,
                            lastName: true,
                            email: true,
                            phone: true,
                            photoUrl: true,
                          },
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {relation: 'order'},
          {
            relation: 'productVariant',
            scope: {
              include: [{relation: 'product'}],
            },
          },
        ],
      },
      token,
    )) as OrderLineItemWithRelations;

    const currentStatus = this.getValidatedOrderItemStatus(orderItem.status);

    if (currentStatus === newStatus && !ignoreValidation) return;

    await this.validateAndUpdateItemStatus(
      currentStatus,
      newStatus,
      async validatedStatus => {
        await this.orderItemProxy.updateById(
          orderItemId,
          {
            status: validatedStatus,
            cancelledByAdmin:
              validatedStatus === OrderItemStatus.Cancelled &&
              orderItem.cancelledByAdmin
                ? true
                : undefined,
          },
          token,
        );

        const {productVariant, seller} = orderItem;
        const productName = productVariant?.product?.name ?? 'N/A';
        const orderId = orderItem.orderId;

        const sellerUser = seller?.userTenant?.user;

        const fullOrder = (await this.orderProxy.findById(
          orderId,
          {
            include: [
              {
                relation: 'customer',
                scope: {
                  include: [
                    {
                      relation: 'userTenant',
                      scope: {include: [{relation: 'user'}]},
                    },
                  ],
                },
              },
            ],
          },
          token,
        )) as OrderWithRelations;

        const customerUser = fullOrder?.customer?.userTenant?.user;

        const sellerName = this.getUserDisplayName(sellerUser, 'Seller');
        const customerName = this.getUserDisplayName(customerUser, 'Customer');
        if (newStatus === OrderItemStatus.ReadyToDispatch) {
          const [shipping] = await this.shippingProxy.find({
            where: {
              orderLineItemId: orderItem.id,
            },
            include: [
              {
                relation: 'shippingAddresses',
              },
            ],
            limit: 1,
          });

          const warehouse = shipping.shippingAddresses.find(
            address => address.addressType === 'PICKUP',
          );

          const dropAddress = shipping.shippingAddresses.find(
            address => address.addressType === 'DROP',
          );

          try {
            await this.clickpostService.createOrder(
              {
                pickup_name: warehouse?.name ?? '',
                pickup_phone: warehouse?.phoneNumber ?? '',
                pickup_address: warehouse?.addressLine1 ?? '',
                pickup_city: warehouse?.city ?? '',
                pickup_state: warehouse?.state ?? '',
                pickup_country: warehouse?.country ?? '',
                pickup_pincode: warehouse?.zipCode ?? '',
                pickup_time: new Date().toISOString(),
                drop_name: customerName,
                drop_phone: customerUser?.phone ?? '',
                drop_address: dropAddress?.addressLine1
                  ? ', ' + dropAddress?.addressLine2
                  : '',
                drop_city: dropAddress?.city ?? '',
                drop_state: dropAddress?.state ?? '',
                drop_country: dropAddress?.country ?? '',
                drop_pincode: dropAddress?.zipCode ?? '',
                items: [
                  {
                    price: orderItem.unitPrice.toString(),
                    description: productName,
                    sku: orderItem.productVariantId,
                    quantity: orderItem.quantity.toString(),
                  },
                ],
                invoice_number: fullOrder?.invoiceId ?? '',
                invoice_value: orderItem.totalPrice,
                reference_number: orderId,
                email: customerUser?.email ?? '',
              } as ClickPostOrderRequest,
              process.env.CLICKPOST_USERNAME ?? '',
              process.env.CLICKPOST_API_KEY ?? '',
            );
          } catch (error) {
            console.error(
              'Failed to create order in ClickPost for shipment:',
              error,
            );
          }
        }
        await this.notifyParties({
          validatedStatus,
          currentStatus,
          orderId,
          productName,
          seller: {
            name: sellerName,
            email: sellerUser?.email,
            phone: sellerUser?.phone,
          },
          customer: {
            name: customerName,
            email: customerUser?.email,
            phone: customerUser?.phone,
          },
        });
      },
    );
  }

  private getValidatedOrderItemStatus(value: string): OrderItemStatus {
    if (Object.values(OrderItemStatus).includes(value as OrderItemStatus)) {
      return value as OrderItemStatus;
    }
    throw new HttpErrors.BadRequest('Invalid status');
  }

  private getUserDisplayName(user?: Partial<User>, fallback = 'User'): string {
    return (
      (user?.firstName &&
        user?.lastName &&
        `${user.firstName} ${user.lastName}`) ??
      user?.firstName ??
      user?.email ??
      fallback
    );
  }

  private async notifyParties({
    validatedStatus,
    currentStatus,
    orderId,
    productName,
    seller,
    customer,
  }: {
    validatedStatus: OrderItemStatus;
    currentStatus: OrderItemStatus;
    orderId: string;
    productName: string;
    seller: {name: string; email?: string; phone?: string};
    customer: {name: string; email?: string; phone?: string};
  }) {
    const sendToCustomer = async () => {
      if (customer.email) {
        await this.notificationHelperService.sendEmail(
          'order-status-update-customer.hbs',
          `Your Order Status Updated to ${validatedStatus}`,
          {
            customerName: customer.name,
            orderId,
            productName,
            oldStatus: currentStatus,
            newStatus: validatedStatus,
            supportId: '<EMAIL>',
            brand: 'Ecomdukes',
          },
          customer.email,
          customer.name,
        );
      }

      if (customer.phone) {
        await this.notificationHelperService.sendSMS(
          `Hi ${customer.name}, your order for "${productName}" is now ${validatedStatus}.`,
          customer.phone,
          customer.name,
        );
      }
    };

    const sendToSeller = async () => {
      if (seller.email) {
        await this.notificationHelperService.sendEmail(
          'order-status-update-seller.hbs',
          `Order Status Changed to ${validatedStatus}`,
          {
            sellerName: seller.name,
            orderId,
            productName,
            newStatus: validatedStatus,
            supportId: '<EMAIL>',
            brand: 'Ecomdukes',
          },
          seller.email,
          seller.name,
        );
      }

      if (seller.phone) {
        await this.notificationHelperService.sendSMS(
          `Hi ${seller.name}, the status of order "${productName}" is now ${validatedStatus}.`,
          seller.phone,
          seller.name,
        );
      }
    };

    switch (validatedStatus) {
      case OrderItemStatus.Pending:
      case OrderItemStatus.Paid:
        await Promise.all([sendToCustomer(), sendToSeller()]);
        break;

      case OrderItemStatus.Accepted:
      case OrderItemStatus.Rejected:
      case OrderItemStatus.Dispatched:
      case OrderItemStatus.Delivered:
      case OrderItemStatus.RefundCompleted:
        await sendToCustomer();
        break;

      case OrderItemStatus.Cancelled:
        await sendToSeller();
        break;

      default:
        // no-op
        break;
    }
  }

  async validateAndUpdateItemStatus(
    currentStatus: OrderItemStatus,
    newStatus: OrderItemStatus,
    updateFn: (newStatus: OrderItemStatus) => Promise<void>,
  ): Promise<void> {
    const allowed = allowedOrderItemTransitions[currentStatus] || [];

    if (
      currentStatus === OrderItemStatus.Processing &&
      newStatus === OrderItemStatus.Dispatched
    ) {
      const [shippingMethod] = await this.shippingProxyService.find({
        where: {type: ShippingMethodType.SELF_SHIPPING},
      });

      if (!shippingMethod) {
        throw new HttpErrors.BadRequest(
          `Order can only be marked as "${OrderItemStatus.Dispatched}" from "${OrderItemStatus.Processing}" if using self-shipping.`,
        );
      }
    }

    if (!allowed.includes(newStatus)) {
      throw new HttpErrors.BadRequest(
        `Invalid status transition from "${currentStatus}" to "${newStatus}".`,
      );
    }

    await updateFn(newStatus);
  }
}
