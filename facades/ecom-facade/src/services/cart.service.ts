import {
  injectable,
  BindingScope,
  inject,
  Getter,
  service,
} from '@loopback/core';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {
  Cart,
  CartItem,
  CartWithRelations,
  CashfreeCreatePaymentLinkRequestDto,
  Customer,
  CustomerDetails,
  IAuthUserWithTenant,
  LinkNotify,
  Order,
  OrderDto,
  OrderItemDto,
  Payment,
  CartItemWithRelations,
  ProductVariantWithRelations,
  CustomizationValue,
} from '../models';
import {AuthenticationBindings} from 'loopback4-authentication';
import {
  CartStatus,
  CashfreePaymentLinkResponse,
  CustomerStatus,
  DEFAULT_CURRENCY,
  OrderItemStatus,
  OrderStatus,
  PaymentMethod,
} from '@local/core';
import {HttpErrors, RestBindings, Request} from '@loopback/rest';
import {Filter, repository} from '@loopback/repository';
import {CustomerRepository} from '../repositories';
import {OrderProxyType, PaymentProxyType} from '../datasources/configs';
import {OrderService} from './order.service';
import {OrderEntity} from 'cashfree-pg';
import {AssetService} from './asset.service';
import {CartItemDto} from '../models/ecom-service/dto/cart-item-dto.model';
import {OrderItemService} from './order-item.service';
import {ShippingCalculationHelperService} from './shipping-calculation-helper.service';

@injectable({scope: BindingScope.TRANSIENT})
export class CartService {
  private token: string;
  private customer: Customer;
  constructor(
    @restService(Cart)
    private cartProxy: ModifiedRestService<Cart>,
    @restService(Order)
    private orderProxy: OrderProxyType,
    @restService(Customer)
    public customerProxy: ModifiedRestService<Customer>,
    @restService(CartItem)
    private cartItemProxy: ModifiedRestService<CartItem>,
    @restService(Payment)
    public paymentProxy: PaymentProxyType,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @repository(CustomerRepository)
    private readonly customerRepository: CustomerRepository,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @service(OrderService)
    private readonly orderService: OrderService,
    @service(AssetService)
    private readonly assetService: AssetService,
    @restService(CustomizationValue)
    private customizationValueProxy: ModifiedRestService<CustomizationValue>,
    @service(OrderItemService)
    private readonly orderItemService: OrderItemService,
    @service(ShippingCalculationHelperService)
    private shippingCalculationHelper: ShippingCalculationHelperService,
  ) {
    if (request.headers.authorization) {
      this.token = request.headers.authorization;
    }
  }

  async createCart(cart: Partial<Cart>): Promise<Cart> {
    const customer = await this.getCustomer();

    const existingCart = await this.cartProxy.find({
      where: {customerId: customer.id, status: CartStatus.Active},
      limit: 1,
    });
    if (existingCart?.length) {
      return existingCart[0];
    }
    return this.cartProxy.create({
      customerId: customer.id,
      status: CartStatus.Active,
      ...cart,
    });
  }

  async initCart() {
    let cart = await this.getActiveCart();

    // Create new cart if none exists
    if (!cart) {
      cart = await this.cartProxy.create({
        customerId: this.customer.id,
        status: CartStatus.Active,
      });
    }
    return cart;
  }

  async addItemToCart(cartItem: Partial<CartItemDto>): Promise<Cart> {
    const cart = await this.initCart();
    // Check if the item already exists in the cart
    const [existingCartItem] = await this.cartItemProxy.find({
      where: {
        cartId: cart.id,
        productVariantId: cartItem.productVariantId,
      },
      limit: 1,
    });
    if (existingCartItem) {
      // Increment quantity if item exists
      await this.cartItemProxy.updateById(existingCartItem.id, {
        quantity: (existingCartItem.quantity || 1) + 1,
      });
    } else {
      // Create new cart item
      await this.cartItemProxy.create({
        cartId: cart.id,
        productVariantId: cartItem.productVariantId,
        quantity: 1,
      });
    }
    // Add customization values, if any
    if (cartItem.customizationValues?.length) {
      await Promise.all(
        cartItem.customizationValues.map(async value => {
          const [existingCustomization] =
            await this.customizationValueProxy.find({
              where: {
                cartId: cart.id,
                customizationFieldId: value.customizationFieldId,
              },
              limit: 1,
            });

          if (existingCustomization) {
            // Update the existing customization value
            return this.customizationValueProxy.updateById(
              existingCustomization?.id ?? '',
              {
                value: value.value,
              },
            );
          } else {
            // Create a new customization value
            return this.customizationValueProxy.create({
              cartId: cart.id,
              customizationFieldId: value.customizationFieldId,
              value: value.value,
            });
          }
        }),
      );
    }

    // Calculate total shipping cost for the cart
    // const totalShippingCost =
    //   await this.shippingCalculationHelper.calculateShippingCost(
    //     cartItem,
    //     shippingAddressId,
    //   );

    // // Update cart with final shipping cost
    // await this.cartProxy.updateById(cart.id, {
    //   shippingCost: totalShippingCost,
    // });

    return cart;
  }

  async updateCartItemQuantity({
    productVariantId,
    quantity,
  }: Omit<CartItem, 'id' | 'cartId'>): Promise<void> {
    const cartItem = await this.getCartItem(productVariantId);

    if (quantity <= 0) {
      await this.cartItemProxy.deleteById(cartItem.id);
    } else {
      await this.cartItemProxy.updateById(cartItem.id, {quantity});
    }
  }

  private async getCartItem(productVariantId: string): Promise<CartItem> {
    const {id: cartId} = await this.initCart();
    // Check if the item already exists in the cart
    const [existingCartItem] = await this.cartItemProxy.find({
      where: {
        cartId,
        productVariantId,
      },
      limit: 1,
    });
    if (!existingCartItem) {
      return this.cartItemProxy.create({
        cartId,
        productVariantId,
        quantity: 1,
      });
    }
    return existingCartItem;
  }

  async getActiveCart(
    filter?: Filter<Cart>,
  ): Promise<CartWithRelations | null> {
    const customerUser = await this.getCustomer();
    this.customer = customerUser;
    // Find active cart
    const [existingCart] = await this.cartProxy.find({
      where: {customerId: this.customer.id, status: CartStatus.Active},
      limit: 1,
      ...filter,
    });

    return existingCart ?? null;
  }

  private async getCustomer(): Promise<Customer> {
    const user = await this.getCurrentUser();
    let customer = await this.customerRepository.get(user.userTenantId ?? '');
    if (!customer) {
      const customers = await this.customerProxy.find({
        where: {
          userTenantId: user.userTenantId ?? '',
          status: CustomerStatus.ACTIVE,
        },
        limit: 1,
      });

      if (!customers?.length) {
        throw new HttpErrors.BadRequest(
          `Your account is either suspended or cannot be found in our system. Please contact support.`,
        );
      }
      customer = customers[0];
      await this.customerRepository.set(user.userTenantId ?? '', customer);
    }
    return customer;
  }

  async removeCartItem(productVariantId: string): Promise<void> {
    const cartItem = await this.getCartItem(productVariantId);
    await this.cartItemProxy.deleteById(cartItem.id);
  }

  async checkout(
    billingAddressId: string,
    shippingAddressId: string,
    ecomDukeCoinsApplied: number,
    discountConditionId?: string,
    gstNumber?: string,
    businessName?: string,
    businessAddress?: string,
    paymentMethod?: string,
  ): Promise<OrderEntity | Order> {
    const customer = await this.getCustomer();

    const [existingCart] = await this.cartProxy.find({
      where: {customerId: customer.id, status: CartStatus.Active},
      limit: 1,
      include: [
        {
          relation: 'cartItems',
          scope: {
            where: {deleted: false},
            fields: {
              productVariantId: true,
              quantity: true,
              id: true,
            },
          },
        },
      ],
      fields: {
        status: true,
        id: true,
      },
    });

    if (!existingCart) {
      throw new HttpErrors.BadRequest(`Customer have no active cart`);
    }

    // Calculate final shipping cost for the cart
    const shippings =
      await this.shippingCalculationHelper.calculateAndCreateShippings(
        existingCart.cartItems,
        shippingAddressId,
        false,
      );

    const totalShippingCost = shippings.reduce(
      (acc, shipping) => acc + Number(shipping.shippingCost),
      0,
    );
    // Update cart with final shipping cost
    await this.cartProxy.updateById(existingCart.id, {
      shippingCost: Number(totalShippingCost.toFixed(2)),
    });

    // Continue with existing checkout logic
    const orderItems = existingCart.cartItems.map(
      item =>
        new OrderItemDto({
          productVariantId: item.productVariantId,
          quantity: item.quantity,
        }),
    );

    // Include shipping cost in order creation
    const newOrder = new OrderDto({
      billingAddressId,
      shippingAddressId,
      ecomDukeCoinsUsed: ecomDukeCoinsApplied,
      customerId: customer.id,
      status: OrderStatus.Pending,
      cartId: existingCart.id,
      items: orderItems,
      discountConditionId,
      gstNumber: gstNumber,
      businessName: businessName,
      businessAddress: businessAddress,
      paymentMethod: paymentMethod as PaymentMethod,
      shippingCost: Number(totalShippingCost.toFixed(2)),
    });
    const order = await this.orderProxy.createOrder(newOrder, this.token);

    await this.shippingCalculationHelper.createShippingForOrder(
      existingCart.cartItems,
      order.id,
    );

    if (paymentMethod === PaymentMethod.CashOnDelivery) {
      await this.orderItemService.updateOrderLineItemStatusByOrderId(
        order.id,
        OrderItemStatus.Pending,
        true,
      );
      return order;
    } else {
      try {
        return await this.orderService.createOrderInPg(order, customer);
      } catch (error) {
        await this.orderProxy.deleteById(order.id);
        throw error;
      }
    }
  }

  async sendPaymentLink(order: Order): Promise<CashfreePaymentLinkResponse> {
    const currentUser = await this.getCurrentUser();
    const paymentLinkBody = new CashfreeCreatePaymentLinkRequestDto({
      customerDetails: new CustomerDetails({
        customerEmail: currentUser.email ?? '',
        customerPhone: currentUser.phone?.substring(2) ?? '',
        customerName: `${currentUser?.firstName} ${currentUser.lastName}`,
      }),
      linkPurpose: `Payment for Order #${order.id} - Thank you for shopping with Ecomdukes!`,
      linkAmount: Number(order.totalAmount),
      linkCurrency: DEFAULT_CURRENCY,
      linkNotify: new LinkNotify({sendEmail: true, sendSms: true}),
      linkId: order.id,
    });
    return this.paymentProxy.createPaymentLink(paymentLinkBody, this.token);
  }

  async getCartsWithPresignedUrl(
    carts: CartWithRelations[],
  ): Promise<CartWithRelations[]> {
    return Promise.all(
      carts.map(async cart => {
        return this.getCartWithPresignedUrl(cart);
      }),
    );
  }

  async getCartWithPresignedUrl(
    cart: CartWithRelations,
  ): Promise<CartWithRelations> {
    if (cart.cartItems?.length) {
      const items = cart.cartItems as CartItemWithRelations[];
      cart.cartItems = await Promise.all(
        items.map(async item => {
          const variant = item.productVariant as ProductVariantWithRelations;
          if (variant?.featuredAssetId && variant.featuredAsset?.preview) {
            variant.featuredAsset = this.assetService.getAssetWithPreview(
              variant.featuredAsset,
            );
          }
          return item;
        }),
      );
    }
    return cart;
  }

  async updateCartItemCustomizations(
    cartId: string,
    customizations: Partial<CustomizationValue>[],
  ): Promise<void> {
    const fieldIds = customizations
      .map(item => item.customizationFieldId ?? '')
      .filter(item => item !== '');
    const existingValues = await this.customizationValueProxy.find({
      where: {
        cartId,
        customizationFieldId: {inq: fieldIds},
      },
      fields: {
        id: true,
        customizationFieldId: true,
        value: true,
      },
    });

    const promises = customizations.map(item => {
      const existValue = existingValues.find(
        el => el.customizationFieldId === item.customizationFieldId,
      );
      if (existValue) {
        return this.customizationValueProxy.updateById(existValue?.id ?? '', {
          value: item.value,
        });
      } else {
        return this.customizationValueProxy.create({
          cartId,
          customizationFieldId: item.customizationFieldId ?? '',
          value: item.value,
        });
      }
    });

    await Promise.all(promises);
  }
}
