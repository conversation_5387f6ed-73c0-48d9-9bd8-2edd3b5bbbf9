/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/naming-convention */
import {injectable, BindingScope, inject, service} from '@loopback/core';
import {RestBindings, Request} from '@loopback/rest';
import {
  PaymentEntityPaymentMethod,
  PaymentWebhook,
  PaymentWebhookDataEntity,
} from 'cashfree-pg';
import {WebhookEventType} from '../types';
import {
  IAuthUserWithPermissions,
  ILogger,
  LOGGER,
  ModifiedRestService,
  restService,
} from '@sourceloop/core';
import {
  BillingAddress,
  Cart,
  CreateZohoInvoiceRequestDto,
  Customer,
  Order,
  OrderLineItem,
  OrderLineItemWithRelations,
  Payment,
  Shipping,
  ZohoInvoiceItem,
} from '../models';
import {OrderProxyType, PaymentProxyType} from '../datasources/configs';
import {
  CartStatus,
  OrderItemStatus,
  OrderStatus,
  PermissionKeys,
  ShippingStatus,
} from '@local/core';
import {
  AuthCodeBindings,
  CodeWriterFn,
} from '@sourceloop/authentication-service';
import * as jwt from 'jsonwebtoken';
import {systemUser} from '../constants';
import {EcomdukeserviceRequest} from '../models/ecom-service/ecomdukeserice-request.model';
import {OrderItemService} from './order-item.service';
import {Ecomdukeservice} from '../models/ecom-service/ecomdukeservice.model';

@injectable({scope: BindingScope.TRANSIENT})
export class CashfreeWebhookService {
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @restService(Order)
    private orderProxy: OrderProxyType,
    @restService(Cart)
    private cartProxy: ModifiedRestService<Cart>,
    @restService(Payment)
    private paymentProxy: PaymentProxyType,
    @inject(LOGGER.LOGGER_INJECT)
    public logger: ILogger,
    @inject(AuthCodeBindings.CODEWRITER_PROVIDER)
    private readonly codeWriter: CodeWriterFn,
    @restService(Customer)
    public customerProxy: ModifiedRestService<Customer>,
    @restService(EcomdukeserviceRequest)
    private readonly requestProxy: ModifiedRestService<EcomdukeserviceRequest>,
    @service(OrderItemService)
    private readonly orderItemService: OrderItemService,
    @restService(Shipping)
    public shippingProxy: ModifiedRestService<Shipping>,
    @restService(OrderLineItem)
    private readonly orderLineItemProxy: ModifiedRestService<OrderLineItem>,
    @restService(Ecomdukeservice)
    private readonly serviceProxy: ModifiedRestService<Ecomdukeservice>,
  ) {}

  async handlePaymentWebhook(event: PaymentWebhook): Promise<void> {
    const raw = event as unknown as Buffer;
    const json: PaymentWebhook = JSON.parse(raw.toString('utf-8'));

    this.logger.info('Payment webhook invoked with event type', json.type);
    switch (json.type) {
      case WebhookEventType.PAYMENT_SUCCESS_WEBHOOK:
        return this.handlePaymentSuccess(json.data);
      case WebhookEventType.PAYMENT_FAILED_WEBHOOK:
        return this.handlePaymentFailed(json.data);
      case WebhookEventType.PAYMENT_LINK_EVENT:
        return this.handlePaymentLinkPaid(json.data);
      default:
        break;
    }
  }

  private async generateInternalToken() {
    const codePayload: IAuthUserWithPermissions = {
      ...systemUser,
      permissions: [
        PermissionKeys.CreateNotification,
        PermissionKeys.CreateNotificationNum,
        PermissionKeys.CreatePayment,
        PermissionKeys.ViewPayment,
        PermissionKeys.ViewOrder,
        PermissionKeys.UpdateOrder,
        PermissionKeys.CreateOrder,
        PermissionKeys.UpdateCart,
        PermissionKeys.CreateCart,
        PermissionKeys.ViewCustomer,
        PermissionKeys.UpdateCustomer,
        PermissionKeys.ViewSeller,
      ],
    };

    const token = await this.codeWriter(
      jwt.sign(codePayload, process.env.JWT_SECRET as string, {
        expiresIn: '1h',
        audience: '',
        issuer: process.env.JWT_ISSUER,
        algorithm: 'HS256',
      }),
    );
    return `Bearer ${token}`;
  }

  private async generateInternalTokenRequest() {
    const codePayload: IAuthUserWithPermissions = {
      ...systemUser,
      permissions: [
        PermissionKeys.UpdateServiceRequest,
        PermissionKeys.ViewEcomdukeService,
        PermissionKeys.ViewServiceRequest,
      ],
    };

    const token = await this.codeWriter(
      jwt.sign(codePayload, process.env.JWT_SECRET as string, {
        expiresIn: '1h',
        audience: '',
        issuer: process.env.JWT_ISSUER,
        algorithm: 'HS256',
      }),
    );
    return `Bearer ${token}`;
  }

  private getPaymentMethodName(
    paymentMethod?: PaymentEntityPaymentMethod,
  ): string | undefined {
    if (!paymentMethod || typeof paymentMethod !== 'object') return undefined;
    const keys = Object.keys(paymentMethod);
    return keys.length > 0 ? keys[0] : undefined;
  }

  private async handlePaymentLinkPaid(data: any): Promise<void> {
    console.log(
      '🚀 ~ CashfreeWebhookService ~ handlePaymentLinkPaid ~ data:',
      data,
    );
    const {link_id, link_status, order} = data;

    this.logger.info(`Payment link ${link_id} status: ${link_status}`);

    if (data.payment?.payment_status !== 'SUCCESS') return;

    const serviceRequestId = data.order?.order_tags?.ecomdukeservice_request_id;
    if (!serviceRequestId) {
      this.logger.error(
        `Missing serviceRequestId in linkNotes for link ${link_id}`,
      );
      return;
    }

    const token = await this.generateInternalTokenRequest();

    // Fetch the existing request and service
    const existingRequest = await this.requestProxy.findById(
      serviceRequestId,
      {},
      token,
    );
    const service = await this.serviceProxy.findById(
      existingRequest.ecomdukeserviceId,
      {},
      token,
    );

    const newStatus = service?.fileUploadRequired
      ? 'Action Required'
      : 'Pending';

    await this.requestProxy.updateById(
      serviceRequestId,
      {
        status: newStatus,
        paymentReference: Number(data.payment.cf_payment_id),
        paidOn: data.payment?.payment_time,
        paidAmount: data.payment?.payment_amount,
        // invoiceUrl: data.payment?.invoice_url,
      },
      token,
    );

    this.logger.info(
      `Service request ${serviceRequestId} updated with status '${newStatus}' after payment.`,
    );
  }

  private async handlePaymentSuccess(data?: PaymentWebhookDataEntity) {
    const orderId = data?.order?.order_id;
    if (!orderId || !data?.payment) {
      return;
    }

    // ✅ Short-circuit: if it's a service request, handle separately and exit
    if (data.order?.order_tags?.ecomdukeservice_request_id) {
      await this.handlePaymentLinkPaid(data);
      return;
    }

    this.logger.info('Payment Success Event Triggered for order', orderId);
    const token = await this.generateInternalToken();

    await this.orderProxy.updateById(
      orderId,
      {status: OrderStatus.Paid},
      token,
    );

    await this.orderItemService.updateOrderLineItemStatusByOrderId(
      orderId,
      OrderItemStatus.Paid,
      true,
      token,
    );

    const payment = await this.paymentProxy.create(
      new Payment({
        orderId,
        paymentMethod: this.getPaymentMethodName(data.payment?.payment_method),
        paymentStatus: data.payment?.payment_status,
        transactionId: data.payment?.cf_payment_id?.toString(),
        amount: data.payment?.payment_amount,
        currency: data.payment?.payment_currency,
        paymentDate: data.payment?.payment_time,
      }),
      token,
    );

    const order = await this.orderProxy.findById(
      orderId,
      {
        fields: {cartId: true},
        include: [
          {
            relation: 'orderLineItems',
            scope: {
              include: [{relation: 'productVariant'}],
            },
          },
        ],
      },
      token,
    );

    if (order?.orderLineItems?.length) {
      await Promise.all(
        order.orderLineItems.map(lineItem =>
          this.orderLineItemProxy.updateById(
            lineItem.id,
            {status: OrderItemStatus.Paid},
            token,
          ),
        ),
      );
    }

    await this.cartProxy.updateById(
      order.cartId,
      {
        status: CartStatus.Completed,
      },
      token,
    );

    const customer = await this.customerProxy.findById(
      data.customer_details?.customer_id ?? '',
      {fields: {zohoContactId: true}},
      token,
    );

    const invoicePayload: CreateZohoInvoiceRequestDto =
      new CreateZohoInvoiceRequestDto({
        payment,
        customerName: data.customer_details?.customer_name,
        customerEmail: data.customer_details?.customer_email,
        customerPhone: data.customer_details?.customer_phone,
        zohoContactId: customer?.zohoContactId ?? '',
        billingAddress: {
          address: '123 Street Name',
          city: 'Mumbai',
          state: 'Maharashtra',
          zip: '400001',
          country: 'India',
        } as BillingAddress,
        items:
          (order?.orderLineItems?.map(
            (lineItem: OrderLineItemWithRelations) => ({
              itemId: lineItem.productVariant?.zohoItemId ?? '',
              name: lineItem.productVariant?.name ?? 'Product',
              quantity: lineItem.quantity,
              price: parseFloat(
                (lineItem.unitPrice as unknown as string) ?? '0',
              ),
              description: 'E-commerce order item',
            }),
          ) as ZohoInvoiceItem[]) ?? [],
      });

    const invoiceRes = await this.paymentProxy.createInvoice(
      invoicePayload,
      token,
    );
    const invoiceId = invoiceRes.invoiceId;

    if (invoiceId) {
      await this.orderProxy.updateById(
        orderId,
        {invoiceId, invoiceUrl: invoiceRes.invoiceUrl},
        token,
      );
      this.logger.info(
        `Invoice ${invoiceId} created and linked to order ${orderId}`,
      );

      if (
        invoiceRes.contactId &&
        invoiceRes.contactId !== customer.zohoContactId
      ) {
        await this.customerProxy.updateById(
          customer.id,
          {
            zohoContactId: invoiceRes.contactId,
          },
          token,
        );
        this.logger.info(
          `Updated customer ${customer.id} with Zoho contact ID ${invoiceRes.contactId}`,
        );
      }
    } else {
      this.logger.warn(`Invoice creation failed for order ${orderId}`);
    }

    await Promise.all(
      order?.orderLineItems?.map(async lineItem => {
        const [shipping] = await this.shippingProxy.find({
          where: {orderLineItemId: lineItem.id},
          limit: 1,
        });
        if (shipping) {
          await this.shippingProxy.updateById(shipping.id ?? '', {
            status: ShippingStatus.PROCESSING,
          });
        }
      }),
    );
  }

  private async handlePaymentFailed(data?: PaymentWebhookDataEntity) {
    const orderId = data?.order?.order_id;
    if (!orderId || !data?.payment) {
      return;
    }

    this.logger.info('Payment Failed Event Triggered for order', orderId);
    const token = await this.generateInternalToken();

    // Update order status to failed
    await this.orderProxy.updateById(
      orderId,
      {status: OrderStatus.PaymentFailed},
      token,
    );

    // Update order line items status to failed
    await this.orderItemService.updateOrderLineItemStatusByOrderId(
      orderId,
      OrderItemStatus.PaymentFailed,
      true,
      token,
    );

    // Create payment record with failed status
    await this.paymentProxy.create(
      new Payment({
        orderId,
        paymentMethod: this.getPaymentMethodName(data.payment?.payment_method),
        paymentStatus: data.payment?.payment_status,
        transactionId: data.payment?.cf_payment_id?.toString(),
        amount: data.payment?.payment_amount,
        currency: data.payment?.payment_currency,
        paymentDate: data.payment?.payment_time,
      }),
      token,
    );

    // Get order details to update cart
    const order = await this.orderProxy.findById(
      orderId,
      {fields: {cartId: true}},
      token,
    );

    // Revert cart status back to active so customer can retry
    await this.cartProxy.updateById(
      order.cartId,
      {status: CartStatus.Active},
      token,
    );

    this.logger.info(
      `Payment failed for order ${orderId}, cart ${order.cartId} reverted to active`,
    );
  }
}
