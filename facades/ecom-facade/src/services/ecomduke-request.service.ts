import {
  BindingScope,
  inject,
  injectable,
  Getter,
  service,
} from '@loopback/core';
import {HttpErrors, RestBindings, Request} from '@loopback/rest';
import {AuthenticationBindings} from 'loopback4-authentication';
import {restService, ModifiedRestService} from '@sourceloop/core';
import {Filter} from '@loopback/repository';

import {SellerProxyType, ProfileProxyType} from '../datasources/configs';
import {NotificationHelperService} from './notification-helper.service';
import {ServiceRequestStatus} from '@local/core';
import {EcomdukeserviceRequest} from '../models/ecom-service/ecomdukeserice-request.model';
import {Ecomdukeservice} from '../models/ecom-service/ecomdukeservice.model';
import {IAuthUserWithTenant, Profile, Seller} from '../models';

interface EmailData {
  productName: string;
  supportId: string;
  brand: string;
  collectionName: string;
  message: string;
  [key: string]: string;
}

@injectable({scope: BindingScope.TRANSIENT})
export class EcomdukeRequestService {
  private token: string;

  constructor(
    @restService(EcomdukeserviceRequest)
    private readonly requestProxy: ModifiedRestService<EcomdukeserviceRequest>,

    @restService(Ecomdukeservice)
    private readonly serviceProxy: ModifiedRestService<Ecomdukeservice>,

    @restService(Seller)
    private readonly sellerProxy: SellerProxyType,

    @restService(Profile)
    private readonly profileProxy: ProfileProxyType,

    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,

    @service(NotificationHelperService)
    private readonly notificationHelper: NotificationHelperService,

    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {
    this.token = this.request.headers.authorization ?? '';
  }

  async handleServiceRequestUpdate(
    id: string,
    request: Partial<EcomdukeserviceRequest>,
  ): Promise<void> {
    const existing = await this.requestProxy.findById(id);
    const statusChanged = request.status && request.status !== existing.status;

    if (request.paidAmount && !existing.paidAmount) {
      const service = await this.serviceProxy.findById(
        existing.ecomdukeserviceId,
      );
      request.status = service?.fileUploadRequired
        ? ServiceRequestStatus.ACTION_REQUIRED
        : ServiceRequestStatus.PENDING;
    }

    if (request.documents && request.documents.length > 0) {
      const existingDocs = existing.documents ?? [];
      const newDocs = request.documents.filter(
        doc => !existingDocs.includes(doc),
      );
      request.documents = [...existingDocs, ...newDocs];

      if (existing.status === ServiceRequestStatus.ACTION_REQUIRED) {
        request.status = ServiceRequestStatus.PENDING;
      }
    }

    if (statusChanged) {
      await this.sendStatusUpdateEmail(
        existing.createdBy!,
        existing,
        request.status!,
      );

      if (
        request.status === ServiceRequestStatus.COMPLETED &&
        existing.status !== ServiceRequestStatus.COMPLETED
      ) {
        const service = await this.serviceProxy.findById(
          existing.ecomdukeserviceId,
        );

        if (service.serviceType === 'Recurring') {
          const nextPaymentDate = new Date();
          if (service.recurringInterval === 'monthly') {
            nextPaymentDate.setMonth(nextPaymentDate.getMonth() + 1);
          } else if (service.recurringInterval === 'yearly') {
            nextPaymentDate.setFullYear(nextPaymentDate.getFullYear() + 1);
          }

          const newRequest: Partial<EcomdukeserviceRequest> = {
            sellerId: existing.sellerId,
            status: ServiceRequestStatus.UPCOMING,
            ecomdukeserviceId: existing.ecomdukeserviceId,
            paidAmount: Number(service.secondPartAmount),
            recurringPaymentDate: nextPaymentDate.toISOString(),
            createdBy: existing.createdBy,
          };

          await this.requestProxy.create(newRequest, this.token);
        }
      }
    }

    await this.requestProxy.updateById(id, request, this.token);
  }

  async sendStatusUpdateEmail(
    createdBy: string,
    request: EcomdukeserviceRequest,
    newStatus: string,
  ): Promise<void> {
    if (!this.token) throw new HttpErrors.Unauthorized('Missing token');

    const userTenant = await this.profileProxy.getUserTenantById(
      createdBy,
      this.token,
      {
        include: [{relation: 'user'}],
      },
    );

    const email = userTenant.user?.email;
    if (!email) return;

    let subject = '';
    let message = '';

    switch (newStatus) {
      case ServiceRequestStatus.ACTION_REQUIRED:
        subject = 'Action Required: Upload Documents to Proceed';
        message =
          'Your service request requires document upload. Please upload the necessary documents to continue processing.';
        break;

      case ServiceRequestStatus.PENDING:
        subject = 'Documents Received: Your Service Request is Under Review';
        message =
          'We have received your documents and your service request is currently under review. You’ll hear from us soon.';
        break;

      case ServiceRequestStatus.ACCEPTED:
        subject = 'Great News! Your Service Request is Approved';
        message =
          'Congratulations! Your submitted documents have been verified and your service request is approved.';
        break;

      case ServiceRequestStatus.REJECTED:
        subject = 'Update on Your Service Request: Rejected';
        message =
          'We’re sorry, but your service request has been rejected. For more details, please reach out to our support team.';
        break;

      case ServiceRequestStatus.COMPLETED:
        subject = 'Service Request Completed Successfully';
        message =
          'We’re happy to inform you that your requested service has been completed successfully.';
        break;

      case ServiceRequestStatus.ON_HOLD:
        subject = 'Your Service Request is On Hold';
        message =
          'Your request is currently on hold due to pending actions. Please contact our support team for further information.';
        break;

      default:
        return;
    }

    const emailData: EmailData = {
      productName: 'Ecomdukes',
      supportId: '<EMAIL>',
      brand: 'Ecomdukes',
      collectionName: request.ecomdukeserviceId,
      message,
      subject,
    };

    const filteredData = Object.fromEntries(
      Object.entries(emailData).filter(([_, v]) => v !== undefined),
    );

    await this.notificationHelper.sendEmail(
      'service-request-status-update.hbs',
      subject,
      filteredData,
      email,
      'Seller',
    );
  }

  async getSellerId(): Promise<string> {
    const user = await this.getCurrentUser();
    const sellers = await this.sellerProxy.find({
      where: {userTenantId: user.userTenantId ?? ''},
      limit: 1,
    });

    if (!sellers.length) {
      throw new HttpErrors.BadRequest(
        'Seller account not found or not approved',
      );
    }

    return sellers[0].id!;
  }

  attachPreviewUrls(request: EcomdukeserviceRequest): EcomdukeserviceRequest {
    const cdnOrigin = process.env.CDN_ORIGIN ?? '';
    request.previewDocuments = (request.documents ?? []).map(
      docKey => `${cdnOrigin}/${docKey}`,
    );
    return request;
  }

  async findByIdWithPreview(
    id: string,
    filter: Filter<EcomdukeserviceRequest> = {},
  ): Promise<EcomdukeserviceRequest> {
    filter = filter ?? {};
    filter.include = [
      ...(filter.include ?? []),
      {relation: 'ecomdukeRequestNotes'},
    ];

    const request = await this.requestProxy.findById(id, filter);
    return this.attachPreviewUrls(request);
  }
}
