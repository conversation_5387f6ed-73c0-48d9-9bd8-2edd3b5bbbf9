/* eslint-disable @typescript-eslint/no-explicit-any */
import {inject, Provider} from '@loopback/core';
import {getService} from '@loopback/service-proxy';
import {ClickPostDataSource} from '../datasources';
import {
  ClickPostPredictedSlaRequest,
  ClickPostPredictedSlaResponse,
  ClickPostRecommendationRequestBatch,
  ClickPostRecommendationResponse,
  ClickPostOrderRequest,
  ClickPostOrderResponse,
  ClickpostRegisterShipmentRequest,
  ClickpostRegisterShipmentResponse,
} from '../interfaces';

export interface ClickPost {
  getRecommendations: (
    body: ClickPostRecommendationRequestBatch,
    apiKey: string,
  ) => Promise<ClickPostRecommendationResponse>;
  createOrder: (
    body: ClickPostOrderRequest,
    username: string,
    apiKey: string,
  ) => Promise<ClickPostOrderResponse>;
  registerAwbTracking: (
    body: ClickpostRegisterShipmentRequest,
    apiKey: string,
  ) => Promise<ClickpostRegisterShipmentResponse>;
  getPredictedSla: (
    body: ClickPostPredictedSlaRequest,
    checkDropPincodeValidity: boolean,
    username: string,
    apiKey: string,
  ) => Promise<ClickPostPredictedSlaResponse>;
}

export class ClickPostProvider implements Provider<ClickPost> {
  constructor(
    @inject('datasources.clickPost')
    protected dataSource: ClickPostDataSource = new ClickPostDataSource(),
  ) {}

  async value(): Promise<ClickPost> {
    return getService<ClickPost>(this.dataSource);
  }
}
