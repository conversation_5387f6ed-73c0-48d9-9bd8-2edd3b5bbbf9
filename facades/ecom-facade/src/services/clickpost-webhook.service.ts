/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {injectable, BindingScope, inject} from '@loopback/core';
import {RestBindings, Request} from '@loopback/rest';
import {
  IAuthUserWithPermissions,
  ILogger,
  LOGGER,
  ModifiedRestService,
  restService,
} from '@sourceloop/core';
import {ShippingMovement, Shipping} from '../models';
import {
  AuthCodeBindings,
  CodeWriterFn,
} from '@sourceloop/authentication-service';
import * as jwt from 'jsonwebtoken';
import {systemUser} from '../constants';
import {PermissionKeys} from '@local/core';

// ClickPost webhook payload interface
export interface ClickPostWebhookPayload {
  additional: {
    latest_status: {
      remark: string;
      clickpost_status_code: number;
      reference_number: string;
      timestamp: string;
      clickpost_status_bucket_description: string;
      location: string;
      clickpost_status_description: string;
      clickpost_status_bucket: number;
      status: string;
    };
    is_rvp: boolean;
    courier_partner_edd: string;
    order_id: string;
  };
  remark: string;
  clickpost_status_description: string;
  timestamp: string;
  location: string;
  status: string;
  cp_id: number;
  clickpost_status_code: number;
  waybill: string;
}

@injectable({scope: BindingScope.TRANSIENT})
export class ClickPostWebhookService {
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @restService(ShippingMovement)
    private shippingMovementProxy: ModifiedRestService<ShippingMovement>,
    @restService(Shipping)
    private shippingProxy: ModifiedRestService<Shipping>,
    @inject(LOGGER.LOGGER_INJECT)
    public logger: ILogger,
    @inject(AuthCodeBindings.CODEWRITER_PROVIDER)
    private readonly codeWriter: CodeWriterFn,
  ) {}

  async handleShipmentTrackingWebhook(
    payload: ClickPostWebhookPayload,
  ): Promise<void> {
    this.logger.info(
      'ClickPost shipment tracking webhook received',
      JSON.stringify({
        waybill: payload.waybill,
        status: payload.status,
        orderId: payload.additional?.order_id,
      }),
    );

    try {
      // Generate internal token for API calls
      const token = await this.generateInternalToken();

      // Find shipping record by tracking number (waybill)
      const shippingRecords = await this.shippingProxy.find(
        {
          where: {trackingNumber: payload.waybill},
          limit: 1,
        },
        token,
      );

      let shippingId: string | undefined;

      if (shippingRecords.length > 0) {
        shippingId = shippingRecords[0].id;
        this.logger.info(`Found shipping record with ID: ${shippingId}`);
      } else {
        this.logger.warn(
          `No shipping record found for waybill: ${payload.waybill}`,
        );
        // You might want to create a shipping record here or handle this case differently
        // For now, we'll continue without a shippingId
      }

      // Create shipping movement record
      const shippingMovementData: Partial<ShippingMovement> = {
        waybill: payload.waybill,
        orderId: payload.additional?.order_id,
        status: payload.status as any,
        remark: payload.remark,
        location: payload.location,
        clickpostStatusDescription: payload.clickpost_status_description,
        clickpostStatusCode: payload.clickpost_status_code,
        timestamp: payload.timestamp,
        courierPartnerId: payload.cp_id,
        statusBucketDescription:
          payload.additional?.latest_status
            ?.clickpost_status_bucket_description,
        latestStatusDescription:
          payload.additional?.latest_status?.clickpost_status_description,
        statusBucket:
          payload.additional?.latest_status?.clickpost_status_bucket,
        referenceNumber: payload.additional?.latest_status?.reference_number,
        isRvp: payload.additional?.is_rvp,
        courierPartnerEdd: payload.additional?.courier_partner_edd,
        shippingId: shippingId ?? '',
      };

      // Create the shipping movement record
      const createdMovement = await this.shippingMovementProxy.create(
        shippingMovementData,
        token,
      );

      this.logger.info(
        'Shipping movement created successfully',
        JSON.stringify({
          movementId: createdMovement.id,
          waybill: payload.waybill,
          status: payload.status,
        }),
      );

      // Update shipping status if we found a shipping record
      if (shippingId) {
        await this.updateShippingStatus(shippingId, payload, token);
      }
    } catch (error) {
      this.logger.error('Error processing ClickPost webhook', error);
      throw error;
    }
  }

  private async updateShippingStatus(
    shippingId: string,
    payload: ClickPostWebhookPayload,
    token: string,
  ): Promise<void> {
    try {
      const updateData: Partial<Shipping> = {};

      // Map ClickPost status to shipping status
      // You might need to adjust this mapping based on your business logic
      switch (payload.status) {
        case 'DEL':
          updateData.status = 'DELIVERED';
          updateData.actualDeliveryDate = new Date(payload.timestamp);
          break;
        case 'OFD':
          updateData.status = 'OUT_FOR_DELIVERY';
          break;
        case 'INT':
          updateData.status = 'IN_TRANSIT';
          break;
        case 'RTO':
          updateData.status = 'RETURNED';
          break;
        default:
          updateData.status = 'IN_TRANSIT';
      }

      // Update expected delivery date if provided
      if (payload.additional?.courier_partner_edd) {
        updateData.expectedDeliveryDate = new Date(
          payload.additional.courier_partner_edd,
        );
      }

      await this.shippingProxy.updateById(shippingId, updateData, token);

      this.logger.info(
        'Shipping status updated successfully',
        JSON.stringify({
          shippingId,
          newStatus: updateData.status,
        }),
      );
    } catch (error) {
      this.logger.error('Error updating shipping status', error);
    }
  }

  private async generateInternalToken(): Promise<string> {
    const codePayload: IAuthUserWithPermissions = {
      ...systemUser,
      permissions: [
        PermissionKeys.CreateOrder,
        PermissionKeys.UpdateOrder,
        PermissionKeys.ViewOrder,
      ],
    };

    const token = await this.codeWriter(
      jwt.sign(codePayload, process.env.JWT_SECRET as string, {
        expiresIn: '1h',
        audience: '',
        issuer: process.env.JWT_ISSUER,
        algorithm: 'HS256',
      }),
    );
    return `Bearer ${token}`;
  }
}
