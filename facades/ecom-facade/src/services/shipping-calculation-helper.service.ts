/* eslint-disable @typescript-eslint/naming-convention */
import {BindingScope, inject, injectable} from '@loopback/core';
import {HttpErrors} from '@loopback/rest';
import {
  ILogger,
  LOGGER,
  ModifiedRestService,
  restService,
} from '@sourceloop/core';
import {
  Address,
  CartItem,
  InventoryItem,
  OrderLineItem,
  ProductVariant,
  ProductVariantWithRelations,
  SellerAdvancedShippingCharge,
  SellerBasicShippingCharge,
  SellerProductVariantShippingCharge,
  SellerShippingProfile,
  Shipping,
  ShippingAddress,
  ShippingMethod,
  ShippingWithRelations,
  Warehouse,
} from '../models';
import {
  BasicShippingStateType,
  ShippingMethodType,
  ShippingStatus,
} from '@local/core';
import {excludeAuditFields} from '../constants';
import moment from 'moment';
import {ClickPost} from './click-post.service';
import {toSnakeCase} from '../utils/format-conversion.util';
import {ClickPostRecommendationRequestBatch} from '../interfaces/clickpost-recommendation.interface';

@injectable({scope: BindingScope.TRANSIENT})
export class ShippingCalculationHelperService {
  constructor(
    @restService(SellerShippingProfile)
    private sellerShippingProfileProxy: ModifiedRestService<SellerShippingProfile>,
    @restService(ProductVariant)
    private productVariantProxy: ModifiedRestService<ProductVariant>,
    @restService(Shipping)
    private shippingProxy: ModifiedRestService<Shipping>,
    @restService(ShippingMethod)
    private shippingMethodProxy: ModifiedRestService<ShippingMethod>,
    @restService(Address)
    private addressService: ModifiedRestService<Address>,
    @restService(Warehouse)
    private warehouseService: ModifiedRestService<Warehouse>,
    @restService(InventoryItem)
    private inventoryItemProxy: ModifiedRestService<InventoryItem>,
    @restService(OrderLineItem)
    private orderLineItemProxy: ModifiedRestService<OrderLineItem>,
    @inject(LOGGER.LOGGER_INJECT)
    public logger: ILogger,
    @inject('services.ClickPost')
    private readonly clickpostService: ClickPost,
    @restService(ShippingAddress)
    private shippingAddressProxy: ModifiedRestService<ShippingAddress>,
  ) {}

  /**
   * Calculate shipping details for a cart item
   * @param productVariantId Product variant ID to calculate shipping for
   * @param quantity Quantity of the product
   * @param address Customer's delivery address
   * @returns Shipping object with calculated cost and delivery dates
   */
  async calculateShippingDetails(
    productVariantId: string,
    quantity: number,
    address: Address,
    cartItemId: string,
  ): Promise<Shipping> {
    // Get product variant details to access seller ID and weight
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const {createdOn, ...excludeAuditFieldsWithoutCreatedOn} =
      excludeAuditFields;
    const productVariant: ProductVariantWithRelations =
      (await this.productVariantProxy.findById(productVariantId, {
        fields: {name: true, id: true, productId: true},
        include: [
          {
            relation: 'product',
            scope: {
              fields: {
                sellerId: true,
                averageWeight: true,
                turnAroundTime: true,
              },
            },
          },
        ],
      })) as ProductVariantWithRelations;

    // Get seller's shipping profiles
    const [shippingProfile] = await this.sellerShippingProfileProxy.find({
      where: {
        sellerId: productVariant.product.sellerId,
        isActive: true,
      },
      include: [
        {relation: 'basicShippingCharges', scope: {fields: excludeAuditFields}},
        {
          relation: 'advancedShippingCharges',
          scope: {fields: excludeAuditFields},
        },
        {
          relation: 'productVariantShippingCharges',
          scope: {fields: excludeAuditFields},
        },
        {relation: 'shippingMethod', scope: {fields: excludeAuditFields}},
      ],
      limit: 1,
      order: ['createdOn DESC'],
      fields: excludeAuditFieldsWithoutCreatedOn,
    });

    const isEcomShippingServicable = await this.isEcomShippingServicable(
      address.zipCode,
      productVariantId,
    );

    // Prioritize default profile if available, otherwise use the first active one
    if (isEcomShippingServicable && !shippingProfile?.isDefault) {
      const [shippingMethod] = await this.shippingMethodProxy.find({
        fields: excludeAuditFields,
        where: {
          type: ShippingMethodType.ECOMDUKES,
        },
        limit: 1,
      });
      if (!shippingMethod) {
        throw new HttpErrors.NotFound('Shipping method not found');
      }

      const warehouse = await this.getWarehouseByInventory(productVariantId);
      let minDeliveryDate: Date | null = null;
      let maxDeliveryDate: Date | null = null;
      let expectedDeliveryDate: Date | null = null;
      try {
        const deliveryEDD = await this.clickpostService.getPredictedSla(
          [
            {
              pickup_pincode: warehouse?.zipCode ?? '',
              drop_pincode: address.zipCode,
              optional: {
                cp_id: isEcomShippingServicable.courierPartnerId,
                account_code: isEcomShippingServicable.partnerAccountCode,
              },
            },
          ],
          false,
          process.env.CLICKPOST_USERNAME ?? '',
          process.env.CLICKPOST_API_KEY ?? '',
        );

        const turnAroundTime = productVariant.product.turnAroundTime ?? 0;
        const predictedExact = deliveryEDD?.result?.[0]?.predicted_exact_sla;
        const predictedMin =
          deliveryEDD?.result?.[0]?.predicted_sla_min ?? null;
        const predictedMax =
          deliveryEDD?.result?.[0]?.predicted_sla_max ?? null;
        const expectedDays =
          predictedExact ?? Math.round((predictedMin + predictedMax) / 2);

        if (predictedMin !== null) {
          minDeliveryDate = moment()
            .add(predictedMin + turnAroundTime, 'days')
            .toDate();
        }
        if (predictedMax !== null) {
          maxDeliveryDate = moment()
            .add(predictedMax + turnAroundTime, 'days')
            .toDate();
        }
        if (expectedDays !== null) {
          expectedDeliveryDate = moment()
            .add(expectedDays + turnAroundTime, 'days')
            .toDate();
        }
      } catch (err) {
        console.error('ClickPost SLA API failed:', err);
        throw new HttpErrors.InternalServerError(
          'Failed to calculate shipping dates',
        );
      }

      return new Shipping({
        sellerId: productVariant.product.sellerId,
        shippingCost: isEcomShippingServicable.shippingCost ?? 0,
        status: ShippingStatus.PENDING,
        minDeliveryDate: minDeliveryDate as Date,
        maxDeliveryDate: maxDeliveryDate as Date,
        expectedDeliveryDate: expectedDeliveryDate as Date,
        shippingMethodId: shippingMethod.id,
        cartItemId,
        shippingPartnerId: isEcomShippingServicable.courierPartnerId,
        partnerAccountCode: isEcomShippingServicable.partnerAccountCode,
      });
    }

    // Calculate shipping cost based on hierarchical rules
    let shippingCost = 0;
    const minDeliveryDays = shippingProfile.fallbackMinDeliveryDays;
    const maxDeliveryDays = shippingProfile.fallbackMaxDeliveryDays;

    // 1. Check for product-specific shipping charges
    const productSpecificCharge = this.findProductSpecificCharge(
      shippingProfile.productVariantShippingCharges as SellerProductVariantShippingCharge[],
      productVariantId,
      address.state,
    );

    if (productSpecificCharge) {
      shippingCost = productSpecificCharge.price * quantity;
    } else if (productVariant.product.averageWeight) {
      // 2. Check for advanced weight-based shipping charges
      const advancedCharge = this.findAdvancedWeightBasedCharge(
        shippingProfile.advancedShippingCharges,
        productVariant.product.averageWeight,
        address.state,
      );

      if (advancedCharge) {
        shippingCost = advancedCharge.price * quantity;
      } else {
        // 3. Check for basic in-state/out-state shipping charges
        const warehouse = await this.getWarehouseByInventory(productVariantId);
        if (!warehouse) {
          throw new HttpErrors.NotFound('Warehouse not found');
        }

        const basicCharge = this.findBasicShippingCharge(
          shippingProfile.basicShippingCharges as SellerBasicShippingCharge[],
          address.state,
          warehouse.state,
        );

        if (basicCharge) {
          shippingCost = basicCharge.price * quantity;
        } else {
          // 4. Fallback to the shipping profile's default price
          shippingCost = shippingProfile.fallbackPrice * quantity;
        }
      }
    } else {
      // If no weight info, go directly to basic shipping charges
      const warehouse = await this.getWarehouseByInventory(productVariantId);
      if (!warehouse) {
        throw new HttpErrors.NotFound('Warehouse not found');
      }

      const basicCharge = this.findBasicShippingCharge(
        shippingProfile.basicShippingCharges as SellerBasicShippingCharge[],
        address.state,
        warehouse.state,
      );

      if (basicCharge) {
        shippingCost = basicCharge.price * quantity;
      } else {
        // Fallback to the shipping profile's default price
        shippingCost = shippingProfile.fallbackPrice * quantity;
      }
    }

    // Create and return the shipping object with calculated cost
    return new Shipping({
      sellerId: productVariant.product.sellerId,
      shippingCost: shippingCost,
      status: ShippingStatus.PENDING,
      minDeliveryDate: new Date(moment().add(minDeliveryDays, 'days').format()),
      maxDeliveryDate: new Date(moment().add(maxDeliveryDays, 'days').format()),
      expectedDeliveryDate: new Date(
        moment()
          .add(Math.floor((minDeliveryDays + maxDeliveryDays) / 2), 'days')
          .format(),
      ),
      shippingMethodId: shippingProfile.shippingMethodId,
      cartItemId,
    });
  }

  /**
   * Find product-specific shipping charge
   */
  private findProductSpecificCharge(
    charges: SellerProductVariantShippingCharge[],
    productVariantId: string,
    customerState: string,
  ): SellerProductVariantShippingCharge | undefined {
    if (!charges || charges.length === 0) return undefined;

    // First check for exact product and state match
    const exactMatch = charges.find(
      charge =>
        charge.productVariantId === productVariantId &&
        charge.state === customerState &&
        charge.isActive,
    );

    if (exactMatch) return exactMatch;

    // Then check for ALL_STATES match for this product
    return charges.find(
      charge =>
        charge.productVariantId === productVariantId &&
        charge.state === 'ALL_STATES' &&
        charge.isActive,
    );
  }

  /**
   * Find advanced weight-based shipping charge
   */
  private findAdvancedWeightBasedCharge(
    charges: SellerAdvancedShippingCharge[],
    averageWeight: number,
    customerState: string,
  ): SellerAdvancedShippingCharge | undefined {
    if (!charges || charges.length === 0) return undefined;

    return charges.find(
      charge =>
        charge.state === customerState &&
        averageWeight >= charge.minWeightGrams &&
        averageWeight <= charge.maxWeightGrams &&
        charge.isActive,
    );
  }

  /**
   * Find basic in-state/out-state shipping charge
   */
  private findBasicShippingCharge(
    charges: SellerBasicShippingCharge[],
    customerState: string,
    sellerState: string,
  ): SellerBasicShippingCharge | undefined {
    if (!charges || charges.length === 0) return undefined;

    const stateType =
      customerState === sellerState
        ? BasicShippingStateType.IN_STATE
        : BasicShippingStateType.OUT_STATE;

    return charges.find(
      charge => charge.stateType === stateType && charge.isActive,
    );
  }

  /**
   * Calculate and create shipping records for all cart items
   * @param cartItems Cart items to calculate shipping for
   * @param shippingAddressId Customer's shipping address ID
   * @returns Total shipping cost for all items
   */
  async calculateAndCreateShippings(
    cartItems: CartItem[],
    shippingAddressId: string,
    skipCreation: boolean,
  ): Promise<Shipping[]> {
    if (!cartItems || cartItems.length === 0) return [];

    // Fetch customer's shipping address (DROP address)
    const shippingAddress =
      await this.addressService.findById(shippingAddressId);

    // Calculate shipping info for each cart item
    const shippings = await Promise.all(
      cartItems.map(item =>
        this.calculateShippingDetails(
          item.productVariantId,
          item.quantity,
          shippingAddress,
          item.id,
        ),
      ),
    );

    if (skipCreation) return shippings;

    // Create all shipping records first
    const createdShippings = await Promise.all(
      shippings.map(shipping => this.shippingProxy.create(shipping)),
    );

    // Create both DROP and PICKUP shipping addresses
    await Promise.all(
      createdShippings.flatMap(shipping => {
        const cartItem = cartItems.find(
          item => item.id === shipping.cartItemId,
        );
        if (!cartItem) return [];

        return [
          // DROP address
          this.shippingAddressProxy.create({
            addressLine1: shippingAddress.addressLine1,
            addressLine2: shippingAddress.addressLine2,
            city: shippingAddress.city,
            state: shippingAddress.state,
            zipCode: shippingAddress.zipCode,
            country: shippingAddress.country,
            customerId: shippingAddress.customerId,
            locality: shippingAddress.locality,
            name: shippingAddress.name,
            phoneNumber: shippingAddress.phoneNumber,
            landmark: shippingAddress.landmark,
            alternativePhoneNumber: shippingAddress.alternativePhoneNumber,
            addressType: 'DROP',
            shippingId: shipping.id,
          } as ShippingAddress),

          // PICKUP address (from warehouse)
          (async () => {
            const warehouse = await this.getWarehouseByInventory(
              cartItem.productVariantId,
            );
            if (!warehouse) return;

            await this.shippingAddressProxy.create({
              addressLine1: warehouse.street,
              city: warehouse.city,
              state: warehouse.state,
              zipCode: warehouse.zipCode,
              country: warehouse.country,
              customerId: shippingAddress.customerId,
              locality: warehouse.street ?? '',
              name: warehouse.name ?? 'Warehouse',
              phoneNumber: warehouse.phone ?? '',
              addressType: 'PICKUP',
              shippingId: shipping.id,
            } as ShippingAddress);
          })(),
        ];
      }),
    );

    return createdShippings;
  }

  /**
   * Calculate shipping details for a product variant and pincode
   * @param productVariantId Product variant ID to calculate shipping for
   * @param pincode Delivery pincode
   * @returns Shipping details with cost, delivery dates, and shipping type
   */
  async calculateShippingByPincode(
    productVariantId: string,
    pincode: string,
  ): Promise<{
    shippingCost: number;
    minDeliveryDate: Date;
    maxDeliveryDate: Date;
    expectedDeliveryDate: Date;
    shippingType: 'ECOMDUKES' | 'SELF_SHIPPING';
    isServiceable: boolean;
    message?: string;
  }> {
    // Check if ECOMDUKES shipping is serviceable
    const isEcomShippingServicable = await this.isEcomShippingServicable(
      pincode,
      productVariantId,
    );

    const excludeAuditFieldsWithoutCreatedOn = excludeAuditFields;

    const productVariant: ProductVariantWithRelations =
      (await this.productVariantProxy.findById(productVariantId, {
        include: [
          {
            relation: 'product',
            scope: {
              fields: excludeAuditFields,
            },
          },
        ],
        fields: excludeAuditFieldsWithoutCreatedOn,
      })) as ProductVariantWithRelations;

    // Get seller's shipping profiles
    const [shippingProfile] = await this.sellerShippingProfileProxy.find({
      where: {
        sellerId: productVariant.product.sellerId,
        isActive: true,
      },
      include: [
        {
          relation: 'basicShippingCharges',
          scope: {fields: excludeAuditFields},
        },
        {
          relation: 'advancedShippingCharges',
          scope: {fields: excludeAuditFields},
        },
        {
          relation: 'productVariantShippingCharges',
          scope: {fields: excludeAuditFields},
        },
        {relation: 'shippingMethod', scope: {fields: excludeAuditFields}},
      ],
      limit: 1,
      order: ['createdOn DESC'],
    });

    if (!shippingProfile) {
      throw new HttpErrors.NotFound('Shipping profile not found for seller');
    }

    const turnAroundTime = productVariant.product.turnAroundTime ?? 0;
    if (isEcomShippingServicable && !shippingProfile?.isDefault) {
      // ECOMDUKES shipping is available
      const warehouse = await this.getWarehouseByInventory(productVariantId);
      let minDeliveryDate: Date | null = null;
      let maxDeliveryDate: Date | null = null;
      let expectedDeliveryDate: Date | null = null;

      try {
        const deliveryEDD = await this.clickpostService.getPredictedSla(
          [
            {
              pickup_pincode: warehouse?.zipCode ?? '',
              drop_pincode: pincode,
              optional: {
                cp_id: isEcomShippingServicable.courierPartnerId,
                account_code: isEcomShippingServicable.partnerAccountCode,
              },
            },
          ],
          false,
          process.env.CLICKPOST_USERNAME ?? '',
          process.env.CLICKPOST_API_KEY ?? '',
        );

        const predictedExact = deliveryEDD?.result?.[0]?.predicted_exact_sla;
        const predictedMin =
          deliveryEDD?.result?.[0]?.predicted_sla_min ?? null;
        const predictedMax =
          deliveryEDD?.result?.[0]?.predicted_sla_max ?? null;
        const expectedDays =
          predictedExact ?? Math.round((predictedMin + predictedMax) / 2);

        if (predictedMin !== null) {
          minDeliveryDate = moment()
            .add(predictedMin + turnAroundTime, 'days')
            .toDate();
        }
        if (predictedMax !== null) {
          maxDeliveryDate = moment()
            .add(predictedMax + turnAroundTime, 'days')
            .toDate();
        }
        if (expectedDays !== null) {
          expectedDeliveryDate = moment()
            .add(expectedDays + turnAroundTime, 'days')
            .toDate();
        }

        // If no valid SLA data, use fallback dates
        if (!minDeliveryDate || !maxDeliveryDate || !expectedDeliveryDate) {
          minDeliveryDate = new Date(moment().add(2, 'days').format());
          maxDeliveryDate = new Date(moment().add(5, 'days').format());
          expectedDeliveryDate = new Date(moment().add(3, 'days').format());
        }
      } catch (err) {
        console.error('ClickPost SLA API failed:', err);
        // Fallback to default dates
        minDeliveryDate = new Date(moment().add(2, 'days').format());
        maxDeliveryDate = new Date(moment().add(5, 'days').format());
        expectedDeliveryDate = new Date(moment().add(3, 'days').format());
      }

      return {
        shippingCost: isEcomShippingServicable.shippingCost ?? 0,
        minDeliveryDate: minDeliveryDate as Date,
        maxDeliveryDate: maxDeliveryDate as Date,
        expectedDeliveryDate: expectedDeliveryDate as Date,
        shippingType: 'ECOMDUKES',
        isServiceable: true,
        message: 'ECOMDUKES shipping is available for this pincode',
      };
    } else {
      // Fall back to seller's self shipping
      let shippingCost = 0;

      // 1. Check for product-specific shipping charges
      const productCharge = (
        shippingProfile.productVariantShippingCharges as SellerProductVariantShippingCharge[]
      )?.find(charge => charge.productVariantId === productVariantId);

      if (productCharge) {
        shippingCost = productCharge.price;
      } else if (productVariant.product.averageWeight) {
        // 2. Fallback to the shipping profile's default price
        shippingCost = shippingProfile.fallbackPrice;
      } else {
        // If no weight info, go directly to basic shipping charges
        const warehouse = await this.getWarehouseByInventory(productVariantId);
        if (!warehouse) {
          throw new HttpErrors.NotFound('Warehouse not found');
        } else {
          // Fallback to the shipping profile's default price
          shippingCost = shippingProfile.fallbackPrice;
        }
      }

      // Calculate delivery dates for self shipping
      const minDeliveryDays =
        shippingProfile.fallbackMinDeliveryDays + turnAroundTime;
      const maxDeliveryDays =
        shippingProfile.fallbackMaxDeliveryDays + turnAroundTime;

      return {
        shippingCost: shippingCost,
        minDeliveryDate: new Date(
          moment().add(minDeliveryDays, 'days').format(),
        ),
        maxDeliveryDate: new Date(
          moment().add(maxDeliveryDays, 'days').format(),
        ),
        expectedDeliveryDate: new Date(
          moment()
            .add(Math.floor((minDeliveryDays + maxDeliveryDays) / 2), 'days')
            .format(),
        ),
        shippingType: 'SELF_SHIPPING',
        isServiceable: true,
        message: 'Self shipping is available for this pincode',
      };
    }
  }

  async isEcomShippingServicable(
    dropPincode: string,
    productVariantId: string,
  ): Promise<{
    courierPartnerId: number;
    partnerAccountCode?: string;
    shippingCost?: number;
  } | null> {
    const warehouse = await this.getWarehouseByInventory(productVariantId);
    if (!warehouse) {
      throw new HttpErrors.NotFound('Warehouse not found');
    }
    const requestBody = [
      {
        pickupPincode: warehouse.zipCode,
        dropPincode,
        orderType: 'PREPAID',
        referenceNumber: 'CHECK-SERVICE-REF',
        item: 'generic',
        invoiceValue: 500,
        deliveryType: 'FORWARD',
        weight: 1000,
        height: 10,
        length: 10,
        breadth: 10,
        additional: {
          customFields: [],
        },
      },
    ];
    const convertedReqBody = toSnakeCase(requestBody);
    try {
      const response = await this.clickpostService.getRecommendations(
        convertedReqBody as ClickPostRecommendationRequestBatch,
        process.env.CLICKPOST_API_KEY ?? '',
      );

      const result = response?.result?.[0];

      if (
        result?.pincode_serviceable === true &&
        result?.preference_array?.length > 0
      ) {
        const preferredPartner = result.preference_array[0];
        return {
          courierPartnerId: preferredPartner.cp_id,
          partnerAccountCode: preferredPartner.account_code ?? undefined,
          shippingCost: preferredPartner.shipping_charge ?? 0,
        };
      }

      return null;
    } catch (error) {
      console.error('ClickPost recommendation API error:', error);
      return null;
    }
  }

  async getWarehouseByInventory(
    productVariantId: string,
  ): Promise<Warehouse | null> {
    // Find inventory item with highest stock available for this product variant
    const [inventoryItem] = await this.inventoryItemProxy.find({
      where: {
        productVariantId: productVariantId,
        stockOnHand: {gt: 0},
      },
      order: ['stockOnHand DESC'],
      limit: 1,
    });

    if (!inventoryItem) {
      return null;
    }

    // Get warehouse details for the inventory item
    const warehouse = await this.warehouseService.findById(
      inventoryItem.warehouseId,
    );
    return warehouse;
  }

  async createShippingForOrder(
    cartItems: CartItem[],
    orderId: string,
  ): Promise<Shipping> {
    // Get all shipping records associated with the cart items
    const shippings = (await this.shippingProxy.find({
      where: {
        cartItemId: {inq: cartItems.map(item => item.id)},
      },
      include: [
        {
          relation: 'cartItem',
          scope: {
            fields: {productVariantId: true},
          },
        },
      ],
    })) as ShippingWithRelations[];

    if (!shippings?.length) {
      throw new HttpErrors.NotFound('Shipping not found');
    }

    // Get all order line items for this order
    const orderLineItems = await this.orderLineItemProxy.find({
      where: {
        orderId,
      },
      fields: {id: true, productVariantId: true},
    });

    if (!orderLineItems.length) {
      throw new HttpErrors.NotFound('Order line item not found');
    }

    // Create a map of product variant IDs to order line item IDs
    const productVariantToOrderLineItemMap = new Map(
      orderLineItems.map(item => [item.productVariantId, item.id]),
    );

    // Update each shipping record with the corresponding order line item ID
    await Promise.all(
      shippings.map(shipping => {
        // Get the product variant ID from the associated cart item
        const productVariantId = shipping.cartItem?.productVariantId;

        if (!productVariantId) {
          this.logger.warn(
            `No product variant ID found for shipping ${shipping.id}`,
          );
          return Promise.resolve();
        }

        // Find the matching order line item ID
        const orderLineItemId =
          productVariantToOrderLineItemMap.get(productVariantId);

        if (!orderLineItemId) {
          this.logger.warn(
            `No order line item found for product variant ${productVariantId}`,
          );
          return Promise.resolve();
        }

        // Update the shipping record with the order line item ID
        return this.shippingProxy.updateById(shipping.id ?? '', {
          orderLineItemId: orderLineItemId,
        });
      }),
    );

    return shippings[0];
  }
}
