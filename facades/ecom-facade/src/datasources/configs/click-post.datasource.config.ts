/* eslint-disable @typescript-eslint/naming-convention */
export const ClickpostDataSourceConfig = {
  name: 'clickpost',
  connector: 'rest',
  baseURL: process.env.CLICKPOST_API_URL,
  crud: false,
  options: {
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
    },
  },
  operations: [
    {
      template: {
        method: 'POST',
        url: `${process.env.CLICKPOST_API_URL}/api/v1/recommendation_api/`,
        headers: {
          'content-type': 'application/json',
        },
        body: '{body}',
        query: {
          key: '{apiKey}',
        },
      },
      functions: {
        getRecommendations: ['body', 'apiKey'],
      },
    },
    {
      template: {
        method: 'POST',
        url: `${process.env.CLICKPOST_API_URL}/api/v3/create-order/`,
        headers: {
          'content-type': 'application/json',
        },
        body: '{body}',
        query: {
          username: '{username}',
          key: '{api<PERSON><PERSON>}',
        },
      },
      functions: {
        createOrder: ['body', 'username', 'apiKey'],
      },
    },
    {
      template: {
        method: 'POST',
        url: `${process.env.CLICKPOST_API_URL}/api/v3/tracking/awb-register/`,
        headers: {
          'content-type': 'application/json',
        },
        body: '{body}',
        query: {
          key: '{apiKey}',
        },
      },
      functions: {
        registerAwbTracking: ['body', 'apiKey'],
      },
    },
    {
      template: {
        method: 'POST',
        url: `https://ds.clickpost.in/api/v2/predicted_sla_api/`,
        headers: {
          'content-type': 'application/json',
        },
        body: '{body}',
        query: {
          check_drop_pincode_validity: '{checkDropPincodeValidity}',
          username: '{username}',
          key: '{apiKey}',
        },
      },
      functions: {
        getPredictedSla: [
          'body',
          'checkDropPincodeValidity',
          'username',
          'apiKey',
        ],
      },
    },
  ],
};
