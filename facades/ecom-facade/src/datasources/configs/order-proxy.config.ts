import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';
import {Order, OrderDto} from '../../models';

export type OrderProxyType = {
  createOrder(order: Omit<OrderDto, 'id'>, token: string): Promise<Order>;
} & ModifiedRestService<Order>;

export const OrderProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'POST',
      url: '/orders',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{order}',
    },
    functions: {
      createOrder: ['order', 'token'],
    },
  },
];
