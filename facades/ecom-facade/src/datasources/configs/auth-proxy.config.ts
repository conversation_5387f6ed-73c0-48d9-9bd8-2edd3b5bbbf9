import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';
import {
  LoginRequest,
  AuthTokenRequest,
  AuthRefreshTokenRequest,
  CodeResponse,
  TokenResponse,
  ForgetPasswordDto,
  LocalUserProfileDto,
  ResetPasswordWithClient,
  SignupRequestDto,
  SignupWithTokenReponseDto,
} from '@sourceloop/authentication-service';
import {Auth, IAuthUserWithTenant} from '../../models';
import {AnyObject} from '@loopback/repository';
export type AuthProxyType = {
  login(credentials: LoginRequest): Promise<CodeResponse>;
  getToken(tokenRequest: AuthTokenRequest): Promise<TokenResponse>;
  exchangeToken(
    refreshRequest: AuthRefreshTokenRequest,
    token: string,
    deviceId?: string,
  ): Promise<TokenResponse>;
  getMe(token: string, xOrigin: string): Promise<IAuthUserWithTenant>;
  requestSignup(
    signUpRequest: SignupRequestDto<LocalUserProfileDto>,
  ): Promise<void>;
  signupWithToken(
    req: LocalUserProfileDto,
    token: string,
    xOrigin: string,
  ): Promise<SignupWithTokenReponseDto<AnyObject>>;
  forgetPassword(
    req: ForgetPasswordDto,
    xOrigin: string,
  ): Promise<{code: string}>;
  resetPassword(req: ResetPasswordWithClient): Promise<void>;
  changePassword(
    req: {
      username: string;
      password: string;
      oldPassword: string;
      refreshToken: string;
    },
    token: string,
  ): Promise<void>;
} & ModifiedRestService<Auth>;

export const AuthProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'POST',
      url: `/auth/login`,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
      },
      body: '{credentials}',
      query: {},
    },
    functions: {
      login: ['credentials'],
    },
  },
  {
    template: {
      method: 'POST',
      url: `/auth/token`,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
      },
      body: '{tokenRequest}',
      query: {},
    },
    functions: {
      getToken: ['tokenRequest'],
    },
  },
  {
    template: {
      method: 'POST',
      url: `/auth/token-refresh`,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        device_id: '{deviceId}',
      },
      body: '{refreshRequest}',
      query: {},
    },
    functions: {
      exchangeToken: ['refreshRequest', 'token', 'deviceId'],
    },
  },
  {
    template: {
      method: 'GET',
      url: `/auth/me`,
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
        'x-origin': '{xOrigin}',
      },
      query: {},
    },
    functions: {
      getMe: ['token', 'xOrigin'],
    },
  },
  {
    template: {
      method: 'POST',
      url: `/auth/sign-up/create-token`,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
      },
      body: '{signUpRequest}',
      query: {},
    },
    functions: {
      requestSignup: ['signUpRequest'],
    },
  },
  {
    template: {
      method: 'POST',
      url: `/auth/sign-up/create-user`,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
        'x-origin': '{xOrigin}',
      },
      body: '{req}',
      query: {},
    },
    functions: {
      signupWithToken: ['req', 'token', 'xOrigin'],
    },
  },
  {
    template: {
      method: 'POST',
      url: `/auth/forget-password`,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        'x-origin': '{xOrigin}',
      },
      body: '{req}',
      query: {},
    },
    functions: {
      forgetPassword: ['req', 'xOrigin'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: `/auth/reset-password`,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{req}',
      query: {},
    },
    functions: {
      resetPassword: ['req', 'token'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: `/auth/change-password`,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{req}',
      query: {},
    },
    functions: {
      changePassword: ['req', 'token'],
    },
  },
];
