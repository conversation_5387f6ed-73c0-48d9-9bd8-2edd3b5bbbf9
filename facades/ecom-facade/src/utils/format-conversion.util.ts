import {AnyObject} from '@loopback/repository';

export function toSnakeCase(obj: AnyObject): AnyObject {
  if (Array.isArray(obj)) {
    return obj.map(toSnakeCase);
  } else if (obj !== null && typeof obj === 'object') {
    return Object.entries(obj).reduce((acc, [key, value]) => {
      const snakeKey = key.replace(
        /[A-Z]/g,
        letter => `_${letter.toLowerCase()}`,
      );
      acc[snakeKey] = toSnakeCase(value);
      return acc;
    }, {} as AnyObject);
  }
  return obj;
}

export function toCamelCase(obj: AnyObject): AnyObject {
  if (Array.isArray(obj)) {
    return obj.map(toCamelCase);
  } else if (obj !== null && typeof obj === 'object') {
    return Object.entries(obj).reduce((acc, [key, value]) => {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) =>
        letter.toUpperCase(),
      );
      acc[camelKey] = toCamelCase(value);
      return acc;
    }, {} as AnyObject);
  }
  return obj;
}
