import {Model, model, property} from '@loopback/repository';

@model()
export class ChangePasswordRequest extends Model {
  @property({
    type: 'string',
    required: true,
  })
  username: string;

  @property({
    type: 'string',
    required: true,
  })
  password: string;

  @property({
    type: 'string',
    required: true,
  })
  oldPassword: string;

  @property({
    type: 'string',
    required: true,
  })
  refreshToken: string;

  constructor(data?: Partial<ChangePasswordRequest>) {
    super(data);
  }
}

export interface ChangePasswordRequestRelations {
  // describe navigational properties here
}

export type ChangePasswordRequestWithRelations = ChangePasswordRequest & ChangePasswordRequestRelations;
