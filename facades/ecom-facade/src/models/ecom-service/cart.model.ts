import {CartStatus} from '@local/core';
import {belongsTo, hasMany, model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {CartItem} from './cart-item.model';
import {Customer} from '../auth-service';
import {PromoCode} from './promo-code.model';

@model({name: 'carts'})
export class Cart extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      enum: Object.values(CartStatus),
    },
  })
  status: string;

  @property({
    type: 'number',
    name: 'ecom_duke_coins_applied',
    default: 0,
  })
  ecomDukeCoinsApplied?: number;

  @property({
    type: 'number',
    name: 'shipping_cost',
    default: 0,
  })
  shippingCost?: number;

  @hasMany(() => CartItem, {keyTo: 'cartId'})
  cartItems: CartItem[];

  @belongsTo(() => Customer, {keyTo: 'id'})
  customerId: string;

  @belongsTo(() => PromoCode, {keyTo: 'id'})
  promoCodeId?: string;

  constructor(data?: Partial<Cart>) {
    super(data);
  }
}

export interface CartRelations {
  // describe navigational properties here
}

export type CartWithRelations = Cart & CartRelations;
