import {model, property, belongsTo, hasMany} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {ShippingMethod} from './shipping-method.model';
import {SellerBasicShippingCharge} from './seller-basic-shipping-charge.model';
import {SellerAdvancedShippingCharge} from './seller-advanced-shipping-charge.model';
import {SellerProductVariantShippingCharge} from './seller-product-variant-shipping-charge.model';

@model({
  name: 'seller_shipping_profiles',
})
export class SellerShippingProfile extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'boolean',
    default: false,
    name: 'is_default',
  })
  isDefault?: boolean;

  @property({
    type: 'boolean',
    default: true,
    name: 'is_active',
  })
  isActive?: boolean;

  @property({
    type: 'string',
    required: true,
    name: 'seller_id',
  })
  sellerId: string;

  @property({
    type: 'number',
    required: true,
    name: 'fallback_min_delivery_days',
  })
  fallbackMinDeliveryDays: number;

  @property({
    type: 'number',
    required: true,
    name: 'fallback_max_delivery_days',
  })
  fallbackMaxDeliveryDays: number;

  @property({
    type: 'number',
    required: true,
    name: 'fallback_price',
  })
  fallbackPrice: number;

  @belongsTo(() => ShippingMethod, {keyTo: 'id'}, {name: 'shipping_method_id'})
  shippingMethodId: string;

  @hasMany(() => SellerBasicShippingCharge, {keyTo: 'shippingProfileId'})
  basicShippingCharges: SellerBasicShippingCharge[];

  @hasMany(() => SellerAdvancedShippingCharge, {keyTo: 'shippingProfileId'})
  advancedShippingCharges: SellerAdvancedShippingCharge[];

  @hasMany(() => SellerProductVariantShippingCharge, {
    keyTo: 'shippingProfileId',
  })
  productVariantShippingCharges: SellerProductVariantShippingCharge[];

  constructor(data?: Partial<SellerShippingProfile>) {
    super(data);
  }
}

export interface SellerShippingProfileRelations {
  shippingMethod?: ShippingMethod;
  basicShippingCharges?: SellerBasicShippingCharge[];
  advancedShippingCharges?: SellerAdvancedShippingCharge[];
  productVariantShippingCharges?: SellerProductVariantShippingCharge[];
}

export type SellerShippingProfileWithRelations = SellerShippingProfile &
  SellerShippingProfileRelations;
