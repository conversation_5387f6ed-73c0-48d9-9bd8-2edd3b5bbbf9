import {
  model,
  property,
  belongsTo,
  hasOne,
  hasMany,
} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Order, OrderWithRelations} from './order.model';
import {
  ProductVariant,
  ProductVariantWithRelations,
} from './product-variant.model';
import {Review} from './review.model';
import {OrderItemStatus} from '@local/core';
import {Seller, SellerWithRelations} from '../auth-service';
import {CustomizationValue} from './customization-value.model';
import {Shipping} from './shipping.model';

@model({name: 'order_line_items'})
export class OrderLineItem extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'number',
    required: true,
  })
  quantity: number;

  @property({
    type: 'number',
    required: true,
    name: 'unit_price',
  })
  unitPrice: number;

  @property({
    type: 'number',
    required: true,
    name: 'total_price',
  })
  totalPrice: number;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      enum: Object.values(OrderItemStatus),
    },
  })
  status: string;

  @property({
    type: 'string',
    name: 'rejection_reason',
  })
  rejectionReason: string;

  @property({
    type: 'boolean',
    name: 'cancelled_by_admin',
  })
  cancelledByAdmin?: boolean;

  @property({
    type: 'string',
    name: 'cancelled_reason',
  })
  cancelledReason?: string;

  @belongsTo(() => Seller, {keyTo: 'id'}, {name: 'seller_id'})
  sellerId: string;

  @belongsTo(() => Order, {keyTo: 'id'}, {name: 'order_id'})
  orderId: string;

  @belongsTo(() => ProductVariant, {keyTo: 'id'}, {name: 'product_variant_id'})
  productVariantId: string;

  @hasOne(() => Review, {keyTo: 'orderLineItemId'})
  review: Review;

  @hasMany(() => CustomizationValue, {keyTo: 'orderLineItemId'})
  customizationValues: CustomizationValue[];

  @hasOne(() => Shipping, {keyTo: 'orderLineItemId'})
  shipping: Shipping;

  constructor(data?: Partial<OrderLineItem>) {
    super(data);
  }
}

export interface OrderLineItemRelations {
  productVariant?: ProductVariantWithRelations;
  seller?: SellerWithRelations;
  order?: OrderWithRelations;
  // describe navigational properties here
}

export type OrderLineItemWithRelations = OrderLineItem & OrderLineItemRelations;
