import {ClickpostTrackingStatusCode} from '@local/core';
import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Shipping} from './shipping.model';

@model({name: 'shipping_movements'})
export class ShippingMovement extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
    name: 'id',
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'waybill',
  })
  waybill: string;

  @property({
    type: 'string',
    required: false,
    name: 'order_id',
  })
  orderId?: string;

  @property({
    type: 'string',
    name: 'status',
  })
  status?: string;

  @property({
    type: 'string',
    name: 'remark',
  })
  remark?: string;

  @property({
    type: 'string',
    name: 'location',
  })
  location?: string;

  @property({
    type: 'string',
    name: 'clickpost_status_description',
  })
  clickpostStatusDescription?: string;

  @property({
    type: 'number',
    name: 'clickpost_status_code',
  })
  clickpostStatusCode?: ClickpostTrackingStatusCode;

  @property({
    type: 'date',
    name: 'timestamp',
  })
  timestamp?: string;

  @property({
    type: 'number',
    name: 'courier_partner_id',
  })
  courierPartnerId?: number;

  @property({
    type: 'string',
    name: 'status_bucket_description',
  })
  statusBucketDescription?: string;

  @property({
    type: 'string',
    name: 'latest_status_description',
  })
  latestStatusDescription?: string;

  @property({
    type: 'number',
    name: 'status_bucket',
  })
  statusBucket?: number;

  @property({
    type: 'string',
    name: 'reference_number',
  })
  referenceNumber?: string;

  @property({
    type: 'boolean',
    name: 'is_rvp',
  })
  isRvp?: boolean;

  @property({
    type: 'string',
    name: 'courier_partner_edd',
  })
  courierPartnerEdd?: string;

  @belongsTo(() => Shipping, {keyTo: 'id'}, {name: 'shipping_id'})
  shippingId: string;

  constructor(data?: Partial<ShippingMovement>) {
    super(data);
  }
}

export interface ShippingMovementRelations {
  // describe navigational properties here
}

export type ShippingMovementWithRelations = ShippingMovement &
  ShippingMovementRelations;
