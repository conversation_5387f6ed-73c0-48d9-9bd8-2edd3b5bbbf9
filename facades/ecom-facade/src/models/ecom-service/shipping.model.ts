import {model, property, belongsTo, hasMany} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {CartItem} from './cart-item.model';
import {OrderLineItem} from './order-line-item.model';
import {ShippingMethod} from './shipping-method.model';
import {ShippingStatus, ShippingMethodType} from '@local/core';
import {ShippingAddress} from './shipping-address.model';

@model({
  name: 'shipping',
})
export class Shipping extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    name: 'seller_id',
    required: true,
  })
  sellerId: string;

  @property({
    type: 'number',
    required: true,
    name: 'shipping_cost',
  })
  shippingCost: number;

  @property({
    type: 'string',
    jsonSchema: {
      enum: Object.values(ShippingStatus),
    },
    default: ShippingStatus.PENDING,
  })
  status: string;

  @property({
    type: 'date',
    name: 'min_delivery_date',
  })
  minDeliveryDate?: Date;

  @property({
    type: 'date',
    name: 'max_delivery_date',
  })
  maxDeliveryDate?: Date;

  @property({
    type: 'date',
    name: 'expected_delivery_date',
  })
  expectedDeliveryDate?: Date;

  @property({
    type: 'date',
    name: 'actual_delivery_date',
  })
  actualDeliveryDate?: Date;

  @property({
    type: 'string',
    name: 'tracking_number',
  })
  trackingNumber?: string;

  @property({
    type: 'string',
    name: 'tracking_url',
  })
  trackingUrl?: string;

  @property({
    type: 'string',
    name: 'shipping_carrier',
  })
  shippingCarrier?: string;

  @property({
    type: 'string',
    name: 'shipping_method_type',
    jsonSchema: {
      enum: Object.values(ShippingMethodType),
    },
  })
  shippingMethodType?: string;

  @property({
    type: 'string',
    name: 'shipping_notes',
  })
  shippingNotes?: string;

  @property({
    type: 'number',
    name: 'shipping_partner_id',
  })
  shippingPartnerId?: number;

  @property({
    type: 'string',
    name: 'partner_account_code',
  })
  partnerAccountCode?: string;

  @belongsTo(() => CartItem, {keyTo: 'id'}, {name: 'cart_item_id'})
  cartItemId?: string;

  @belongsTo(() => OrderLineItem, {keyTo: 'id'}, {name: 'order_line_item_id'})
  orderLineItemId?: string;

  @belongsTo(() => ShippingMethod, {keyTo: 'id'}, {name: 'shipping_method_id'})
  shippingMethodId?: string;

  @hasMany(() => ShippingAddress, {keyTo: 'shippingId'})
  shippingAddresses: ShippingAddress[];

  constructor(data?: Partial<Shipping>) {
    super(data);
  }
}

export interface ShippingRelations {
  cartItem?: CartItem;
  orderLineItem?: OrderLineItem;
  shippingMethod?: ShippingMethod;
  shippingAddresses?: ShippingAddress[];
}

export type ShippingWithRelations = Shipping & ShippingRelations;
