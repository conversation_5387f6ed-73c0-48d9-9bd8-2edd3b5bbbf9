import {model, property, Model} from '@loopback/repository';
import {Address} from '../address.model';

@model()
export class ShippingCalculationRequestDto extends Model {
  @property({
    type: 'string',
    required: true,
    description: 'Product variant ID to calculate shipping for',
  })
  productVariantId: string;

  @property({
    type: 'number',
    required: true,
    description: 'Quantity of the product',
  })
  quantity: number;

  @property({
    type: 'object',
    required: true,
    description: 'Customer delivery address',
  })
  address: Address;

  @property({
    type: 'string',
    required: true,
    description: 'Cart item ID',
  })
  cartItemId: string;

  constructor(data?: Partial<ShippingCalculationRequestDto>) {
    super(data);
  }
}

@model()
export class BulkShippingCalculationRequestDto extends Model {
  @property({
    type: 'array',
    itemType: 'string',
    required: true,
    description: 'Array of cart item IDs to calculate shipping for',
  })
  cartItemIds: string[];

  @property({
    type: 'string',
    required: true,
    description: 'Shipping address ID',
  })
  shippingAddressId: string;

  constructor(data?: Partial<BulkShippingCalculationRequestDto>) {
    super(data);
  }
}

@model()
export class ShippingCalculationResponseDto extends Model {
  @property({
    type: 'string',
    description: 'Shipping record ID',
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    description: 'Seller ID',
  })
  sellerId: string;

  @property({
    type: 'number',
    required: true,
    description: 'Calculated shipping cost',
  })
  shippingCost: number;

  @property({
    type: 'string',
    required: true,
    description: 'Shipping status',
  })
  status: string;

  @property({
    type: 'date',
    description: 'Minimum delivery date',
  })
  minDeliveryDate?: Date;

  @property({
    type: 'date',
    description: 'Maximum delivery date',
  })
  maxDeliveryDate?: Date;

  @property({
    type: 'date',
    description: 'Expected delivery date',
  })
  expectedDeliveryDate?: Date;

  @property({
    type: 'string',
    description: 'Shipping method ID',
  })
  shippingMethodId?: string;

  @property({
    type: 'string',
    description: 'Cart item ID',
  })
  cartItemId?: string;

  constructor(data?: Partial<ShippingCalculationResponseDto>) {
    super(data);
  }
}

@model()
export class BulkShippingCalculationResponseDto extends Model {
  @property({
    type: 'number',
    required: true,
    description: 'Total shipping cost for all items',
  })
  totalShippingCost: number;

  @property({
    type: 'array',
    itemType: 'object',
    description: 'Individual shipping calculations for each item',
  })
  shippingDetails?: ShippingCalculationResponseDto[];

  constructor(data?: Partial<BulkShippingCalculationResponseDto>) {
    super(data);
  }
}

@model()
export class ServiceabilityCheckRequestDto extends Model {
  @property({
    type: 'string',
    required: true,
  })
  zipCode: string;

  @property({
    type: 'string',
    required: true,
    description: 'Product variant ID to check serviceability for',
  })
  productVariantId: string;

  constructor(data?: Partial<ServiceabilityCheckRequestDto>) {
    super(data);
  }
}

@model()
export class ServiceabilityCheckResponseDto extends Model {
  @property({
    type: 'boolean',
    required: true,
    description: 'Whether ECOMDUKES shipping is serviceable for this address',
  })
  isServiceable: boolean;

  @property({
    type: 'string',
    description: 'Additional information about serviceability',
  })
  message?: string;

  constructor(data?: Partial<ServiceabilityCheckResponseDto>) {
    super(data);
  }
}

@model()
export class PincodeShippingCalculationRequestDto extends Model {
  @property({
    type: 'string',
    required: true,
    description: 'Product variant ID to calculate shipping for',
  })
  productVariantId: string;

  @property({
    type: 'string',
    required: true,
    description: 'Delivery pincode',
  })
  pincode: string;

  constructor(data?: Partial<PincodeShippingCalculationRequestDto>) {
    super(data);
  }
}

@model()
export class PincodeShippingCalculationResponseDto extends Model {
  @property({
    type: 'number',
    required: true,
    description: 'Calculated shipping cost',
  })
  shippingCost: number;

  @property({
    type: 'date',
    required: true,
    description: 'Minimum delivery date',
  })
  minDeliveryDate: Date;

  @property({
    type: 'date',
    required: true,
    description: 'Maximum delivery date',
  })
  maxDeliveryDate: Date;

  @property({
    type: 'date',
    required: true,
    description: 'Expected delivery date',
  })
  expectedDeliveryDate: Date;

  @property({
    type: 'string',
    required: true,
    description: 'Shipping type - ECOMDUKES or SELF_SHIPPING',
    jsonSchema: {
      enum: ['ECOMDUKES', 'SELF_SHIPPING'],
    },
  })
  shippingType: 'ECOMDUKES' | 'SELF_SHIPPING';

  @property({
    type: 'boolean',
    required: true,
    description: 'Whether shipping is serviceable for this pincode',
  })
  isServiceable: boolean;

  @property({
    type: 'string',
    description: 'Additional information about shipping',
  })
  message?: string;

  constructor(data?: Partial<PincodeShippingCalculationResponseDto>) {
    super(data);
  }
}
