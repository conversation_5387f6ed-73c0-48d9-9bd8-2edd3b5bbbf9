import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Shipping} from './shipping.model';

@model({name: 'shipping_addresses'})
export class ShippingAddress extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'address_line1',
  })
  addressLine1: string;

  @property({
    type: 'string',
    name: 'address_line2',
  })
  addressLine2?: string;

  @property({
    type: 'string',
    required: true,
  })
  city: string;

  @property({
    type: 'string',
    required: true,
  })
  state: string;

  @property({
    type: 'string',
    required: true,
    name: 'zip_code',
  })
  zipCode: string;

  @property({
    type: 'string',
    required: true,
  })
  country: string;

  @property({
    type: 'string',
    name: 'customer_id',
  })
  customerId?: string;

  // New fields
  @property({
    type: 'string',
    required: true,
  })
  locality: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
    name: 'phone_number',
  })
  phoneNumber: string;

  @property({
    type: 'string',
  })
  landmark?: string;

  @property({
    type: 'string',
    name: 'alternative_phone_number',
  })
  alternativePhoneNumber?: string;

  @property({
    type: 'string',
    required: true,
    name: 'address_type',
    jsonSchema: {
      enum: ['PICKUP', 'DROP'],
    },
  })
  addressType: 'PICKUP' | 'DROP';

  @belongsTo(() => Shipping, {keyTo: 'id'}, {name: 'shipping_id'})
  shippingId: string;

  constructor(data?: Partial<ShippingAddress>) {
    super(data);
  }
}

export interface ShippingAddressRelations {
  // describe navigational properties here
}

export type ShippingAddressWithRelations = ShippingAddress &
  ShippingAddressRelations;
