import {Entity, model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {EcomdukeserviceRequest} from './ecomdukeserice-request.model';

@model({name: 'ecomduke_request_notes'})
export class EcomdukeRequestNotes extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @belongsTo(
    () => EcomdukeserviceRequest,
    {keyTo: 'id'},
    {name: 'ecomdukeservice_requests_id'},
  )
  ecomdukeserviceRequestId: string;

  @property({
    type: 'string',
    required: true,
  })
  notes: string;

  @property({
    type: 'string',
    name: 'previous_status',
  })
  previousStatus?: string;

  @property({
    type: 'string',
    name: 'changed_status',
  })
  changedStatus?: string;
  constructor(data?: Partial<EcomdukeRequestNotes>) {
    super(data);
  }
}

export interface EcomdukeRequestNotesRelations {
  // describe navigational properties here
}

export type EcomdukeRequestNotesWithRelations = EcomdukeRequestNotes &
  EcomdukeRequestNotesRelations;
