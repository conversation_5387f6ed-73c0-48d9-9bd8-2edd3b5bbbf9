/* eslint-disable @typescript-eslint/naming-convention */
import {post, requestBody} from '@loopback/openapi-v3';
import {
  CONTENT_TYPE,
  rateLimitKeyGenPublic,
  STATUS_CODE,
} from '@sourceloop/core';
import {authorize} from 'loopback4-authorization';
import {ratelimit} from 'loopback4-ratelimiter';
import {CashfreeWebhookService, ClickPostWebhookService} from '../services';
import {service} from '@loopback/core';
import {AnyObject} from '@loopback/repository';
import {ClickPostWebhookPayload} from '../services/clickpost-webhook.service';
const basePath = '/webhooks';
export class WebhookController {
  constructor(
    @service(CashfreeWebhookService)
    private cashfreeWebhookService: CashfreeWebhookService,
    @service(ClickPostWebhookService)
    private clickPostWebhookService: ClickPostWebhookService,
  ) {}

  @ratelimit(true, {
    max: parseInt(process.env.PUBLIC_API_MAX_ATTEMPTS ?? '5'),
    keyGenerator: rateLimitKeyGenPublic,
  })
  @authorize({permissions: ['*']})
  // @intercept('interceptors.CashfreeWebhookInterceptor')
  @post(`${basePath}/payment`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Webhook Success response',
      },
    },
  })
  async webhook(
    @requestBody({
      description: 'Webhook request body',
      required: true,
      content: {
        [CONTENT_TYPE.JSON]: {
          'x-parser': 'raw',
          schema: {type: 'object'},
        },
      },
    })
    event: AnyObject,
  ): Promise<void> {
    await this.cashfreeWebhookService.handlePaymentWebhook(event);
  }

  @ratelimit(true, {
    max: parseInt(process.env.PUBLIC_API_MAX_ATTEMPTS ?? '5'),
    keyGenerator: rateLimitKeyGenPublic,
  })
  @authorize({permissions: ['*']})
  // @intercept('interceptors.CashfreeWebhookInterceptor')
  @post(`${basePath}/payment-link`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Webhook Success response',
      },
    },
  })
  async webhookLink(
    @requestBody({
      description: 'Webhook request body',
      required: true,
      content: {
        [CONTENT_TYPE.JSON]: {
          'x-parser': 'raw',
          schema: {type: 'object'},
        },
      },
    })
    event: AnyObject,
  ): Promise<void> {
    await this.cashfreeWebhookService.handlePaymentWebhook(event);
  }

  @ratelimit(true, {
    max: parseInt(process.env.PUBLIC_API_MAX_ATTEMPTS ?? '5'),
    keyGenerator: rateLimitKeyGenPublic,
  })
  @authorize({permissions: ['*']})
  @post(`${basePath}/clickpost/shipment-tracking`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'ClickPost Shipment Tracking Webhook Success response',
      },
    },
  })
  async clickPostShipmentTracking(
    @requestBody({
      description: 'ClickPost shipment tracking webhook payload',
      required: true,
      content: {
        [CONTENT_TYPE.JSON]: {
          'x-parser': 'raw',
          schema: {
            type: 'object',
            properties: {
              additional: {
                type: 'object',
                properties: {
                  latest_status: {
                    type: 'object',
                    properties: {
                      remark: {type: 'string'},
                      clickpost_status_code: {type: 'number'},
                      reference_number: {type: 'string'},
                      timestamp: {type: 'string'},
                      clickpost_status_bucket_description: {type: 'string'},
                      location: {type: 'string'},
                      clickpost_status_description: {type: 'string'},
                      clickpost_status_bucket: {type: 'number'},
                      status: {type: 'string'},
                    },
                  },
                  is_rvp: {type: 'boolean'},
                  courier_partner_edd: {type: 'string'},
                  order_id: {type: 'string'},
                },
              },
              remark: {type: 'string'},
              clickpost_status_description: {type: 'string'},
              timestamp: {type: 'string'},
              location: {type: 'string'},
              status: {type: 'string'},
              cp_id: {type: 'number'},
              clickpost_status_code: {type: 'number'},
              waybill: {type: 'string'},
            },
            required: ['waybill', 'status'],
          },
        },
      },
    })
    payload: AnyObject,
  ): Promise<void> {
    await this.clickPostWebhookService.handleShipmentTrackingWebhook(
      payload as ClickPostWebhookPayload,
    );
  }
}
