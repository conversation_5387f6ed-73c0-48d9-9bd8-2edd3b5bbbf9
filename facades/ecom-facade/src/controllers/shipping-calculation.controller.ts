/* eslint-disable @typescript-eslint/naming-convention */
import {post, param, get, getModelSchemaRef, requestBody} from '@loopback/rest';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {service} from '@loopback/core';
import {ShippingCalculationHelperService} from '../services';
import {
  ShippingCalculationRequestDto,
  BulkShippingCalculationRequestDto,
  ShippingCalculationResponseDto,
  BulkShippingCalculationResponseDto,
  ServiceabilityCheckRequestDto,
  ServiceabilityCheckResponseDto,
  PincodeShippingCalculationRequestDto,
  PincodeShippingCalculationResponseDto,
} from '../models/ecom-service/dto';
import {CartItem, Shipping} from '../models';
import {ModifiedRestService, restService} from '@sourceloop/core';

const basePath = '/shipping-calculations';

export class ShippingCalculationController {
  constructor(
    @service(ShippingCalculationHelperService)
    private readonly shippingCalculationService: ShippingCalculationHelperService,
    @restService(CartItem)
    private readonly cartItemProxy: ModifiedRestService<CartItem>,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCart]})
  @post(`${basePath}/calculate`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Calculate shipping for a single product variant',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(ShippingCalculationResponseDto),
          },
        },
      },
    },
  })
  async calculateShipping(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ShippingCalculationRequestDto, {
            title: 'ShippingCalculationRequest',
          }),
        },
      },
    })
    request: ShippingCalculationRequestDto,
  ): Promise<ShippingCalculationResponseDto> {
    const shipping =
      await this.shippingCalculationService.calculateShippingDetails(
        request.productVariantId,
        request.quantity,
        request.address,
        request.cartItemId,
      );

    return new ShippingCalculationResponseDto({
      id: shipping.id,
      sellerId: shipping.sellerId,
      shippingCost: shipping.shippingCost,
      status: shipping.status,
      minDeliveryDate: shipping.minDeliveryDate,
      maxDeliveryDate: shipping.maxDeliveryDate,
      expectedDeliveryDate: shipping.expectedDeliveryDate,
      shippingMethodId: shipping.shippingMethodId,
      cartItemId: shipping.cartItemId,
    });
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCart]})
  @post(`${basePath}/calculate-bulk`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Calculate shipping for multiple cart items',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(BulkShippingCalculationResponseDto),
          },
        },
      },
    },
  })
  async calculateBulkShipping(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(BulkShippingCalculationRequestDto, {
            title: 'BulkShippingCalculationRequest',
          }),
        },
      },
    })
    request: BulkShippingCalculationRequestDto,
  ): Promise<Shipping[]> {
    // Get cart items from the provided IDs
    const cartItems = await this.cartItemProxy.find({
      where: {id: {inq: request.cartItemIds}},
    });

    const totalShipping =
      await this.shippingCalculationService.calculateAndCreateShippings(
        cartItems,
        request.shippingAddressId,
        true,
      );

    return totalShipping;
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCart]})
  @post(`${basePath}/serviceability-check`, {
    responses: {
      [STATUS_CODE.OK]: {
        description:
          'Check if ECOMDUKES shipping is serviceable for an address',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(ServiceabilityCheckResponseDto),
          },
        },
      },
    },
  })
  async checkServiceability(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ServiceabilityCheckRequestDto, {
            title: 'ServiceabilityCheckRequest',
          }),
        },
      },
    })
    request: ServiceabilityCheckRequestDto,
  ): Promise<ServiceabilityCheckResponseDto> {
    const isServiceable =
      await this.shippingCalculationService.isEcomShippingServicable(
        request.zipCode,
        request.productVariantId,
      );

    return new ServiceabilityCheckResponseDto({
      isServiceable: isServiceable !== null,
      message: isServiceable
        ? 'ECOMDUKES shipping is available for this address'
        : 'ECOMDUKES shipping is not available for this address',
    });
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCart]})
  @post(`${basePath}/calculate-by-pincode`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Calculate shipping for a product variant by pincode',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(PincodeShippingCalculationResponseDto),
          },
        },
      },
    },
  })
  async calculateShippingByPincode(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(PincodeShippingCalculationRequestDto, {
            title: 'PincodeShippingCalculationRequest',
          }),
        },
      },
    })
    request: PincodeShippingCalculationRequestDto,
  ): Promise<PincodeShippingCalculationResponseDto> {
    const shippingDetails =
      await this.shippingCalculationService.calculateShippingByPincode(
        request.productVariantId,
        request.pincode,
      );

    return new PincodeShippingCalculationResponseDto({
      shippingCost: shippingDetails.shippingCost,
      minDeliveryDate: shippingDetails.minDeliveryDate,
      maxDeliveryDate: shippingDetails.maxDeliveryDate,
      expectedDeliveryDate: shippingDetails.expectedDeliveryDate,
      shippingType: shippingDetails.shippingType,
      isServiceable: shippingDetails.isServiceable,
      message: shippingDetails.message,
    });
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCart]})
  @get(`${basePath}/warehouse/{productVariantId}`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Get warehouse information for a product variant',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              properties: {
                warehouseId: {type: 'string'},
                warehouseName: {type: 'string'},
                state: {type: 'string'},
                city: {type: 'string'},
                stockOnHand: {type: 'number'},
              },
            },
          },
        },
      },
    },
  })
  async getWarehouseByInventory(
    @param.path.string('productVariantId') productVariantId: string,
  ): Promise<object> {
    const warehouse =
      await this.shippingCalculationService.getWarehouseByInventory(
        productVariantId,
      );

    if (!warehouse) {
      return {
        message:
          'No warehouse found with available stock for this product variant',
      };
    }

    return {
      warehouseId: warehouse.id,
      warehouseName: warehouse.name,
      state: warehouse.state,
      city: warehouse.city,
    };
  }
}
