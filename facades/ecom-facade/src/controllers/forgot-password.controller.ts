// src/controllers/forget-password.controller.ts
import {inject, service} from '@loopback/core';
import {HttpErrors, param, patch, post, requestBody} from '@loopback/rest';
import {
  ErrorCodes,
  ILogger,
  LOGGER,
  OPERATION_SECURITY_SPEC,
  restService,
  STATUS_CODE,
} from '@sourceloop/core';
import {authorize} from 'loopback4-authorization';
import {
  AuthUser,
  ForgetPasswordDto,
  ResetPasswordWithClient,
} from '@sourceloop/authentication-service';
import {NotificationHelperService} from '../services';
import {AuthProxyType} from '../datasources/configs';

export class ForgetPasswordController {
  constructor(
    @inject(LOGGER.LOGGER_INJECT) public logger: ILogger,
    @restService(AuthUser)
    private readonly authProvider: AuthProxyType,
    @service(NotificationHelperService)
    private notificationHelperService: NotificationHelperService,
  ) {}

  @authorize({permissions: ['*']})
  @post(`auth/forget-password`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Success Response.',
      },
      ...ErrorCodes,
    },
  })
  async forgetPassword(
    @requestBody()
    req: ForgetPasswordDto,
    @param.header.string('x-origin') xOrigin: string,
  ): Promise<void> {
    try {
      const response = await this.authProvider.forgetPassword(req, xOrigin);

      let redirectUrl;
      switch (xOrigin) {
        case 'ecomdukes-seller':
          redirectUrl = `${process.env.SELLER_WEB_URL}/reset-password?code=${response.code}`;
          break;
        case 'ecomdukes-admin':
          redirectUrl = `${process.env.ADMIN_WEB_URL}/reset-password?code=${response.code}`;
          break;
        case 'ecomdukes-customer':
          redirectUrl = `${process.env.CUSTOMER_WEB_URL}/reset-password?code=${response.code}`;
          break;
        default:
          throw new HttpErrors.BadRequest('Invalid client origin');
      }

      // Prepare email data
      const emailData = {
        productName: 'Ecomdukes',
        supportId: '<EMAIL>',
        brand: 'Ecomdukes',
        redirectUrl,
      };

      // Send password reset email
      await this.notificationHelperService.sendEmail(
        'reset-password-email.hbs',
        'Reset Your Password',
        emailData,
        req.username,
        // `${req.user?.firstName} ${req.user?.lastName}`,
        '',
      );
    } catch (error) {
      this.logger.error('Forget password request failed', error);
      throw error;
    }
  }

  @authorize({permissions: ['*']})
  @patch(`auth/reset-password`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'If User password successfully changed.',
      },
      ...ErrorCodes,
    },
  })
  async resetPassword(
    @requestBody()
    req: ResetPasswordWithClient,
  ): Promise<void> {
    try {
      await this.authProvider.resetPassword(req);

      // Send notifications in background (non-blocking)
      this.sendPasswordResetNotifications().catch((error: unknown) => {
        console.error('Error sending password reset notifications:', error);
        // Don't throw error to avoid breaking the password reset flow
      });
    } catch (error) {
      this.logger.error('Password reset failed', error);
      throw error;
    }
  }

  private async sendPasswordResetNotifications(): Promise<void> {
    try {
      // Get current date and time
      const now = new Date();
      const changeDate = now.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      const changeTime = now.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        timeZoneName: 'short',
      });

      // Prepare email template data
      const emailData = {
        productName: 'Ecomdukes',
        supportId: '<EMAIL>',
        brand: 'Ecomdukes',
        changeDate,
        changeTime,
      };

      // Send password reset confirmation email
      await this.notificationHelperService.sendEmail(
        'password-changed-email.hbs',
        'Password Reset Successfully - Ecomdukes',
        emailData,
        '<EMAIL>', // Generic for now since we don't have user info from token
        'User',
      );

      // Send SMS notification
      const smsMessage = `Your Ecomdukes account password has been successfully reset on ${changeDate} at ${changeTime}. If you didn't make this change, please contact support <NAME_EMAIL> - Team Ecomdukes`;

      await this.notificationHelperService.sendSMS(
        smsMessage,
        'user-phone', // Generic for now since we don't have user info from token
        'User',
      );
    } catch (error) {
      console.error('Error in sendPasswordResetNotifications:', error);
      // Don't throw error to avoid breaking the main flow
    }
  }
}
