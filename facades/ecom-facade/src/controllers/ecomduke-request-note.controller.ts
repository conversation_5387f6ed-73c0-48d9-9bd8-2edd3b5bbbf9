import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
  repository,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {
  CONTENT_TYPE,
  ModifiedRestService,
  restService,
  STATUS_CODE,
} from '@sourceloop/core';

import {PermissionKeys} from '@local/core';
import {EcomdukeRequestNotes} from '../models/ecom-service/emoduke-request-note.model';

const basePath = '/ecomduke-request-notes';

export class EcomdukeRequestNoteController {
  constructor(
    @restService(EcomdukeRequestNotes)
    private readonly ecomdukeRequestNotesRepository: ModifiedRestService<EcomdukeRequestNotes>,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateServiceRequest]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'EcomdukeRequestNotes model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(EcomdukeRequestNotes),
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(EcomdukeRequestNotes, {
            title: 'NewEcomdukeRequestNote',
            exclude: ['id'],
          }),
        },
      },
    })
    ecomdukeRequestNotes: Omit<EcomdukeRequestNotes, 'id'>,
  ): Promise<EcomdukeRequestNotes> {
    return this.ecomdukeRequestNotesRepository.create(ecomdukeRequestNotes);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewServiceRequest]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'EcomdukeRequestNotes count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(EcomdukeRequestNotes) where?: Where<EcomdukeRequestNotes>,
  ): Promise<Count> {
    return this.ecomdukeRequestNotesRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewServiceRequest]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of EcomdukeRequestNotes instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EcomdukeRequestNotes, {
            includeRelations: true,
          }),
        },
      },
    },
  })
  async find(
    @param.filter(EcomdukeRequestNotes) filter?: Filter<EcomdukeRequestNotes>,
  ): Promise<EcomdukeRequestNotes[]> {
    return this.ecomdukeRequestNotesRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewServiceRequest]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'EcomdukeRequestNotes model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(EcomdukeRequestNotes, {
          includeRelations: true,
        }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(EcomdukeRequestNotes, {exclude: 'where'})
    filter?: FilterExcludingWhere<EcomdukeRequestNotes>,
  ): Promise<EcomdukeRequestNotes> {
    return this.ecomdukeRequestNotesRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateServiceRequest]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'EcomdukeRequestNotes PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(EcomdukeRequestNotes, {partial: true}),
        },
      },
    })
    ecomdukeRequestNotes: Partial<EcomdukeRequestNotes>,
  ): Promise<void> {
    await this.ecomdukeRequestNotesRepository.updateById(
      id,
      ecomdukeRequestNotes,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateServiceRequest]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'EcomdukeRequestNotes PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody()
    ecomdukeRequestNotes: EcomdukeRequestNotes,
  ): Promise<void> {
    await this.ecomdukeRequestNotesRepository.replaceById(
      id,
      ecomdukeRequestNotes,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteServiceRequest]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'EcomdukeRequestNotes DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.ecomdukeRequestNotesRepository.deleteById(id);
  }
}
